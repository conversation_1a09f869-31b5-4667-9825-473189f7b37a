package it.netservice.csp.backend.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import it.netservice.csp.backend.service.monitoraggio.MonitoraggioService;

public class ExcelGenerator {
    private Sheet sheet;
    private XSSFWorkbook workbook;
    private int rowNumber;
    private Map<Integer, Integer> columnsSize = new HashMap<>();
    private static final org.apache.log4j.Logger LOGGER = Logger.getLogger(MonitoraggioService.class);
    private XSSFFont font;
    private CellStyle cellStyle;
    private final Short resizeColumnFactor = 20;

    public ExcelGenerator(Sheet sheet, XSSFWorkbook workbook, int initialRow) {
        this.sheet = sheet;
        this.workbook = workbook;
        this.rowNumber = initialRow;
    }

    public void generateSimpleRow(String... contents) {
        Row row = sheet.createRow(rowNumber);
        Integer currentColumn = 0;
        for (String value : contents) {
            setColumnSize(value, currentColumn);
            Cell cell = row.createCell(currentColumn);
            cell.setCellValue(value);
            cell.setCellStyle(this.cellStyle);
            currentColumn++;
        }
        rowNumber++;
    }

    @SafeVarargs
    public final void generateTableFromColumns(int rows, short rowHeigth, List<String>... columns) {
        List<Row> rowList = createRowFromNumber(rows, rowHeigth);
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < columns.length; j++) {
                setColumnSize(columns[j].get(i), j);
                Cell cell = rowList.get(i).createCell(j);
                cell.setCellValue(columns[j].get(i));
                cell.setCellStyle(this.cellStyle);
            }
        }
    }

    @SafeVarargs
    public final void generateTableFromRows(int columns, short rowHeight, List<String>... rows) {
        List<Row> rowList = createRowFromNumber(rows.length, rowHeight);
        for (int i = 0; i < rows.length; i++) {
            for (int j = 0; j < columns; j++) {
                setColumnSize(rows[i].get(j), j);
                Cell cell = rowList.get(i).createCell(j);
                cell.setCellValue(rows[i].get(j));
                cell.setCellStyle(this.cellStyle);
            }
        }
    }

    private void setColumnSize(String s, Integer columnIndex) {
        if (s.contains("\n"))
            s = s.split("\n")[0].length() > s.split("\n")[1].length() ? s.split("\n")[0] : s.split("\n")[1];
        Integer newColumnSize = s.length() * this.font.getFontHeightInPoints() * resizeColumnFactor;
        if (columnsSize.get(columnIndex) == null) {
            columnsSize.put(columnIndex, newColumnSize);
            sheet.setColumnWidth(columnIndex, columnsSize.get(columnIndex));
            return;
        }
        if (newColumnSize > columnsSize.get(columnIndex)) {
            columnsSize.remove(columnIndex);
            columnsSize.put(columnIndex, newColumnSize);
            sheet.setColumnWidth(columnIndex, columnsSize.get(columnIndex));
        }
    }

    public void generateSpacer(int spacerSize) {
        for (int i = 0; i < spacerSize; i++) {
            sheet.createRow(rowNumber).createCell(0);
        }
        rowNumber++;
    }

    private List<Row> createRowFromNumber(int listNumber, short height) {
        List<Row> rowList = new ArrayList<>();
        for (int i = 0; i < listNumber; i++) {
            Row row = sheet.createRow(rowNumber);
            row.setHeight(height);
            rowList.add(row);
            rowNumber++;
        }
        return rowList;
    }

    public void setFont(String fontFamily, short color, short fontSize, boolean bold) {
        XSSFFont font = workbook.createFont();
        font.setFontName(fontFamily);
        font.setColor(color);
        font.setFontHeightInPoints((short) fontSize);
        font.setBold(bold);
        this.font = font;
    }

    public void createCellStyle(short fgColor, short fillPattern, short... borders) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setWrapText(true);
        cellStyle.setFillForegroundColor(fgColor);
        cellStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellStyle.setFillPattern(fillPattern);
        applyBorder(cellStyle, borders);
        if (this.font != null)
            cellStyle.setFont(font);
        this.cellStyle = cellStyle;
    }

    private void applyBorder(CellStyle cellStyle, short[] borders) {
        if (borders.length != 1 && borders.length != 4)
            return;
        if (borders.length == 1) {
            cellStyle.setBorderLeft(borders[0]);
            cellStyle.setBorderTop(borders[0]);
            cellStyle.setBorderRight(borders[0]);
            cellStyle.setBorderBottom(borders[0]);
            return;
        }
        for (short b : borders) {
            cellStyle.setBorderLeft(b);
            cellStyle.setBorderTop(b);
            cellStyle.setBorderRight(b);
            cellStyle.setBorderBottom(b);
        }
    }

    public void setDefaultStyle() {
        this.cellStyle = workbook.createCellStyle();
    }

    public Sheet getSheet() {
        return sheet;
    }

    public void setSheet(Sheet sheet) {
        this.sheet = sheet;
    }

    public XSSFWorkbook getWorkbook() {
        return workbook;
    }

    public void setWorkbook(XSSFWorkbook workbook) {
        this.workbook = workbook;
    }

    public int getRowNumber() {
        return rowNumber;
    }

    public void setRowNumber(int rowNumber) {
        this.rowNumber = rowNumber;
    }
}
