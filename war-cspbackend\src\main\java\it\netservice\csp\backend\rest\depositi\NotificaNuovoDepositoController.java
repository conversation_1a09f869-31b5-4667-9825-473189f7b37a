package it.netservice.csp.backend.rest.depositi;

import java.io.IOException;
import java.net.MalformedURLException;
import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.security.RolesAllowed;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;
import javax.persistence.NoResultException;
import javax.persistence.Query;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.Response;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.rpc.ServiceException;

import okhttp3.Credentials;
import okhttp3.OkHttpClient;
import okhttp3.OkHttpClient.Builder;
import okhttp3.Request;

import org.apache.log4j.Logger;
import org.xml.sax.SAXException;

import it.netservice.adnclient.AdnUser;
import it.netservice.csp.backend.common.AdnHandler;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.DepositoException;
import it.netservice.csp.backend.ext.service.ServiceBuilder;
import it.netservice.csp.backend.rest.AbstractCspBackendController;
import it.netservice.csp.backend.rest.auth.NoSicAuthService;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.csp.backend.sic.service.repository.SicRepositoryException;
import it.netservice.csp.backend.ws.depositi.ContentSummaryType;
import it.netservice.csp.backend.ws.depositi.EnvSummaryType;
import it.netservice.csp.backend.ws.depositi.EventType;
import it.netservice.penale.model.common.Soggetto;
import it.netservice.penale.model.sic.Deposito;
import it.netservice.penale.model.sic.RicercaSentenza;
import it.netservice.penale.model.sic.RicorsoSic;

/**
 * * Servizi di ricerca ricorsi per il desk del magistrato civile. * * <AUTHOR>
 */
@NoSicAuthService
@Path("/notificaNuovoDeposito")
@RolesAllowed("GLCASS")
public class NotificaNuovoDepositoController extends AbstractCspBackendController {

    private static final Logger LOGGER = Logger.getLogger(NotificaNuovoDepositoController.class);
    @Inject
    private ServiceBuilder serviceBuilder;
    @Inject
    private AdnHandler adnHandler;

    private SicPenaleRepository repo;

    /**
     * * Metodo invocato dal GL-CASS in caso di nuova busta validata * * @param context * @param ref * ref del deposito Inoltre se avra una
     * notifica qui, significa che si tratta di un deposito telematico inviato da GL-cass, quindi aggiorniamo anche lo stato della sentenza
     * (idbusta-numero-anno) * @return
     */
    @GET
    public Response nuovoDeposito(@Context HttpServletRequest context, @QueryParam("idCat") Long idCat)
            throws CspBackendException, ServiceException, SicRepositoryException, IOException, ParserConfigurationException, SAXException {
        LOGGER.info("Notifica nuovo deposito in arrivo. idCat = " + idCat);
        if (idCat == null) {
            throw new CspBackendException("Riferimento deposito non corretto. idCat null", RestErrorCodeEnum.IDCAT_DEPOSITO_NULL, 433);
        }
        EntityManager entityManager = emfCaricamento.createEntityManager();
        repo = new SicPenaleRepository(entityManager);
        EntityTransaction transaction = entityManager.getTransaction();
        transaction.begin();
        try {
            checkIfExisting(entityManager, idCat);
            Deposito deposito = new Deposito();
            deposito.setIdCat(idCat);
            deposito.setStato(Deposito.Stato.NEW);
            populateDepositoGL(deposito);
            populateDepositoSIC(entityManager, deposito);
            // LOGGING NRG/ID_UDIEN - Before persist in nuovoDeposito
            LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] nuovoDeposito - Prima di persist. IDCAT: %d, Deposito.nrg: %s",
                    deposito.getIdCat(), deposito.getNrg()));
            if (deposito.getRicorso() != null) {
                LOGGER.info(String.format(
                        "[LOGGING NRG/ID_UDIEN] nuovoDeposito - Prima di persist. IDCAT: %d, Deposito.getRicorso().getNrg(): %s",
                        deposito.getIdCat(), deposito.getRicorso().getNrg()));
            } else {
                LOGGER.info(
                        String.format("[LOGGING NRG/ID_UDIEN] nuovoDeposito - Prima di persist. IDCAT: %d, Deposito.getRicorso() is NULL.",
                                deposito.getIdCat()));
            }
            entityManager.persist(deposito);
            populateSentenzaTelematica(entityManager, idCat);
            transaction.commit();
            LOGGER.info("Nuovo deposito inserito correttamente.");
            return Response.ok().build();
        } catch (Throwable e) {
            if (transaction.isActive()) {
                transaction.rollback();
            }
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la creazione del deposito. idCat:" + idCat);
            throw new DepositoException("Errore durante la creazione del deposito", RestErrorCodeEnum.DEPOSITO_GENERIC_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    /**
     * Aggiornata la penale T_SENTENZA valorizzando i campi deposito_telematico e pubblicato_telematico a 1 per indicare che si tratta di
     * depositi telematici e non pubblicati o depositati cartaceamenti dal sic
     *
     * @param entityManager
     *            entity per eseguire la transazione
     * @param idCat
     *            id del deposito che poi anfare in join con penale_provvedimento o cspbackend_depositi
     */
    private void populateSentenzaTelematica(EntityManager entityManager, Long idCat) throws SicRepositoryException {
        RicercaSentenza ricercaSentenza = repo.getRicercaSentenzaByIdCatAndNrg(idCat);

        if (ricercaSentenza.getDataMinuta() == null || ricercaSentenza.getDepositoTelamatico() == 1) {
            Query query = entityManager
                    .createNativeQuery("update PENALE_T_SENTENZA set PUBBLICATO_TELEMATICO = 1, DEPOSITO_TELEMATICO = 1 where ID_SENT="
                            + ricercaSentenza.getId());
            query.executeUpdate();
        }
    }

    /**
     * Controllo che il deposito non sia già presenete nel csc
     *
     * @param entityManager
     * @param idCat
     */
    private void checkIfExisting(EntityManager entityManager, Long idCat) throws CspBackendException {
        try {
            /** TODO */
            /*
             * CriteriaBuilderHelper<DepositoView> criteriaBuilder = new CriteriaBuilderHelper<>(DepositoView.class);
             * DepositiViewCriteriaParameters param = new DepositiViewCriteriaParameters(); param.setId(idCat); DepositoView deposito =
             * criteriaBuilder.uniqueResult(entityManager, param); if (deposito.getStato() != Deposito.Stato.NEW) { throw new
             * CspBackendException("Notifica nuovo deposito su deposito già accettato/rifiutato"); }
             */
        } catch (NoResultException e) {
            LOGGER.info("Il deposito è nuovo.");
        }
    }

    /**
     * * Popolamento dell'oggetto deposito con i dati presenti nel GL
     * <p>
     * *
     *
     * @param deposito
     *            * @throws RemoteException *
     * @throws MalformedURLException
     *             * @throws ServiceException
     */
    private void populateDepositoGL(Deposito deposito)
            throws ServiceException, IOException, SAXException, ParserConfigurationException, CspBackendException {
        EnvSummaryType[] events = serviceBuilder.getLogEsitiService().querySummary(new String[] { String.valueOf(deposito.getIdCat()) })
                .getEnvSummary();
        if (events == null || events.length == 0) {
            throw new CspBackendException("Deposito non trovato sul GL. IDDeposito: " + deposito.getIdCat(),
                    RestErrorCodeEnum.GL_DEPOSITO_NOT_FOUND, 433);
        }
        EnvSummaryType envSummaryType = events[0];
        deposito.setDataDeposito(fixTime(envSummaryType.getReceived()).getTimeInMillis());
        deposito.setRefId(envSummaryType.getRefId());
        List<String> idCats = checkContenutiDeposito(deposito, envSummaryType);
        checkAnomalie(deposito, idCats);
        checkDepositante(deposito, envSummaryType);
    }

    private List<String> checkContenutiDeposito(Deposito deposito, EnvSummaryType envSummaryType)
            throws IOException, ParserConfigurationException, SAXException {
        DatiAttoParserMinimal datiAttoParser = new DatiAttoParserMinimal(serviceBuilder);
        List<String> idCats = new ArrayList<>();
        idCats.add(String.valueOf(deposito.getIdCat()));
        if (envSummaryType.getContents().getContentSummary() != null) {
            for (ContentSummaryType contentSummary : envSummaryType.getContents().getContentSummary()) {
                idCats.add(contentSummary.getCatId());
                if ("DA".equals(contentSummary.getDocType())) {
                    Long idCatDatiAtto = Long.valueOf(contentSummary.getCatId());
                    deposito.setIdCatDatiAtto(idCatDatiAtto);
                    datiAttoParser.parse(idCatDatiAtto);
                }
            }
            deposito.setNumeroRicorso(datiAttoParser.getNumeroRicorso());
            deposito.setAnnoRicorso(datiAttoParser.getAnnoRicorso());
            deposito.setIntroduttivo(datiAttoParser.getNumeroRicorso() == null);
            deposito.setTipo(datiAttoParser.getTipoAtto());
            // try {
            // Atto.TipoAtto.valueOf(deposito.getTipo());
            // } catch (IllegalArgumentException | NullPointerException ex) {
            // LOGGER.warn("Tipo atto non riconosciuto: " + datiAttoParser.getTipoAtto());
            // deposito.setTipo("NonClassificabile");
            // }
        }
        return idCats;
    }

    private void checkDepositante(Deposito deposito, EnvSummaryType envSummaryType) {
        Soggetto depositante = new Soggetto(envSummaryType.getSender(), "n.d.", "n.d.");
        try {
            AdnUser adnUser = adnHandler.getUtente(envSummaryType.getSender());
            if (adnUser != null) {
                depositante = new Soggetto(adnUser.getCodiceFiscale(), adnUser.getNome(), adnUser.getCognome());
            } else {
                LOGGER.warn("Utente " + envSummaryType.getSender() + " non trovato in adnhandler");
            }
        } catch (Exception ex) {
            LOGGER.error("Impossibile recuperare le informazioni sul depositante", ex);
        }
        deposito.setSoggettoDepositante(depositante);
    }

    private void checkAnomalie(Deposito deposito, List<String> idCats) {
        try {
            deposito.setProvvedimentoPresente(Boolean.FALSE);
            int fatals = 0;
            int errors = 0;
            int warnings = 0;
            for (String idCat : idCats) {
                EventType[] result = serviceBuilder.getLogEsitiService().queryCatIdEvents(idCat).getEvent();
                if (result != null) {
                    for (EventType event : result) {
                        switch (event.getEvtClass()) {
                            case "FATAL":
                                fatals++;
                                break;
                            case "ERROR":
                                if ("Uno o piu' ricorsi avverso lo stesso provvedimento indicato nel controricorso risultano essere gia' iscritti"
                                        .equals(event.getEvtDesc())) {
                                    deposito.setProvvedimentoPresente(Boolean.TRUE);
                                }
                                errors++;
                                break;
                            case "WARN":
                                warnings++;
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
            deposito.setAnomalie(warnings + "-" + errors + "-" + fatals);
        } catch (MalformedURLException | ServiceException | RemoteException ex) {
            LOGGER.error("Errore durante il caricamento delle anomalie", ex);
            deposito.setAnomalie("n.d.");
        }
    }

    private void populateDepositoSIC(EntityManager entityManager, Deposito deposito) throws IOException {
        LOGGER.info(String.format(
                "[LOGGING NRG/ID_UDIEN] populateDepositoSIC - START - IDCAT: %d, NumeroRicorso from Deposito: %s, AnnoRicorso from Deposito: %s",
                deposito.getIdCat(), deposito.getNumeroRicorso(), deposito.getAnnoRicorso()));
        if (deposito.getNumeroRicorso() != null && deposito.getAnnoRicorso() != null) {
            // deposito in corso di causa
            LOGGER.info(String.format("Ricerca ricorso %s/%s", deposito.getNumeroRicorso(), deposito.getAnnoRicorso()));
            RicorsoSic ricorso = repo.getRicorsoByNumeroAnno(deposito.getNumeroRicorso(), deposito.getAnnoRicorso());
            if (ricorso == null) {
                LOGGER.warn(String.format(
                        "[LOGGING NRG/ID_UDIEN] populateDepositoSIC - RicorsoSic NON trovato per NumeroRicorso: %s, AnnoRicorso: %s. IDCAT: %d",
                        deposito.getNumeroRicorso(), deposito.getAnnoRicorso(), deposito.getIdCat()));
                LOGGER.warn("Ricorso indicato nel dati atto non presente in base dati."); // Original log kept for context
                return;
            }
            // RicorsoSic Trovato
            LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] populateDepositoSIC - RicorsoSic TROVATO. NRG: %s. IDCAT: %d",
                    ricorso.getNrg(), deposito.getIdCat()));
            deposito.setRicorso(ricorso);
        }
    }

    private String getCfRelatoreRicorso(Long idRicorso) throws IOException, CspBackendException {
        Request req = getRequestBuilder(getMagistratiUdienzaUrl(idRicorso)).get().build();
        Builder builder = new Builder();
        builder.readTimeout(5, TimeUnit.MINUTES);
        OkHttpClient longClient = builder.build();
        LOGGER.info("Riicerca relatore ricorso - inizio");
        try (okhttp3.Response response = longClient.newCall(req).execute()) {
            if (response.body() != null) {
                LOGGER.info("Riicerca relatore ricorso - fine");
                return response.body().string();
            }
            throw new CspBackendException("Impossibile ricavare il cf del relatore");
        }
    }

    private Request.Builder getRequestBuilder(String endpoint) {
        String cred = Credentials.basic("desk", "servizi-desk");
        String url = System.getProperty("servizi-desk.url") + "/servizi-desk/jsonapi" + endpoint;
        return new Request.Builder().url(url).addHeader("UserCode", "SGR").addHeader("Authorization", cred);
    }

    private String getNotificheUrl(String cfDestinatario, Long idRicorso, String testo) {
        return String.format("/notifiche?cfDestinatario=%s&idRicorso=%s&testo=%s&dataVisibilita=%s", cfDestinatario, idRicorso, testo,
                null);
    }

    private String getMagistratiUdienzaUrl(Long idRicorso) {
        return String.format("/MagistratoUdienza/relatore?idRicorso=%s", idRicorso);
    }

}
