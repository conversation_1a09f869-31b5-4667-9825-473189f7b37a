/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package it.netservice.csp.backend.firma;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import it.netservice.firmaremota.common.RemoteSignatureCredentials;
import it.netservice.firmaremotaclient.detached.RemoteSignatureHandler;
import it.netservice.nsfirmacommon.DocumentToSign;
import it.netservice.nsfirmacommon.SignatureCredentials;
import it.netservice.nsfirmacommon.SignatureException;
import it.netservice.nsfirmacommon.SignatureType;

/**
 *
 * <AUTHOR>
 */
public class FirmaRemota {

    private static String getErrorCodeItagile(SignatureException ex) {
        String errorCodeStartItagile = "CODICE_ERRORE_ITAGILE: ";
        for (String detail : ex.getMessage().split("\n"))
            if (detail.contains(errorCodeStartItagile)) {
                int startErrorCode = detail.indexOf(errorCodeStartItagile) + errorCodeStartItagile.length();
                if (detail.length() >= startErrorCode + 3)
                    return detail.substring(startErrorCode, startErrorCode + 3);
            }
        return null;
    }

    private static String getRestErrorCode(String itagile_error) {
        switch (itagile_error) {
            case "191":
            case "192":
            case "193":
            case "194":
            case "195":
                return "INVALID_JWT_TOKEN_SIGN_ERROR";
            case "030":
                return "USER_NOT_PRESENT_SIGN_ERROR";
            case "010":
                return "INCORRECT_PASSWORD_SIGN_ERROR";
            case "019":
                return "INCORRECT_PIN_SIGN_ERROR";
            case "027":
                return "USER_BLOCKED_SIGN_ERROR";
            default:
                return "GENERIC_ERROR";
        }
    }

    /**
     * Firma una serie di documenti con le credenziali di firma remota
     *
     * @param credentials
     * @param documentsToSign
     * @throws SignatureException
     */
    public static void apponiFirma(SignatureCredentials credentials, List<DocumentToSign> documentsToSign) throws SignatureException {
        if (credentials instanceof RemoteSignatureCredentials) {
            try {
                RemoteSignatureHandler.getInstance().sign((RemoteSignatureCredentials) credentials, documentsToSign);
            } catch (SignatureException ex) {
                String errorCodeItagile = getErrorCodeItagile(ex);
                String errorMessage = "Errore durante la firma. Verificare l'url del server e/o le credenziali di firma remota. Codice_firma_remota: "
                        + getRestErrorCode(errorCodeItagile);
                throw new SignatureException(errorMessage, ex);
            }
        } else {
            throw new SignatureException("Tipo firma non riconosciuto o supportato");
        }
    }

    /**
     * Firma una serie di documenti creando le credenziali di firma remota
     *
     * @param firmaRemotaUrl
     * @param username
     * @param password
     * @param oneTimePassword
     * @param documentsToSign
     * @throws SignatureException
     */
    public static void apponiFirma(String firmaRemotaUrl, String username, String password, String oneTimePassword,
            List<DocumentToSign> documentsToSign) throws SignatureException {
        apponiFirma(new RemoteSignatureCredentials(firmaRemotaUrl, username, password, oneTimePassword), documentsToSign);
    }

    /**
     * Firma un documento PADES con semplice passaggio di parametri non specifici
     *
     * @param controFirmato
     * @param username
     * @param password
     * @param oneTimePassword
     * @param originaleStream
     * @param originaleStreamXML
     * @param originaleStreamOscurato
     * @throws it.netservice.nsfirmacommon.SignatureException
     */
    public static void apponiFirma(InputStream originaleStream, InputStream originaleStreamXML, InputStream originaleStreamOscurato,
            AttiToSigned attiToSigned) throws SignatureException {
        List<DocumentToSign> documenti = new ArrayList<>();
        DocumentToSign document = new DocumentToSign(originaleStream, attiToSigned.controFirmato, SignatureType.PADES, true, null);
        documenti.add(document);
        if (originaleStreamOscurato != null && attiToSigned.controFirmatoOscurato != null) {
            DocumentToSign documentOscurato = new DocumentToSign(originaleStreamOscurato, attiToSigned.controFirmatoOscurato,
                    SignatureType.PADES, true, null);
            documenti.add(documentOscurato);
        }
        if (originaleStreamXML != null && attiToSigned.controFirmatoXML != null) {
            DocumentToSign documentOscurato = new DocumentToSign(originaleStreamXML, attiToSigned.controFirmatoXML, SignatureType.CADES,
                    true, null);
            documenti.add(documentOscurato);
        }

        apponiFirma(System.getProperty("firma-remota.url"), attiToSigned.username, attiToSigned.password, attiToSigned.oneTimePassword,
                documenti);

    }

}
