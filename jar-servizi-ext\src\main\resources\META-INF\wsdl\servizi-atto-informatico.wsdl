<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:http="http://schemas.xmlsoap.org/wsdl/http/"
             xmlns:xs="http://www.w3.org/2001/XMLSchema"
             xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/"
             xmlns:y="http://www.giustizia.it/gl/common/ServiziAttoInformatico"
             xmlns:yt="http://www.giustizia.it/gl/common/types"
             xmlns:xmime="http://www.w3.org/2005/05/xmlmime"
             targetNamespace="http://www.giustizia.it/gl/common/ServiziAttoInformatico">
    <types>
        <xs:schema elementFormDefault="unqualified"
                   targetNamespace="http://www.giustizia.it/gl/common/ServiziAttoInformatico">
            <xs:import
                    namespace="http://www.giustizia.it/gl/common/types"
                    schemaLocation="common-types.xsd"/>
            <xs:element name="profiloAtto" type="y:profiloAtto"/>
            <xs:element name="profiloAttoResponse" type="y:profiloAttoResponse"/>
            <xs:element name="controFirma" type="y:controFirma"/>
            <xs:element name="controFirmaResponse" type="y:controFirmaResponse"/>
            <xs:element name="download" type="y:download"/>
            <xs:element name="downloadResponse" type="y:downloadResponse"/>
            <xs:element name="downloadOnlyAtto" type="y:downloadOnlyAtto"/>
            <xs:element name="downloadOnlyAttoResposne" type="y:downloadResponse"/>

            <xs:complexType name="profiloAtto">
                <xs:sequence>
                    <xs:element name="atto" type="yt:IdAtto"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="profiloAttoResponse">
                <xs:sequence>
                    <xs:element name="return" type="yt:ContentInfo"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="controFirma">
                <xs:sequence>
                    <xs:element name="atto" type="yt:IdAtto"/>
                    <xs:element name="signature" type="xs:base64Binary"/>
                    <xs:element name="type" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="controFirmaResponse">
            </xs:complexType>
            <xs:complexType name="download">
                <xs:sequence>
                    <xs:element name="atto" type="yt:IdAtto"/>
                    <xs:element name="inOriginale" type="xs:boolean"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="downloadOnlyAtto">
                <xs:sequence>
                    <xs:element name="atto" type="yt:IdAtto"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="downloadResponse">
                <xs:sequence>
                    <xs:element name="return" type="xs:base64Binary"/>
                </xs:sequence>
            </xs:complexType>
        </xs:schema>
    </types>
    <message name="profiloAtto">
        <part element="y:profiloAtto" name="parameters"/>
    </message>
    <message name="profiloAttoResponse">
        <part element="y:profiloAttoResponse" name="parameters"/>
    </message>
    <message name="controFirma">
        <part element="y:controFirma" name="parameters"/>
    </message>
    <message name="controFirmaResponse">
        <part element="y:controFirmaResponse" name="parameters"/>
    </message>
    <message name="download">
        <part element="y:download" name="parameters"/>
    </message>
    <message name="downloadOnlyAtto">
        <part element="y:downloadOnlyAtto" name="parameters"/>
    </message>
    <message name="downloadResponse">
        <part element="y:downloadResponse" name="return"/>
    </message>
    <portType name="ServiziAttoInformatico">
        <operation name="profiloAtto">
            <input message="y:profiloAtto" name="profiloAttoReq"/>
            <output message="y:profiloAttoResponse" name="profiloAttoRes"/>
        </operation>
        <operation name="controFirma">
            <input message="y:controFirma" name="controFirmaReq"/>
            <output message="y:controFirmaResponse" name="controFirmaRes"/>
        </operation>
        <operation name="download">
            <input message="y:download" name="downloadReq"/>
            <output message="y:downloadResponse" name="downloadRes"/>
        </operation>
        <operation name="downloadOnlyAtto">
            <input message="y:downloadOnlyAtto" name="downloadOnlyAttoReq"/>
            <output message="y:downloadResponse" name="downloadOnlyAttoRes"/>
        </operation>
    </portType>
    <binding name="ServiziAttoInformaticoSOAPBinding"
             type="y:ServiziAttoInformatico">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="profiloAtto">
            <soap:operation soapAction="#profiloAtto" style="document"/>
            <input name="profiloAttoReq">
                <soap:body use="literal"/>
            </input>
            <output name="profiloAttoRes">
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="controFirma">
            <soap:operation soapAction="#controFirma" style="document"/>
            <input name="controFirmaReq">
                <soap:body use="literal"/>
            </input>
            <output name="controFirmaRes">
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="download">
            <soap:operation soapAction="#download" style="document"/>
            <input name="downloadReq">
                <soap:body use="literal"/>
            </input>
            <output name="downloadRes">
                <soap:body use="literal"/>
            </output>
        </operation>
        <operation name="downloadOnlyAtto">
            <soap:operation soapAction="#downloadOnlyAtto" style="document"/>
            <input name="downloadOnlyAttoReq">
                <soap:body use="literal"/>
            </input>
            <output name="downloadOnlyAttoRes">
                <soap:body use="literal"/>
            </output>
        </operation>
    </binding>
    <service name="WsServiziAttoInformatico">
        <port name="ServiziAttoInformaticoSOAPPort"
              binding="y:ServiziAttoInformaticoSOAPBinding">
            <soap:address
                    location="http://localhost:8080/wasp/rpcrouter"/>
        </port>
    </service>
</definitions>
