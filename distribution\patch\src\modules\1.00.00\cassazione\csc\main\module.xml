<module xmlns="urn:jboss:module:1.1" name="cassazione.csc">
    <resources>
        <resource-root path="aopalliance-1.0.jar"/>
        <resource-root path="apache-mime4j-core-0.7.2.jar"/>
        <resource-root path="axiom-api-1.2.13.jar"/>
        <resource-root path="axiom-dom-1.2.13.jar"/>
        <resource-root path="axiom-impl-1.2.13.jar"/>
        <resource-root path="axis2-1.6.2.jar"/>
        <resource-root path="axis2-kernel-1.6.2.jar"/>
        <resource-root path="axis2-transport-http-1.6.2.jar"/>
        <resource-root path="axis2-transport-local-1.6.2.jar"/>
        <resource-root path="bcmail-jdk15on-1.51.jar"/>
        <resource-root path="bcpkix-jdk15on-1.51.jar"/>
        <resource-root path="bcprov-jdk15on-1.51.jar"/>
        <resource-root path="castor-core-1.3.3.jar"/>
        <resource-root path="castor-xml-1.3.3.jar"/>
        <resource-root path="commons-fileupload-1.2.jar"/>
        <resource-root path="commons-httpclient-3.1.jar"/>
        <resource-root path="commons-io-2.6.jar"/>
        <resource-root path="commons-lang-2.6.jar"/>
        <resource-root path="commons-text-1.1.jar"/>
        <resource-root path="csc-documentale-1.0.0.jar"/>
        <resource-root path="firmaremota-client-common-1.2.0.jar"/>
        <resource-root path="firmaremota-client-detached-1.2.0.jar"/>
        <resource-root path="ftp4j-1.5.1.jar"/>
        <resource-root path="geronimo-activation_1.1_spec-1.1.jar"/>
        <resource-root path="geronimo-javamail_1.4_spec-1.7.1.jar"/>
        <resource-root path="geronimo-jta_1.1_spec-1.1.jar"/>
        <resource-root path="geronimo-stax-api_1.0_spec-1.0.1.jar"/>
        <resource-root path="geronimo-ws-metadata_2.0_spec-1.1.2.jar"/>
        <resource-root path="httpcore-4.0.jar"/>
        <resource-root path="itextpdf-5.4.5.jar"/>
        <resource-root path="javax.inject-1.jar"/>
        <resource-root path="jaxen-1.1.3.jar"/>
        <resource-root path="jaxp-api-1.4.5.jar"/>
        <resource-root path="jaxp-ri-1.4.5.jar"/>
        <resource-root path="jsr311-api-1.0.jar"/>
        <resource-root path="log4j-1.2.17.jar"/>
        <resource-root path="neethi-3.0.2.jar"/>
        <resource-root path="ns-firma-common-1.2.0.jar"/>
        <resource-root path="poi-3.14.jar"/>
        <resource-root path="poi-ooxml-3.14.jar"/>
        <resource-root path="poi-ooxml-schemas-3.14.jar"/>
        <resource-root path="xmlbeans-2.6.0.jar"/>
        <resource-root path="servlet-api-2.3.jar"/>
        <resource-root path="slf4j-api-1.7.30.jar"/>
        <resource-root path="slf4j-simple-1.7.30.jar"/>
        <resource-root path="stax-1.2.0.jar"/>
        <resource-root path="stax-api-1.0-2.jar"/>
        <resource-root path="stax-api-1.0.1.jar"/>
        <resource-root path="stax2-api-3.0.2.jar"/>
        <resource-root path="woden-api-1.0M9.jar"/>
        <resource-root path="woden-impl-commons-1.0M9.jar"/>
        <resource-root path="woden-impl-dom-1.0M9.jar"/>
        <resource-root path="woodstox-core-asl-4.0.8.jar"/>
        <resource-root path="wstx-asl-3.2.9.jar"/>
        <resource-root path="XmlSchema-1.4.7.jar"/>
        <resource-root path="wsdl4j-1.6.2.jar"/>
        <resource-root path="commons-logging-1.2.jar"/>
        <resource-root path="commons-codec-1.13.jar"/>
        <resource-root path="adn-client-1.0.0.jar"/>
        <resource-root path="asm-3.3.1.jar"/>
        <resource-root path="axis-1.4.jar"/>
        <resource-root path="cglib-2.2.2.jar"/>
        <resource-root path="checker-compat-qual-2.5.5.jar"/>
        <resource-root path="common-rest-1.5.9.jar"/>
        <resource-root path="commons-beanutils-1.9.4.jar"/>
        <resource-root path="commons-digester3-3.2.jar"/>
        <resource-root path="commons-discovery-0.2.jar"/>
        <resource-root path="error_prone_annotations-2.3.4.jar"/>
        <resource-root path="commons-collections-3.2.2.jar"/>
        <resource-root path="failureaccess-1.0.1.jar"/>
        <resource-root path="gson-2.8.6.jar"/>
        <resource-root path="guava-28.2-android.jar"/>
        <resource-root path="j2objc-annotations-1.3.jar"/>
        <resource-root path="jackson-annotations-2.10.0.pr3.jar"/>
        <resource-root path="jackson-core-2.10.0.pr3.jar"/>
        <resource-root path="jackson-databind-2.10.0.pr3.jar"/>
        <resource-root path="java-jwt-3.9.0.jar"/>
        <resource-root path="jsr305-3.0.2.jar"/>
        <resource-root path="listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"/>
        <resource-root path="log4j-1.2.17.jar"/>
        <resource-root path="ns-common-1.5.0.jar"/>
        <resource-root path="okhttp-3.12.10.jar"/>
        <resource-root path="okio-1.15.0.jar"/>
    </resources>
    <dependencies>
        <module name="org.apache.log4j"/>
        <module name="javax.api"/>
        <module name="javaee.api" />
    </dependencies>
</module>
