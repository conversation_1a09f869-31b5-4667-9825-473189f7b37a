package it.netservice.csp.backend.sic.service;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.persistence.EntityManager;

import org.apache.log4j.Logger;

import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.GestioneSicException;
import it.netservice.csp.backend.ext.service.ServiceBuilder;
import it.netservice.csp.backend.sic.service.depositi.DepositoAbstract;
import it.netservice.csp.backend.sic.service.depositi.MinutaOrdinanza;
import it.netservice.csp.backend.sic.service.depositi.MinutaSentenza;
import it.netservice.csp.backend.sic.service.depositi.ProvvedimentoPubblicabile;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.penale.model.sic.ElaborazioneDeposito;
import it.netservice.penale.model.sic.RicorsoSic;

/**
 * Espone i servizi del modulo tramite CDI
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class GestioneSicService {

    private static final Logger LOGGER = Logger.getLogger(GestioneSicService.class);

    @Inject
    private ServiceBuilder serviceBuilder;

    public RicorsoSic accettaDeposito(EntityManager em, ElaborazioneDeposito elabDep, String operatore) throws CspBackendException {
        LOGGER.info("Inizio accettazione deposito sul SIC. idElabDeposito: " + elabDep.getId() + ", operatore:" + operatore);
        try {
            SicPenaleRepository repo = new SicPenaleRepository(em);
            DepositoAbstract gd = null;
            String tipoDeposito = elabDep.getDeposito().getTipo();
            LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] GestioneSicService.accettaDeposito - Tipo Deposito: %s, IDCAT: %d",
                    tipoDeposito, elabDep.getDeposito().getIdCat()));
            switch (tipoDeposito) {
                case "MinutaOrdinanza":
                case "MinutaOrdinanzaInterlocutoria":
                    gd = new MinutaOrdinanza(repo, operatore);
                    LOGGER.info(String.format(
                            "[LOGGING NRG/ID_UDIEN] GestioneSicService.accettaDeposito - Instanziato MinutaOrdinanza. IDCAT: %d",
                            elabDep.getDeposito().getIdCat()));
                    break;
                case "MinutaSentenza":
                    gd = new MinutaSentenza(repo, operatore);
                    LOGGER.info(String.format(
                            "[LOGGING NRG/ID_UDIEN] GestioneSicService.accettaDeposito - Instanziato MinutaSentenza. IDCAT: %d",
                            elabDep.getDeposito().getIdCat()));
                    break;
                case "Sentenza":
                case "Ordinanza":
                    gd = new ProvvedimentoPubblicabile(repo, operatore);
                    LOGGER.info(String.format(
                            "[LOGGING NRG/ID_UDIEN] GestioneSicService.accettaDeposito - Instanziato ProvvedimentoPubblicabile. IDCAT: %d",
                            elabDep.getDeposito().getIdCat()));
                    break;
                default:
                    LOGGER.warn(String.format(
                            "[LOGGING NRG/ID_UDIEN] GestioneSicService.accettaDeposito - Tipo Deposito non gestito esplicitamente per gd: %s. IDCAT: %d",
                            tipoDeposito, elabDep.getDeposito().getIdCat()));
                    break;
            }
            RicorsoSic result;
            if (gd != null) {
                LOGGER.info(String.format(
                        "[LOGGING NRG/ID_UDIEN] GestioneSicService.accettaDeposito - Chiamo gd.accettaAtto() per %s. IDCAT: %d",
                        gd.getClass().getSimpleName(), elabDep.getDeposito().getIdCat()));
                result = gd.accettaAtto(elabDep);
            } else {
                LOGGER.info(String.format(
                        "[LOGGING NRG/ID_UDIEN] GestioneSicService.accettaDeposito - gd e' null, chiamo repo.getRicorsoByElaborazioneDeposito(). IDCAT: %d",
                        elabDep.getDeposito().getIdCat()));
                result = repo.getRicorsoByElaborazioneDeposito(elabDep);
            }
            /*
             * for (Sentenza sentenza : result.getSentenze()) { Query query = em
             * .createNativeQuery("update PENALE_T_SENTENZA set DEPOSITO_TELEMATICO = 1 where ID_SENT=" + sentenza.getId());
             * query.executeUpdate(); }
             */

            LOGGER.info("Fine accettazione deposito sul SIC");
            return result;
        } catch (CspBackendException ex) {
            throw ex;
        } catch (Exception ex) {
            LOGGER.error(
                    "Errore durante l'accettazione del deposito sul SIC. idElabDeposito: " + elabDep.getId() + ", operatore:" + operatore);
            throw new GestioneSicException("Errore durante l'accettazione del deposito sul SIC",
                    RestErrorCodeEnum.ACCETTAZIONE_DEPOSITO_SIC_ERROR, ex);
        }
    }

    public RicorsoSic rifiutaDeposito(EntityManager em, ElaborazioneDeposito elabDep, String operatore) throws CspBackendException {
        LOGGER.info("Inizio rifiuto deposito sul SIC. idElabDeposito: " + elabDep.getId() + ", operatore:" + operatore);
        try {
            SicPenaleRepository repo = new SicPenaleRepository(em);
            DepositoAbstract gd = null;
            switch (elabDep.getDeposito().getTipo()) {
                case "AttoRichiestaVisibilita":
                    /* TODO AttoVis */
                    // gd = new AttoVis(repo, operatore);
                    break;
                case "DecretoFissazioneAdunanza":
                    try {
                        /* TODO statoRifiutatoProposta */
                        repo.statoRifiutatoProposta(elabDep.getDeposito().getNrg(), "ADUNANZA_RIFIUTATA");
                    } catch (CspBackendException w) {
                        throw w;
                    } catch (Throwable t) {
                        LOGGER.error("Errore nel salvataggio dei dati del ricorso. idElabDeposito:" + elabDep.getId() + ", nrg:"
                                + elabDep.getDeposito().getNrg());
                        throw new GestioneSicException("Errore nel salvataggio dei dati del ricorso.",
                                RestErrorCodeEnum.RIFIUTA_DEPOSITO_SAVE_ERROR, t);
                    }
                    break;
                case "Proposta":
                    try {
                        /* TODO statoRifiutatoProposta */
                        repo.statoRifiutatoProposta(elabDep.getDeposito().getNrg(), "PROPOSTA_RIFIUTATA");
                    } catch (CspBackendException w) {
                        throw w;
                    } catch (Throwable t) {
                        LOGGER.error("Errore nel salvataggio dei dati del ricorso. idElabDeposito:" + elabDep.getId() + ", nrg:"
                                + elabDep.getDeposito().getNrg());
                        throw new GestioneSicException("Errore nel salvataggio dei dati del ricorso.",
                                RestErrorCodeEnum.RIFIUTA_DEPOSITO_SAVE_ERROR, t);
                    }
                    break;
            }
            RicorsoSic result;
            if (gd != null) {
                result = gd.rifiutaAtto(elabDep);
            } else {
                result = null;
            }
            LOGGER.info("Fine rifiuto deposito sul SIC");
            return result;
        } catch (CspBackendException ex) {
            throw ex;
        } catch (Exception ex) {
            LOGGER.error("Errore durante il rifiuto del deposito. idElabDeposito:" + elabDep.getId() + ", nrg:"
                    + elabDep.getDeposito().getNrg());
            throw new GestioneSicException("Errore durante il rifiuto del deposito", RestErrorCodeEnum.RIFIUTA_DEPOSITO_GENERIC_ERROR, ex);
        }
    }

}
