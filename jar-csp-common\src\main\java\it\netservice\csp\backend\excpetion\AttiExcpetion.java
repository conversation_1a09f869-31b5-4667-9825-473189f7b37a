package it.netservice.csp.backend.excpetion;

import it.netservice.csp.backend.dto.ErrorTypeEnum;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;

public class AttiExcpetion extends CspBackendException {
    public AttiExcpetion(String message) {
        super(message, RestErrorCodeEnum.ATTI_GENERIC_ERROR, 433);
    }

    public AttiExcpetion(String message, RestErrorCodeEnum errorCode) {
        super(message, errorCode, 433);
    }

    public AttiExcpetion(String message, RestErrorCodeEnum errorCode, ErrorTypeEnum errorType) {
        super(message, errorCode, 433, errorType);
    }

    public AttiExcpetion(String message, RestErrorCodeEnum errorCode, Throwable cause) {
        super(message, errorCode, 433, cause);
    }

    public AttiExcpetion(String message, RestErrorCodeEnum errorCode, Throwable cause, ErrorTypeEnum errorType) {
        super(message, errorCode, 433, cause, errorType);
    }

    public AttiExcpetion(String message, RestErrorCodeEnum errorCode, String reason) {
        super(message, errorCode, reason, 433);
    }

    public AttiExcpetion(String message, RestErrorCodeEnum errorCode, String reason, ErrorTypeEnum errorType) {
        super(message, errorCode, reason, 433, errorType);
    }

    public AttiExcpetion(String message, RestErrorCodeEnum errorCode, String reason, Throwable cause) {
        super(message, errorCode, reason, 433, cause);
    }

    public AttiExcpetion(String message, RestErrorCodeEnum errorCode, String reason, Throwable cause, ErrorTypeEnum errorType) {
        super(message, errorCode, reason, 433, cause, errorType);
    }
}
