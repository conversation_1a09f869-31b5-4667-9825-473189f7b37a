plugins {
    id "jaxws" version "2.0"
    id "axisws" version "2.0"
}
apply plugin: 'netservice'
apply plugin: 'java'


jaxws {
    sourceDir = "$projectDir/src/main/resources/META-INF/wsdl/"
    wsdls {
        wsdl {
            sourceFile = "servizi-atto-informatico.wsdl"
            packageName = 'it.netservice.csp.backend.ws.atti'
        }
        wsdl {
            sourceFile = "interrogazioni-reginde-int.wsdl"
            packageName = 'it.netservice.csp.backend.ws.reginde'
        }
        wsdl {
            sourceFile = "CatalogoServizi.wsdl"
            packageName = 'it.netservice.csp.backend.ws.catalogoservizi'
        }
    }
}

axisws {
    wsdls {
        wsdl {
            sourceFile = "servizi-consultazione-esiti.wsdl"
            mappings = ['urn:LogEsiti': 'it.netservice.csp.backend.ws.depositi']
        }
        wsdl {
            sourceFile = "servizi-gestione-atti.wsdl"
            mappings = ['http://www.giustizia.it/gl/sgr/cassazione/gestione/atti': 'it.netservice.csp.backend.ws.gestioneatti', 'http://www.giustizia.it/gl/sgr/cassazione/gestione/atti/types': 'it.netservice.csc.backend.ws.gestioneatti.types']
        }
        wsdl {
            sourceFile = "servizi-accettazione.wsdl"
            mappings = ['urn:Accettazione': 'it.netservice.csp.backend.ws.accettazione']
        }
        wsdl {
            sourceFile = "servizi-supporto-pagamenti.wsdl"
            mappings = ['http://www.giustizia.it/gl/sgr/cassazione/pagamenti/types': 'it.netservice.csp.backend.ws.pagamenti.types', 'http://www.giustizia.it/gl/sgr/cassazione/pagamenti': 'it.netservice.csc.backend.ws.pagamenti']
        }
        wsdl {
            sourceFile = "depositoconsole.wsdl"
            mappings = ['urn:DepositoConsole': 'it.netservice.csp.backend.ws.depositoConsole']
        }
        wsdl {
            sourceFile = "servizi-visibilita-cassazione-cc.wsdl"
            mappings = ['http://www.netservice.it/pt/vpnc/ServiziVisibilitaParti':'it.netservice.csp.backend.ws.vpnc',
                        'http://www.netservice.it/pt/vpnc/ServiziVisibilitaParti/types':'it.netservice.csp.backend.ws.vpnc.types'
            ]
        }
		wsdl {
            sourceFile = "servizi-visibilita-cassazione-lav.wsdl"
            mappings = ['http://www.netservice.it/pt/vpnc/ServiziVisibilitaParti':'it.netservice.csp.backend.ws.vpnc',
                        'http://www.netservice.it/pt/vpnc/ServiziVisibilitaParti/types':'it.netservice.csp.backend.ws.vpnc.types'
            ]
        }
		wsdl {
            sourceFile = "servizi-visibilita-cassazione-vg.wsdl"
            mappings = ['http://www.netservice.it/pt/vpnc/ServiziVisibilitaParti':'it.netservice.csp.backend.ws.vpnc',
                        'http://www.netservice.it/pt/vpnc/ServiziVisibilitaParti/types':'it.netservice.csp.backend.ws.vpnc.types'
            ]
        }
		wsdl {
            sourceFile = "servizi-visibilita-cassazione-siecic.wsdl"
            mappings = ['http://www.netservice.it/pt/vpnc/ServiziVisibilitaParti':'it.netservice.csp.backend.ws.vpnc',
                        'http://www.netservice.it/pt/vpnc/ServiziVisibilitaParti/types':'it.netservice.csp.backend.ws.vpnc.types'
            ]
        }
    }
}


