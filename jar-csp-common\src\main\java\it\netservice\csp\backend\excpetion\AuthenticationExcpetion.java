package it.netservice.csp.backend.excpetion;

import it.netservice.csp.backend.dto.ErrorTypeEnum;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;

public class AuthenticationExcpetion extends CspBackendException {
    public AuthenticationExcpetion(String message) {
        super(message, RestErrorCodeEnum.AUTHENTICATION_GENERIC_ERROR, 433);
    }

    public AuthenticationExcpetion(String message, RestErrorCodeEnum errorCode) {
        super(message, errorCode, 433);
    }

    public AuthenticationExcpetion(String message, RestErrorCodeEnum errorCode, ErrorTypeEnum errorType) {
        super(message, errorCode, 433, errorType);
    }

    public AuthenticationExcpetion(String message, RestErrorCodeEnum errorCode, Throwable cause) {
        super(message, errorCode, 433, cause);
    }

    public AuthenticationExcpetion(String message, RestErrorCodeEnum errorCode, Throwable cause, ErrorTypeEnum errorType) {
        super(message, errorCode, 433, cause, errorType);
    }

    public AuthenticationExcpetion(String message, RestErrorCodeEnum errorCode, String reason) {
        super(message, errorCode, reason, 433);
    }

    public AuthenticationExcpetion(String message, RestErrorCodeEnum errorCode, String reason, ErrorTypeEnum errorType) {
        super(message, errorCode, reason, 433, errorType);
    }

    public AuthenticationExcpetion(String message, RestErrorCodeEnum errorCode, String reason, Throwable cause) {
        super(message, errorCode, reason, 433, cause);
    }

    public AuthenticationExcpetion(String message, RestErrorCodeEnum errorCode, String reason, Throwable cause, ErrorTypeEnum errorType) {
        super(message, errorCode, reason, 433, cause, errorType);
    }
}
