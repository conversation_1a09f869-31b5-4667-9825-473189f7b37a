/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package it.netservice.csp.backend.rest.depositi.firma;

import java.io.*;
import java.net.MalformedURLException;
import java.rmi.RemoteException;
import java.util.List;

import javax.activation.DataHandler;
import javax.annotation.security.RolesAllowed;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.Response;
import javax.xml.rpc.ServiceException;

import org.apache.commons.io.FileUtils;
import org.apache.log4j.Logger;

import com.auth0.jwt.interfaces.DecodedJWT;

import it.netservice.common.InputStreamDataSource;
import it.netservice.common.rest.backend.CriteriaBuilderHelper;
import it.netservice.csc.backend.ws.gestioneatti.types.AttoDaAcquisire;
import it.netservice.csc.backend.ws.gestioneatti.types.IdAtto;
import it.netservice.csp.backend.accettazioneDepositi.AccettazioneDepositiService;
import it.netservice.csp.backend.accettazioneDepositi.FileSignedResult;
import it.netservice.csp.backend.depositi.ContentDeposito;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.elaborazioneDepositi.service.DepositoService;
import it.netservice.csp.backend.elaborazioneDepositi.service.ElaborazioneProvvedimentoService;
import it.netservice.csp.backend.excpetion.AccettazioneDepositiExcpetion;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.CspCommonRestWarningException;
import it.netservice.csp.backend.excpetion.SignaturedException;
import it.netservice.csp.backend.ext.service.ServiceBuilder;
import it.netservice.csp.backend.firma.AttiToSigned;
import it.netservice.csp.backend.firma.FirmaRemota;
import it.netservice.csp.backend.rest.AbstractCspBackendController;
import it.netservice.csp.backend.rest.auth.SecurityUtil;
import it.netservice.csp.backend.rest.auth.TokenManager;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.csp.backend.ws.atti.IdFascicolo;
import it.netservice.csp.backend.ws.atti.ServiziAttoInformatico;
import it.netservice.penale.model.csp.ElaborazioneProvvedimento;
import it.netservice.penale.model.sic.RicorsoSic;
import it.netservice.penale.model.sic.criteria.csp.DepositiViewCriteriaParameters;
import it.netservice.penale.model.sic.csp.DepositoView;

/**
 * <AUTHOR>
 */
@RolesAllowed({ "CSPCLIENT" })
@Path("/firmaDepositi")
public class FirmaDepositiController extends AbstractCspBackendController {

    @Inject
    private ElaborazioneProvvedimentoService elaborazioneProvvedimentoService;

    @Inject
    private ServiceBuilder serviceBuilder;

    @Inject
    private TokenManager tokenManager;

    @Inject
    private DepositoService depositoService;
    @Inject
    private AccettazioneDepositiService accettazioneDepositiService;

    @Inject
    private SecurityUtil util;

    private static final Logger LOGGER = Logger.getLogger(FirmaDepositiController.class);

    @POST
    @Path("/firmaRemotaProvvedimento")
    public Response firmaProvvedimento(@Context HttpServletRequest context, @QueryParam("idDeposito") Long idDeposito,
            @QueryParam("username") String username, @QueryParam("password") String password, @QueryParam("otppassword") String otppassword)
            throws Throwable {

        LOGGER.info("Inizio firma deposito " + idDeposito);

        if (username.isEmpty() || password.isEmpty() || otppassword.isEmpty()) {
            throw new CspBackendException("Dati obbligatori non inseriti!", RestErrorCodeEnum.FIELD_MANDATORY_ERROR, 433);
        }
        DecodedJWT jwt = tokenManager.verifyToken(context.getHeader("x-auth-token"));

        EntityManager entityManager = emfCaricamento.createEntityManager();
        EntityTransaction transaction = entityManager.getTransaction();
        transaction.begin();
        ElaborazioneProvvedimento elabProv = null;
        try {

            /* Checking that the operator passed in the JWT token is valid, and throwing an error if not. */
            if (util.getOperatore(jwt) == null) {
                throw new CspBackendException("Sessione scaduta o assente", RestErrorCodeEnum.SESSION_EXPIRED, 433);
            }

            // Logging that a deposit signing command was executed by the operator
            util.logComandoDeposito(util.getOperatore(jwt), "firma deposito", idDeposito);

            // Querying the database to retrieve the deposit object matching the given ID
            CriteriaBuilderHelper<DepositoView> criteriaBuilder = new CriteriaBuilderHelper<>(DepositoView.class);
            DepositiViewCriteriaParameters param = new DepositiViewCriteriaParameters();
            param.setIdCat(idDeposito);
            DepositoView deposito = criteriaBuilder.uniqueResult(entityManager, param);

            // Checking that the deposit has been valued by the Sic system
            depositoService.checkHasBeenValorisedByTheSic(entityManager, deposito.getTipo(), deposito.getIdCat());

            // Validating the session token.
            util.checkSessionToValuedInToken(context, entityManager, idDeposito);

            SicPenaleRepository repo = new SicPenaleRepository(entityManager);

            // Authorizing the user based on their role
            SecurityUtil.checkUtenzaPubblicaProvvedimento(jwt, otppassword, repo);

            // vada come vada devo annullare il documento firmato precedentemente
            // in quanto si può firmare n volte facendo n documenti bea
            elabProv = elaborazioneProvvedimentoService.findElaborazioneProvvedimentoByIdDeposito(entityManager, idDeposito);
            elabProv.setIdCatControfirmato(null);
            entityManager.merge(elabProv);
            transaction.commit();
        } catch (Throwable e) {
            if (transaction.isActive()) {
                transaction.rollback();
            }
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la firma del provvedimento. idDeposito:" + idDeposito);
            throw new SignaturedException("Errore durante la firma del provvedimento", RestErrorCodeEnum.FIRMA_REMOTA_GENERIC_ERROR, e);
        } finally {
            entityManager.close();
        }
        entityManager = emfCaricamento.createEntityManager();
        transaction = entityManager.getTransaction();
        transaction.begin();
        try {

            FileSignedResult signedResult = fileSignedAndResult(username, password, otppassword, entityManager, elabProv);

            transaction.commit();
            return accettazioneDepositiService.accetta(util.getOperatore(jwt), util.getIdUtente(jwt), idDeposito, true, null, null,
                    signedResult, null);

        } catch (Throwable ex) {
            if (transaction.isActive()) {
                transaction.rollback();
            }
            if (ex instanceof CspBackendException) {
                throw (CspBackendException) ex;
            }
            LOGGER.error("Errore durante l'accettazione del provvedimento. idDeposito:" + idDeposito);
            throw new AccettazioneDepositiExcpetion("Errore durante l'accettazione del provvedimento",
                    RestErrorCodeEnum.ACCETTAZIONE_DEPOSITI_GENERIC_ERROR, ex);
        } finally {
            entityManager.close();
        }
    }

    private FileSignedResult fileSignedAndResult(String username, String password, String otppassword, EntityManager entityManager,
            ElaborazioneProvvedimento elabProv) throws CspBackendException {

        String idCat = elabProv.getDeposito().getIdCat().toString();
        try {

            AttiToSigned attiToSigned = new AttiToSigned();

            String filename = "depositoMagistrato_" + elabProv.getDeposito().getIdCat() + "_controFirmato_" + System.currentTimeMillis();
            attiToSigned.setFileNameXML("atto.xml.p7m");
            // firmiamo l'atto oscurato se presente
            attiToSigned.setFileName(filename);
            String filenameOScurato = "depositoMagistrato_oscurato" + elabProv.getDeposito().getIdCat() + "_controFirmato_"
                    + System.currentTimeMillis();
            attiToSigned.setFileNameOscurato(filenameOScurato);

            attiToSigned.setContent(downloadAtto(idCat, "ATTO"));
            attiToSigned.setContentXML(downloadAtto(idCat, "DA"));
            attiToSigned.setContentOcurato(downloadAtto(idCat, "SM"));
            attiToSigned.setUsername(username);
            attiToSigned.setPassword(password);
            attiToSigned.setOneTimePassword(otppassword);
            FileSignedResult signedResult = controFirmaFile(entityManager, elabProv, attiToSigned);
            return signedResult;

        } catch (SignaturedException se) {
            LOGGER.error("Errore durante la firma dei file. idCat:" + idCat, se);
            throw se;
        } catch (Exception e) {
            LOGGER.error("Errore durante la firma dei file. idCat:" + idCat, e);
            throw new SignaturedException("errore durante la firma remota.", RestErrorCodeEnum.FIRMA_REMOTA_GENERIC_ERROR, e);
        }
    }

    private byte[] downloadAtto(String idCat, String type)
            throws MalformedURLException, ServiceException, RemoteException, CspBackendException {
        List<ContentDeposito> rts = DepositoService.getContentDeposito(serviceBuilder, idCat, type);
        if (rts.size() > 0) {
            ServiziAttoInformatico service = serviceBuilder.getAttoInformaticoService();
            IdFascicolo fascicolo = new IdFascicolo();
            fascicolo.setRegistro(System.getProperty("gestorelocale.registro"));
            fascicolo.setUfficio(System.getProperty("gestorelocale.ufficio"));
            it.netservice.csp.backend.ws.atti.IdAtto idAtto = new it.netservice.csp.backend.ws.atti.IdAtto();
            idAtto.setId(rts.get(0).getCatId());
            idAtto.setFascicolo(fascicolo);
            return service.download(idAtto, true);
        }
        return null;
    }

    private FileSignedResult controFirmaFile(EntityManager entityManager, ElaborazioneProvvedimento elabProv, AttiToSigned attiToSigned)
            throws IOException, CspBackendException, ServiceException {
        FileSignedResult signedResult = new FileSignedResult();
        try (InputStream originaleStream = new ByteArrayInputStream(attiToSigned
                .getContent()); InputStream originaleStreamOscurato = attiToSigned.getContentOcurato() != null ? new ByteArrayInputStream(
                        attiToSigned.getContentOcurato()) : null; InputStream originaleStreamXML = new ByteArrayInputStream(
                                attiToSigned.getContentXML())) {
            if (Boolean.getBoolean("firmaRemotaTest")) {
                FileUtils.copyInputStreamToFile(originaleStream, attiToSigned.getControFirmato());
                FileUtils.copyInputStreamToFile(originaleStreamOscurato, attiToSigned.getControFirmatoOscurato());
            } else {
                FirmaRemota.apponiFirma(originaleStream, originaleStreamXML, originaleStreamOscurato, attiToSigned);
            }
            SicPenaleRepository repo = new SicPenaleRepository(entityManager);
            RicorsoSic ricorso = repo.getRicorsoByElaborazioneDeposito(elabProv);
            if (ricorso == null) {
                LOGGER.error(
                        String.format("Ricorso %s/%s non presente nei registri.", elabProv.getNumeroRicorso(), elabProv.getAnnoRicorso()));
                throw new CspCommonRestWarningException("Ricorso non presente nei registri", RestErrorCodeEnum.RICORSO_NOT_FOUND);
            }
            AttoDaAcquisire attoDaAcquisire = new AttoDaAcquisire(String.valueOf(ricorso.getNrg()), attiToSigned.getFileName() + ".pdf",
                    new DataHandler(new InputStreamDataSource(attiToSigned.getControFirmato())));
            AttoDaAcquisire attoDaAcquisireXML = new AttoDaAcquisire(String.valueOf(ricorso.getNrg()),
                    attiToSigned.getFileNameXML() + ".xml.p7m",
                    new DataHandler(new InputStreamDataSource(attiToSigned.getControFirmatoXML())));
            if (attiToSigned.getControFirmatoOscurato() != null && attiToSigned.getControFirmatoOscurato().exists()) {
                AttoDaAcquisire attoDaAcquisireOscurato = new AttoDaAcquisire(String.valueOf(ricorso.getNrg()),
                        attiToSigned.getFileNameOscurato() + ".pdf",
                        new DataHandler(new InputStreamDataSource(attiToSigned.getControFirmatoOscurato())));
                IdAtto idAttoControfirmatoOscurato = serviceBuilder.getGestioneAttiService().acquisisciAtto(attoDaAcquisireOscurato);
                signedResult.setIdAttoControfirmatoOscurato(idAttoControfirmatoOscurato);
            }
            IdAtto idAttoControfirmato = serviceBuilder.getGestioneAttiService().acquisisciAtto(attoDaAcquisire);
            ;
            IdAtto idAttoControfirmatoXML = serviceBuilder.getGestioneAttiService().acquisisciAtto(attoDaAcquisireXML);
            ;
            elabProv.setIdCatControfirmato(Long.parseLong(idAttoControfirmato.getId()));
            signedResult.setIdAttoControfirmato(idAttoControfirmato);
            signedResult.setIdAttoControfirmatoXML(idAttoControfirmatoXML);
            LOGGER.info("Sono stati firmati gli atti: attoId:" + idAttoControfirmato + "; idAttoOscurato:"
                    + signedResult.getIdAttoControfirmatoOscurato());
            entityManager.merge(elabProv);
            return signedResult;

        } catch (Exception ex) {
            if (ex instanceof CspBackendException)
                throw (CspBackendException) ex;
            LOGGER.error("Errore in fase di controfirma del file. idCatDeposito:" + elabProv.getDeposito().getIdCat());
            String[] arrayErrorMessage = ex.getMessage().split("Codice_firma_remota: ");
            if (arrayErrorMessage.length == 2)
                throw new SignaturedException("Errore in fase di controfirma del file", RestErrorCodeEnum.valueOf(arrayErrorMessage[1]),
                        ex);
            throw new SignaturedException("Errore in fase di controfirma del file", RestErrorCodeEnum.FIRMA_REMOTA_GENERIC_ERROR, ex);
        } finally {
            FileUtils.deleteQuietly(attiToSigned.getControFirmato());
            FileUtils.deleteQuietly(attiToSigned.getControFirmatoOscurato());
            FileUtils.deleteQuietly(attiToSigned.getControFirmatoXML());
        }

    }
}
