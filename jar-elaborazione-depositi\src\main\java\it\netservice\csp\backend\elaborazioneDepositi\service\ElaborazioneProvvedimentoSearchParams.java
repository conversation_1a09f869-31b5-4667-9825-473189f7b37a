/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package it.netservice.csp.backend.elaborazioneDepositi.service;

import it.netservice.common.rest.backend.CriteriaParametersBackend;
import it.netservice.penale.model.csp.ElaborazioneProvvedimento;

/**
 *
 * <AUTHOR>
 */
public class ElaborazioneProvvedimentoSearchParams extends CriteriaParametersBackend<ElaborazioneProvvedimento> {

    @CriteriaParametersBackend.SearchParameter(operator = CriteriaParametersBackend.Operator.EQUAL)
    String id = null;

    @CriteriaParametersBackend.SearchParameter(joins = {
            @CriteriaParametersBackend.SearchJoin(property = "deposito") }, property = "idCat", operator = CriteriaParametersBackend.Operator.EQUAL)
    Long idCatDeposito = null;

    ElaborazioneProvvedimentoSearchParams(String id) {
        this.id = id;
    }

    ElaborazioneProvvedimentoSearchParams(Long idCatDeposito) {
        this.idCatDeposito = idCatDeposito;
    }

}
