package it.netservice.csp.backend.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.jar.JarFile;
import java.util.jar.Manifest;
import java.util.logging.Logger;

import javax.servlet.ServletContext;

/**
 * Utility class to retrieve the application version from MANIFEST.MF.
 */
public class VersionUtil {
    private static final Logger LOGGER = Logger.getLogger(VersionUtil.class.getName());
    private static final String FALLBACK_VERSION = "Unknown";
    private static final String VERSION_ATTRIBUTE = "Implementation-Version";
    private static final String RC_VERSION_ATTRIBUTE = "RC-Version";
    private String version;
    private String rcVersion;
    private ServletContext servletContext;

    public VersionUtil(ServletContext servletContext) {
        this.servletContext = servletContext;
        loadVersion();
    }

    private void loadVersion() {
        LOGGER.info("Attempting to load version from MANIFEST.MF");

        try {
            String realPath = servletContext.getRealPath("/");
            File warFile = new File(realPath);

            if (warFile.isDirectory()) {

                // Exploded WAR
                File manifestFile = new File(warFile, "META-INF/MANIFEST.MF");
                if (manifestFile.exists()) {
                    try (InputStream is = new FileInputStream(manifestFile)) {
                        Manifest manifest = new Manifest(is);
                        version = manifest.getMainAttributes().getValue(VERSION_ATTRIBUTE);
                        rcVersion = manifest.getMainAttributes().getValue(RC_VERSION_ATTRIBUTE);
                    }
                }
            } else if (warFile.isFile() && warFile.getName().endsWith(".war")) {
                // WAR file
                try (JarFile jar = new JarFile(warFile)) {
                    Manifest manifest = jar.getManifest();
                    if (manifest != null) {
                        version = manifest.getMainAttributes().getValue(VERSION_ATTRIBUTE);
                        rcVersion = manifest.getMainAttributes().getValue(RC_VERSION_ATTRIBUTE);
                    }
                }
            }

            if (version != null && !version.isEmpty()) {
                LOGGER.info("Loaded app version: " + version);
                LOGGER.info("Loaded app rc version: " + rcVersion);
                return;
            }
        } catch (IOException e) {
            LOGGER.warning("Error reading MANIFEST.MF: " + e.getMessage());
        }

        LOGGER.warning("Version not found in MANIFEST.MF. Using fallback version.");
        version = FALLBACK_VERSION;
        rcVersion = FALLBACK_VERSION;
    }

    public String getProjectVersion() {
        if (rcVersion != null && !rcVersion.isEmpty()) {
            return version + "_RC" + rcVersion;
        }
        return version;
    }
}
