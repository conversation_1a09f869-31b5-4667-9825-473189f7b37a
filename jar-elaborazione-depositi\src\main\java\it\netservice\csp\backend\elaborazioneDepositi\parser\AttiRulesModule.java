package it.netservice.csp.backend.elaborazioneDepositi.parser;

import org.apache.commons.digester3.binder.AbstractRulesModule;

import it.netservice.penale.model.csp.ElaborazioneProvvedimento;

/**
 * <AUTHOR>
 */
public class AttiRulesModule extends AbstractRulesModule {

    private final String rootElement;

    public AttiRulesModule(String rootElement) {
        this.rootElement = rootElement;
    }

    @Override
    protected void configure() {
        switch (rootElement) {
            case "Provvedimento":
                configureProvvedimentoMagistrato(rootElement);
                break;
            default:
                throw new IllegalStateException("Tipo deposito " + rootElement + " non valido per questo parser.");
        }
    }

    private void configureProvvedimentoMagistrato(String root) {
        forPattern(root).createObject().ofType(ElaborazioneProvvedimento.class);
        configureProcedimento(root);
        forPattern(root + "/Tipo").setBeanProperty().withName("tipoProvvedimento");
        // Minute quando presente il tipo nell' xml (Ordinanze e Ordinanze interlocutorie)
        forPattern(root + "/Minuta/TipoMinuta").setBeanProperty().withName("tipominuta");
    }

    private void configureProcedimento(String root) {
        forPattern(root + "/procedimento/numero").setBeanProperty().withName("numeroRicorso");
        forPattern(root + "/procedimento/anno").setBeanProperty().withName("annoRicorso");
        forPattern(root).callMethod("setRefId").withParamCount(1).then().callParam().ofIndex(0).fromAttribute("RefId");
    }
}
