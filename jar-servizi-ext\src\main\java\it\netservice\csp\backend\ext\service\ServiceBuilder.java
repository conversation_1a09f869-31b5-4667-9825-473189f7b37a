package it.netservice.csp.backend.ext.service;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Hashtable;
import java.util.List;

import javax.enterprise.context.ApplicationScoped;
import javax.xml.namespace.QName;
import javax.xml.rpc.ServiceException;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.handler.Handler;

import org.apache.axis.AxisEngine;
import org.apache.axis.EngineConfiguration;
import org.apache.axis.client.Stub;
import org.apache.axis.configuration.BasicClientConfig;
import org.apache.axis.configuration.SimpleProvider;
import org.apache.axis.message.SOAPHeaderElement;

import it.netserv.www.QBuilder.ServiziQueryBuilder;
import it.netservice.common.rest.soap.InvocationDomainHeaderHandler;
import it.netservice.common.rest.soap.WebServiceProxy;
import it.netservice.common.rest.soap.XOPAttachmentHandler;
import it.netservice.csc.backend.ws.pagamenti.ServiziSupportoPagamenti;
import it.netservice.csc.backend.ws.pagamenti.WsServiziSupportoPagamentiLocator;
import it.netservice.csp.backend.ws.accettazione.ServiziAccettazione;
import it.netservice.csp.backend.ws.accettazione.WsServiziAccettazioneLocator;
import it.netservice.csp.backend.ws.atti.ServiziAttoInformatico;
import it.netservice.csp.backend.ws.atti.WsServiziAttoInformatico;
import it.netservice.csp.backend.ws.catalogoservizi.CatalogoServizi;
import it.netservice.csp.backend.ws.catalogoservizi.CatalogoServiziBeanService;
import it.netservice.csp.backend.ws.catalogoservizi.CatalogoServiziException_Exception;
import it.netservice.csp.backend.ws.depositi.LogEsiti;
import it.netservice.csp.backend.ws.depositi.WsLogEsitiLocator;
import it.netservice.csp.backend.ws.depositoConsole.ServizioDepositoConsole;
import it.netservice.csp.backend.ws.depositoConsole.WsDepositoConsoleLocator;
import it.netservice.csp.backend.ws.gestioneatti.ServiziGestioneAtti;
import it.netservice.csp.backend.ws.gestioneatti.WsServiziGestioneAttiLocator;
import it.netservice.csp.backend.ws.reginde.WsServiziInterrogazioneInterni;
import it.netservice.csp.backend.ws.reginde.WsServiziInterrogazioneInterni_Service;
import it.netservice.www.pt.vpnc.ServiziVisibilitaCassazione.ServiziVisibilitaCassazione;
import it.netservice.www.pt.vpnc.ServiziVisibilitaCassazione.WsServiziDettaglioSICCLocator;
import it.netservice.www.pt.vpnc.ServiziVisibilitaCassazione.WsServiziDettaglioSIECICLocator;
import it.netservice.www.pt.vpnc.ServiziVisibilitaCassazione.WsServiziDettaglioSILLocator;
import it.netservice.www.pt.vpnc.ServiziVisibilitaCassazione.WsServiziDettaglioSIVGLocator;
import it.netservice.www.pt.vpnc.ServiziVisibilitaCassazione.WsServiziVisibilitaCassazioneSICCLocator;
import it.netservice.www.pt.vpnc.ServiziVisibilitaCassazione.WsServiziVisibilitaCassazioneSIECICLocator;
import it.netservice.www.pt.vpnc.ServiziVisibilitaCassazione.WsServiziVisibilitaCassazioneSILLocator;
import it.netservice.www.pt.vpnc.ServiziVisibilitaCassazione.WsServiziVisibilitaCassazioneSIVGLocator;

/**
 * <AUTHOR>
 */
@ApplicationScoped
public class ServiceBuilder {

    private EngineConfiguration axisConfig;

    private final String codiceUfficio = System.getProperty("gestorelocale.ufficio", "80417740588");
    private final String glEndpoint = System.getProperty("gestorelocale.url", "http://gl-cass/Cassazione/rpcrouter");
    private final String glReceiverEndpoint = System.getProperty("gestorelocale.receiver.url", "http://gl-cass/Receiver/internal");
    private final String glUsername = System.getProperty("gestorelocale.username", "SIC");
    private final String glPassword = System.getProperty("gestorelocale.password", "processotelematico");
    private final String regindeEndpoint = System.getProperty("reginde.interrogazione.interni.url");
    private final String proxyCassEndpoint = System.getProperty("proxyCass.url");
    private final String pstEndpoint = System.getProperty("pst-be.url");

    private ServiziAttoInformatico attoInformaticoService;
    private ServiziGestioneAtti gestioneAttiService;
    private LogEsiti logEsitiService;
    private ServiziAccettazione accettazioneService;
    private ServiziSupportoPagamenti pagamentiService;
    private ServiziVisibilitaCassazione visibilitaCassazioneService;
    private ServiziQueryBuilder dettaglioFascicoloService;
    private WsServiziInterrogazioneInterni regindeService;
    private ServizioDepositoConsole depositoConsoleService;

    public ServiceBuilder() {
        if (Boolean.getBoolean("wsVerboseLog")) {
            System.setProperty("com.sun.xml.ws.transport.http.client.HttpTransportPipe.dump", "true");
            System.setProperty("com.sun.xml.internal.ws.transport.http.client.HttpTransportPipe.dump", "true");
            System.setProperty("com.sun.xml.ws.transport.http.HttpAdapter.dump", "true");
            System.setProperty("com.sun.xml.internal.ws.transport.http.HttpAdapter.dump", "true");
            System.setProperty("com.sun.xml.internal.ws.transport.http.HttpAdapter.dumpTreshold", "999999");
        }
    }

    public ServiziAttoInformatico getAttoInformaticoService() {
        if (attoInformaticoService == null) {
            attoInformaticoService = WebServiceProxy.buildServiceStub(WsServiziAttoInformatico.class, ServiziAttoInformatico.class);
            ((BindingProvider) attoInformaticoService).getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, glEndpoint);
            ((BindingProvider) attoInformaticoService).getRequestContext().put(BindingProvider.USERNAME_PROPERTY, glUsername);
            ((BindingProvider) attoInformaticoService).getRequestContext().put(BindingProvider.PASSWORD_PROPERTY, glPassword);
            List<Handler> handlerChain = ((BindingProvider) attoInformaticoService).getBinding().getHandlerChain();
            handlerChain.add(new InvocationDomainHeaderHandler("CASSAZIONE", codiceUfficio, "PTEL"));
            handlerChain.add(new XOPAttachmentHandler());
            ((BindingProvider) attoInformaticoService).getBinding().setHandlerChain(handlerChain);
        }
        return attoInformaticoService;
    }

    public ServiziGestioneAtti getGestioneAttiService() throws MalformedURLException, ServiceException {
        if (gestioneAttiService == null) {
            gestioneAttiService = new WsServiziGestioneAttiLocator(getAxisConfig()).getServiziGestioneAttiSOAPPort(new URL(glEndpoint));
            configureStub((Stub) gestioneAttiService);
        }
        return gestioneAttiService;
    }

    public LogEsiti getLogEsitiService() throws MalformedURLException, ServiceException {
        if (logEsitiService == null) {
            logEsitiService = new WsLogEsitiLocator().getLogEsitiPort(new URL(glEndpoint));
            ((Stub) logEsitiService).setMaintainSession(true);
            ((Stub) logEsitiService).setUsername(glUsername);
            ((Stub) logEsitiService).setPassword(glPassword);
            // ((Stub) logEsitiService).setHeader(createInvocationDomainGlCASS(codiceUfficio));
        }
        return logEsitiService;
    }

    public ServiziAccettazione getAccettazioneService() throws MalformedURLException, ServiceException {
        if (accettazioneService == null) {
            accettazioneService = new WsServiziAccettazioneLocator().getServiziAccettazionePort(new URL(glEndpoint));
            configureStub((Stub) accettazioneService);
        }
        return accettazioneService;
    }

    public ServiziSupportoPagamenti getSupportoPagamentiService() throws MalformedURLException, ServiceException {
        if (pagamentiService == null) {
            pagamentiService = new WsServiziSupportoPagamentiLocator(getAxisConfig())
                    .getServiziSupportoPagamentiSOAPPort(new URL(glEndpoint));
            configureStub((Stub) pagamentiService);
        }
        return pagamentiService;
    }

    public WsServiziInterrogazioneInterni getRegindeService() throws MalformedURLException, ServiceException {
        if (regindeService == null) {
            regindeService = WebServiceProxy.buildServiceStub(WsServiziInterrogazioneInterni_Service.class,
                    WsServiziInterrogazioneInterni.class);
            ((BindingProvider) regindeService).getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, regindeEndpoint);
        }
        return regindeService;
    }

    public ServizioDepositoConsole getDepositoConsoleService() throws MalformedURLException, ServiceException {
        if (depositoConsoleService == null) {
            depositoConsoleService = new WsDepositoConsoleLocator().getServizioDepositoConsole(new URL(glReceiverEndpoint));
            configureStub((Stub) depositoConsoleService);
        }
        return depositoConsoleService;
    }

    public ServiziVisibilitaCassazione getVisibilitaCassazioneServiceSICC(String codUff)
            throws MalformedURLException, ServiceException, CatalogoServiziException_Exception {
        visibilitaCassazioneService = new WsServiziVisibilitaCassazioneSICCLocator(getAxisConfig())
                .getServiziVisibilitaCassazioneSOAPPort(new URL(proxyCassEndpoint + getDistretto(codUff) + "/sicid/backend/rpcrouter"));
        ((Stub) visibilitaCassazioneService).setMaintainSession(true);
        ((Stub) visibilitaCassazioneService).setHeader(createInvocationDomain(codUff));
        return visibilitaCassazioneService;
    }

    public ServiziVisibilitaCassazione getVisibilitaCassazioneServiceSIL(String codUff)
            throws MalformedURLException, ServiceException, CatalogoServiziException_Exception {
        visibilitaCassazioneService = new WsServiziVisibilitaCassazioneSILLocator(getAxisConfig())
                .getServiziVisibilitaCassazioneSOAPPort(new URL(proxyCassEndpoint + getDistretto(codUff) + "/sicid/backend/rpcrouter"));
        ((Stub) visibilitaCassazioneService).setMaintainSession(true);
        ((Stub) visibilitaCassazioneService).setHeader(createInvocationDomain(codUff));
        return visibilitaCassazioneService;
    }

    public ServiziVisibilitaCassazione getVisibilitaCassazioneServiceSIVG(String codUff)
            throws MalformedURLException, ServiceException, CatalogoServiziException_Exception {
        visibilitaCassazioneService = new WsServiziVisibilitaCassazioneSIVGLocator(getAxisConfig())
                .getServiziVisibilitaCassazioneSOAPPort(new URL(proxyCassEndpoint + getDistretto(codUff) + "/sicid/backend/rpcrouter"));
        ((Stub) visibilitaCassazioneService).setMaintainSession(true);
        ((Stub) visibilitaCassazioneService).setHeader(createInvocationDomain(codUff));
        return visibilitaCassazioneService;
    }

    public ServiziVisibilitaCassazione getVisibilitaCassazioneServiceSIECIC(String codUff)
            throws MalformedURLException, ServiceException, CatalogoServiziException_Exception {
        visibilitaCassazioneService = new WsServiziVisibilitaCassazioneSIECICLocator(getAxisConfig())
                .getServiziVisibilitaCassazioneSOAPPort(new URL(proxyCassEndpoint + getDistretto(codUff) + "/siecic/backend/rpcrouter"));
        ((Stub) visibilitaCassazioneService).setMaintainSession(true);
        ((Stub) visibilitaCassazioneService).setHeader(createInvocationDomain(codUff));
        return visibilitaCassazioneService;
    }

    public ServiziQueryBuilder getDettaglioFascicoloServiceSICC(String codUff)
            throws MalformedURLException, ServiceException, CatalogoServiziException_Exception {
        dettaglioFascicoloService = new WsServiziDettaglioSICCLocator()
                .getDetailFascSICCSOAPPort(new URL(proxyCassEndpoint + getDistretto(codUff) + "/sicid/backend/rpcrouter"));
        ((Stub) dettaglioFascicoloService).setMaintainSession(true);
        ((Stub) dettaglioFascicoloService).setHeader(createInvocationDomain(codUff));
        return dettaglioFascicoloService;
    }

    public ServiziQueryBuilder getDettaglioFascicoloServiceSIL(String codUff)
            throws MalformedURLException, ServiceException, CatalogoServiziException_Exception {
        dettaglioFascicoloService = new WsServiziDettaglioSILLocator()
                .getDetailFascSILSOAPPort(new URL(proxyCassEndpoint + getDistretto(codUff) + "/sicid/backend/rpcrouter"));
        ((Stub) dettaglioFascicoloService).setMaintainSession(true);
        ((Stub) dettaglioFascicoloService).setHeader(createInvocationDomain(codUff));
        return dettaglioFascicoloService;
    }

    public ServiziQueryBuilder getDettaglioFascicoloServiceSIVG(String codUff)
            throws MalformedURLException, ServiceException, CatalogoServiziException_Exception {
        dettaglioFascicoloService = new WsServiziDettaglioSIVGLocator()
                .getDetailFascSIVGSOAPPort(new URL(proxyCassEndpoint + getDistretto(codUff) + "/sicid/backend/rpcrouter"));
        ((Stub) dettaglioFascicoloService).setMaintainSession(true);
        ((Stub) dettaglioFascicoloService).setHeader(createInvocationDomain(codUff));
        return dettaglioFascicoloService;
    }

    public ServiziQueryBuilder getDettaglioFascicoloServiceSIECIC(String codUff)
            throws MalformedURLException, ServiceException, CatalogoServiziException_Exception {
        dettaglioFascicoloService = new WsServiziDettaglioSIECICLocator()
                .getDetailFascSIECICSOAPPort(new URL(proxyCassEndpoint + getDistretto(codUff) + "/siecic/backend/rpcrouter"));
        ((Stub) dettaglioFascicoloService).setMaintainSession(true);
        ((Stub) dettaglioFascicoloService).setHeader(createInvocationDomain(codUff));
        return dettaglioFascicoloService;
    }

    private String getDistretto(String codiceUfficio) throws MalformedURLException, CatalogoServiziException_Exception {
        CatalogoServiziBeanService depLocator = new CatalogoServiziBeanService(new URL(pstEndpoint),
                new QName("http://www.giustizia.it/serviziTelematici/serviziGenerici", "CatalogoServiziBeanService"));
        CatalogoServizi cs = depLocator.getCatalogoServiziSOAPPort();
        return cs.getUfficioGiudiziario(codiceUfficio).getCodiceGL(); // GLMV
    }

    // Crea una configurazione base del client Axis, disabilitando le multi-refs che
    // al gl non piacciono
    private EngineConfiguration getAxisConfig() {
        if (axisConfig == null) {
            Hashtable<String, Object> opts = new Hashtable<>();
            axisConfig = new BasicClientConfig();
            opts.put(AxisEngine.PROP_DOMULTIREFS, Boolean.FALSE);
            ((SimpleProvider) axisConfig).setGlobalOptions(opts);
        }
        return axisConfig;
    }

    private void configureStub(org.apache.axis.client.Stub stub) {
        stub.setMaintainSession(true);
        stub.setUsername(glUsername);
        stub.setPassword(glPassword);
        stub.setHeader(createInvocationDomainGlCASS(codiceUfficio));
    }

    private static SOAPHeaderElement createInvocationDomain(String codiceUfficio) {
        SOAPHeaderElement headerElement = new SOAPHeaderElement(new QName("http://www.netserv.it/anag/security", "InvocationDomain", "ws"));
        headerElement.addAttribute("", "name", "CASSAZIONE");
        headerElement.setAttribute("", "group", codiceUfficio);
        headerElement.addAttribute("", "role", "CASS");
        headerElement.addAttribute("http://schemas.xmlsoap.org/soap/envelope/", "mustUnderstand", "true");
        return headerElement;
    }

    private static SOAPHeaderElement createInvocationDomainGlCASS(String codiceUfficio) {
        SOAPHeaderElement headerElement = new SOAPHeaderElement(new QName("http://www.netserv.it/anag/security", "InvocationDomain", "ws"));
        headerElement.addAttribute("", "name", System.getProperty("gestorelocale.registro"));
        headerElement.setAttribute("", "group", codiceUfficio);
        headerElement.addAttribute("", "role", "PTEL");
        headerElement.addAttribute("http://schemas.xmlsoap.org/soap/envelope/", "mustUnderstand", "true");
        return headerElement;
    }

}
