package it.netservice.csp.backend.rest;

import javax.servlet.ServletContext;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.Response;

import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.rest.auth.NoSicAuthService;
import it.netservice.csp.backend.utils.VersionUtil;

@Path("/public")
@NoSicAuthService
public class PublicController extends AbstractCspBackendController {

    private final VersionUtil versionUtil;

    public PublicController(@Context ServletContext servletContext) {
        this.versionUtil = new VersionUtil(servletContext);
    }

    @GET
    @Path("/isAlive")
    public Response isAlive() {
        return Response.ok("isAlive").build();
    }

    @GET
    @Path("/checkException")
    public Response checkException() throws CspBackendException {
        throw new CspBackendException("gestire Eccezioni");
    }

    @GET
    @Path("/version")
    public Response getProjectVersion() {
        String version = versionUtil.getProjectVersion();
        return Response.ok(version).build();
    }
}
