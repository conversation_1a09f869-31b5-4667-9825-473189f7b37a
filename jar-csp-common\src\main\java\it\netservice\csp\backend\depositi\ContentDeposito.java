package it.netservice.csp.backend.depositi;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ContentDeposito {

    private String catId;
    private String tipo;
    private String nome;
    private String status;
    private List<Esito> esiti;

    public ContentDeposito() {
    }

    public String getCatId() {
        return catId;
    }

    public void setCatId(String catId) {
        this.catId = catId;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<Esito> getEsiti() {
        return esiti;
    }

    public void setEsiti(List<Esito> esiti) {
        this.esiti = esiti;
    }

    public static String getTipo(String docType) {
        switch (docType) {
            case "ATTO":
                return "Atto principale";
            case "DA":
                return "Dati atto xml";
            case "IR":
                return "Nota iscrizione ruolo";
            case "SM":
                return "Allegato semplice";
            default:
                return docType;
        }
    }

}
