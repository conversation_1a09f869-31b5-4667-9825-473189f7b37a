/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package it.netservice.csp.backend.pubblicazioneDepositi;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

import it.netservice.penale.model.csp.ElaborazioneProvvedimento;
import it.netservice.penale.model.enums.TipoOscuramentoPubblica;
import it.netservice.penale.model.enums.TipoProvvedimentoMagistrato;

/**
 *
 * <AUTHOR>
 */
public class PubblicazioneProvvedimentoDTO {

    protected long idProvvedimento;

    // campi di elaborazione provvedimento
    @Enumerated(EnumType.STRING)
    private TipoProvvedimentoMagistrato tipoProvvedimento;

    private Boolean contributoUnificatoEsente;

    private Boolean contributoUnificatoStabilita;

    private Boolean motivazioneSemplificata;

    @Enumerated(EnumType.STRING)
    private TipoOscuramentoPubblica oscuramentoDati;

    @Enumerated(EnumType.STRING)
    private TipoProvvedimentoMagistrato tipoVerificato;

    private Boolean firmaDigitale;

    private Boolean rilasciabile;

    private Long numeroPagine;

    private Long idCatControfirmato;

    private Long idCatPubblicato;

    // informazioni inerenti il documentale

    private Long idCatDocumentale;

    private String nomeFileDocumentale;

    private String utente;

    public PubblicazioneProvvedimentoDTO(ElaborazioneProvvedimento elaborazioneProvvedimento) {
        this.tipoProvvedimento = elaborazioneProvvedimento.getTipoProvvedimento();
        this.contributoUnificatoEsente = elaborazioneProvvedimento.getContributoUnificatoEsente();
        this.contributoUnificatoStabilita = elaborazioneProvvedimento.getContributoUnificatoStabilita();
        this.motivazioneSemplificata = elaborazioneProvvedimento.getMotivazioneSemplificata();
        this.oscuramentoDati = elaborazioneProvvedimento.getOscuramentoDati();
        this.tipoVerificato = elaborazioneProvvedimento.getTipoVerificato();
        this.firmaDigitale = elaborazioneProvvedimento.getFirmaDigitale();
        this.rilasciabile = elaborazioneProvvedimento.getRilasciabile();
        this.numeroPagine = elaborazioneProvvedimento.getNumeroPagine();
        this.idCatControfirmato = elaborazioneProvvedimento.getIdCatControfirmato();
        this.idCatPubblicato = elaborazioneProvvedimento.getIdCatPubblicato();
    }

    public TipoProvvedimentoMagistrato getTipoProvvedimento() {
        return tipoProvvedimento;
    }

    public Boolean getContributoUnificatoEsente() {
        return contributoUnificatoEsente;
    }

    public Boolean getContributoUnificatoStabilita() {
        return contributoUnificatoStabilita;
    }

    public Boolean getMotivazioneSemplificata() {
        return motivazioneSemplificata;
    }

    public TipoOscuramentoPubblica getOscuramentoDati() {
        return oscuramentoDati;
    }

    public TipoProvvedimentoMagistrato getTipoVerificato() {
        return tipoVerificato;
    }

    public Boolean getFirmaDigitale() {
        return firmaDigitale;
    }

    public Boolean getRilasciabile() {
        return rilasciabile;
    }

    public Long getNumeroPagine() {
        return numeroPagine;
    }

    public Long getIdCatControfirmato() {
        return idCatControfirmato;
    }

    public Long getIdCatPubblicato() {
        return idCatPubblicato;
    }

    public Long getIdCatDocumentale() {
        return idCatDocumentale;
    }

    public void setIdCatDocumentale(Long idCatDocumentale) {
        this.idCatDocumentale = idCatDocumentale;
    }

    public String getNomeFileDocumentale() {
        return nomeFileDocumentale;
    }

    public void setNomeFileDocumentale(String nomeFileDocumentale) {
        this.nomeFileDocumentale = nomeFileDocumentale;
    }

    public long getIdProvvedimento() {
        return idProvvedimento;
    }

    public void setIdProvvedimento(long idProvvedimento) {
        this.idProvvedimento = idProvvedimento;
    }

    public String getUtente() {
        return utente;
    }

    public void setUtente(String utente) {
        this.utente = utente;
    }

}
