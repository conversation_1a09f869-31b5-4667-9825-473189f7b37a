package it.netservice.csp.backend.excpetion;

import it.netservice.csp.backend.dto.ErrorTypeEnum;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;

/**
 *
 * <AUTHOR>
 */
public class CspCommonRestWarningException extends CspBackendException {
    public CspCommonRestWarningException(String message) {
        super(message, RestErrorCodeEnum.CSP_COMMON_REST_WARNING, 433, ErrorTypeEnum.WARNING);
    }

    public CspCommonRestWarningException(String message, RestErrorCodeEnum errorCode) {
        super(message, errorCode, 433, ErrorTypeEnum.WARNING);
    }

    public CspCommonRestWarningException(String message, RestErrorCodeEnum errorCode, ErrorTypeEnum errorType) {
        super(message, errorCode, 433, errorType);
    }

    public CspCommonRestWarningException(String message, RestErrorCodeEnum errorCode, Throwable cause) {
        super(message, errorCode, 433, cause, ErrorTypeEnum.WARNING);
    }

    public CspCommonRestWarningException(String message, RestErrorCodeEnum errorCode, Throwable cause, ErrorTypeEnum errorType) {
        super(message, errorCode, 433, cause, errorType);
    }

    public CspCommonRestWarningException(String message, RestErrorCodeEnum errorCode, String reason) {
        super(message, errorCode, reason, 433, ErrorTypeEnum.WARNING);
    }

    public CspCommonRestWarningException(String message, RestErrorCodeEnum errorCode, String reason, ErrorTypeEnum errorType) {
        super(message, errorCode, reason, 433, errorType);
    }

    public CspCommonRestWarningException(String message, RestErrorCodeEnum errorCode, String reason, Throwable cause) {
        super(message, errorCode, reason, 433, cause, ErrorTypeEnum.WARNING);
    }

    public CspCommonRestWarningException(String message, RestErrorCodeEnum errorCode, String reason, Throwable cause,
            ErrorTypeEnum errorType) {
        super(message, errorCode, reason, 433, cause, errorType);
    }
}
