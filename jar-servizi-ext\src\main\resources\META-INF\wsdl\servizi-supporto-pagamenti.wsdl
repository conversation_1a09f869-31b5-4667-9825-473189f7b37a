<?xml version="1.0" encoding="UTF-8"?>
<definitions targetNamespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:glt="http://www.giustizia.it/gl/common/types" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:ns="http://www.giustizia.it/gl/sgr/cassazione/pagamenti/types" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:y="http://www.giustizia.it/gl/sgr/cassazione/pagamenti">
    <types>
        <xs:schema targetNamespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti/types" xmlns="http://www.giustizia.it/gl/sgr/cassazione/pagamenti/types" xmlns:glt="http://www.giustizia.it/gl/common/types" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tns="http://www.giustizia.it/gl/sgr/cassazione/pagamenti/types" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
            <xs:import namespace="http://schemas.xmlsoap.org/soap/encoding/" schemaLocation="soap-encoding.xsd"/>
            <xs:import namespace="http://www.giustizia.it/gl/common/types" schemaLocation="common-types.xsd"/>
            <xs:complexType name="Ricevuta">
                <xs:sequence>
                    <xs:element name="importo" type="xs:float"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="RicevutaTelematica">
                <xs:complexContent>
                    <xs:extension base="Ricevuta">
                        <xs:sequence>
                            <xs:element name="crs" type="xs:string"/>
                            <xs:element name="stato" type="xs:string"/>
                            <xs:element name="pagatore" type="xs:string"/>
                            <xs:element name="versante" type="xs:string"/>
                            <xs:element name="causale" type="xs:string"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="RicevutaTradizionale">
                <xs:complexContent>
                    <xs:extension base="Ricevuta">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="causale" type="xs:string"/>
                            <xs:element minOccurs="0" name="codiceDistretto" type="xs:string"/>
                            <xs:element minOccurs="0" name="data" type="xs:dateTime"/>
                            <xs:element minOccurs="0" name="dominio" type="xs:string"/>
                            <xs:element minOccurs="0" name="fascicolo" type="xs:string"/>
                            <xs:element minOccurs="0" name="gruppo" type="xs:string"/>
                            <xs:element minOccurs="0" name="id" type="xs:string"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="RicevutaF23">
                <xs:complexContent>
                    <xs:extension base="tns:RicevutaTradizionale">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="codF23" type="xs:string"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="RicevutaMarcaBollo">
                <xs:complexContent>
                    <xs:extension base="tns:RicevutaTradizionale">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="codMarca" type="xs:string"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="RicevutaPoste">
                <xs:complexContent>
                    <xs:extension base="tns:RicevutaTradizionale">
                        <xs:sequence>
                            <xs:element minOccurs="0" name="codAgenzia" type="xs:string"/>
                            <xs:element minOccurs="0" name="codVcy" type="xs:string"/>
                        </xs:sequence>
                    </xs:extension>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="StatoPagamento">
                <xs:sequence>
                    <xs:element minOccurs="0" name="dataPagamento" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="idPagamento" type="xs:string"/>
                    <xs:element minOccurs="0" name="importo" type="xs:float"/>
                    <xs:element minOccurs="0" name="pagatore" type="xs:string"/>
                    <xs:element minOccurs="0" name="causale" type="xs:string"/>
                    <xs:element minOccurs="0" name="tipoPagamento" type="xs:string"/>
                    <xs:element minOccurs="0" name="idRegistro" type="xs:string"/>
                    <xs:element minOccurs="0" name="ufficio" type="xs:string"/>
                    <xs:element minOccurs="0" name="statoPagamento" type="xs:string"/>
                    <xs:element minOccurs="0" name="dataInvalidazione" type="xs:dateTime"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ArrayRicevutaTelematica">
                <xs:complexContent>
                    <xs:restriction base="soapenc:Array">
                        <xs:attribute ref="soapenc:arrayType" wsdl:arrayType="tns:RicevutaTelematica[]"/>
                    </xs:restriction>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="ArrayRicevutaTradizionale">
                <xs:complexContent>
                    <xs:restriction base="soapenc:Array">
                        <xs:attribute ref="soapenc:arrayType" wsdl:arrayType="tns:RicevutaTradizionale[]"/>
                    </xs:restriction>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="ArrayStatoPagamento">
                <xs:complexContent>
                    <xs:restriction base="soapenc:Array">
                        <xs:attribute ref="soapenc:arrayType" wsdl:arrayType="tns:StatoPagamento[]"/>
                    </xs:restriction>
                </xs:complexContent>
            </xs:complexType>
            <xs:complexType name="InvalidaPagamentoTradizionale">
                <xs:sequence/>
            </xs:complexType>
            <xs:complexType name="associaPagamentoTradizionale">
                <xs:sequence/>
            </xs:complexType>
            <xs:complexType name="associaPagamentoTelematico">
                <xs:sequence/>
            </xs:complexType>
            <xs:complexType name="invalidaCRS">
                <xs:sequence/>
            </xs:complexType>
        </xs:schema>
    </types>
    <message name="ricercaCRSReq">
        <part name="crs" type="xs:string"/>
    </message>
    <message name="ricercaCRSRes">
        <part name="result" type="ns:ArrayRicevutaTelematica"/>
    </message>
    <message name="scaricaCRSReq">
        <part name="crs" type="xs:string"/>
    </message>
    <message name="scaricaCRSRes">
        <part name="result" type="xs:base64Binary"/>
    </message>
    <message name="dettaglioPagamentoTradizionaleReq">
        <part name="dettaglio" type="ns:RicevutaTradizionale"/>
    </message>
    <message name="dettaglioPagamentoTradizionaleRes">
        <part name="result" type="ns:RicevutaTradizionale"/>
    </message>
    <message name="invalidaPagamentoTradizionaleReq">
        <part name="datiPagamento" type="ns:RicevutaTradizionale"/>
    </message>
    <message name="invalidaPagamentoTradizionaleRes">
        <part name="result" type="ns:InvalidaPagamentoTradizionale"/>
    </message>
    <message name="associaPagamentoTradizionaleReq">
        <part name="ufficio" type="xs:string"/>
        <part name="registro" type="xs:string"/>
        <part name="idFascicolo" type="xs:string"/>
        <part name="ricevute" type="ns:ArrayRicevutaTradizionale"/>
        <part name="descrizione" type="xs:string"/>
    </message>
    <message name="associaPagamentoTelematicoReq">
        <part name="ufficio" type="xs:string"/>
        <part name="registro" type="xs:string"/>
        <part name="idFascicolo" type="xs:string"/>
        <part name="pagatore" type="xs:string"/>
        <part name="importo" type="xs:float"/>
        <part name="crs" type="xs:string"/>
    </message>
    <message name="associaPagamentoTradizionaleRes">
        <part name="result" type="ns:associaPagamentoTradizionale"/>
    </message>
    <message name="associaPagamentoTelematicoRes">
        <part name="result" type="ns:associaPagamentoTelematico"/>
    </message>
    <message name="invalidaCRSReq">
        <part name="crs" type="xs:string"/>
    </message>
    <message name="invalidaCRSRes">
        <part name="result" type="ns:invalidaCRS"/>
    </message>
    <message name="elencoPagamentiReq">
        <part name="idFasc" type="xs:string"/>
    </message>
    <message name="elencoPagamentiRes">
        <part name="result" type="ns:ArrayStatoPagamento"/>
    </message>
    <portType name="ServiziSupportoPagamenti">
        <operation name="ricercaCRS">
            <input message="y:ricercaCRSReq"/>
            <output message="y:ricercaCRSRes"/>
        </operation>
        <operation name="scaricaCRS">
            <input message="y:scaricaCRSReq"/>
            <output message="y:scaricaCRSRes"/>
        </operation>
        <operation name="dettaglioPagamentoTradizionale">
            <input message="y:dettaglioPagamentoTradizionaleReq"/>
            <output message="y:dettaglioPagamentoTradizionaleRes"/>
        </operation>
        <operation name="invalidaPagamentoTradizionale">
            <input message="y:invalidaPagamentoTradizionaleReq"/>
            <output message="y:invalidaPagamentoTradizionaleRes"/>
        </operation>
        <operation name="associaPagamentoTradizionale">
            <input message="y:associaPagamentoTradizionaleReq"/>
            <output message="y:associaPagamentoTradizionaleRes"/>
        </operation>
        <operation name="associaPagamentoTelematico">
            <input message="y:associaPagamentoTelematicoReq"/>
            <output message="y:associaPagamentoTelematicoRes"/>
        </operation>
        <operation name="invalidaCRS">
            <input message="y:invalidaCRSReq"/>
            <output message="y:invalidaCRSRes"/>
        </operation>
        <operation name="elencoPagamenti">
            <input message="y:elencoPagamentiReq"/>
            <output message="y:elencoPagamentiRes"/>
        </operation>
    </portType>
    <binding name="ServiziSupportoPagamentiSOAPBinding" type="y:ServiziSupportoPagamenti">
        <soap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="ricercaCRS">
            <input>
                <soap:body namespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" use="encoded"/>
            </input>
            <output>
                <soap:body namespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" use="encoded"/>
            </output>
        </operation>
        <operation name="scaricaCRS">
            <input>
                <soap:body namespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" use="encoded"/>
            </input>
            <output>
                <soap:body namespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" use="encoded"/>
            </output>
        </operation>
        <operation name="dettaglioPagamentoTradizionale">
            <input>
                <soap:body namespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" use="encoded"/>
            </input>
            <output>
                <soap:body namespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" use="encoded"/>
            </output>
        </operation>
        <operation name="invalidaPagamentoTradizionale">
            <input>
                <soap:body namespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" use="encoded"/>
            </input>
            <output>
                <soap:body namespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" use="encoded"/>
            </output>
        </operation>
        <operation name="associaPagamentoTradizionale">
            <input>
                <soap:body namespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" use="encoded"/>
            </input>
            <output>
                <soap:body namespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" use="encoded"/>
            </output>
        </operation>
        <operation name="associaPagamentoTelematico">
            <input>
                <soap:body namespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" use="encoded"/>
            </input>
            <output>
                <soap:body namespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" use="encoded"/>
            </output>
        </operation>
        <operation name="invalidaCRS">
            <input>
                <soap:body namespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" use="encoded"/>
            </input>
            <output>
                <soap:body namespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" use="encoded"/>
            </output>
        </operation>
        <operation name="elencoPagamenti">
            <input>
                <soap:body namespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" use="encoded"/>
            </input>
            <output>
                <soap:body namespace="http://www.giustizia.it/gl/sgr/cassazione/pagamenti" use="encoded"/>
            </output>
        </operation>
    </binding>
    <service name="WsServiziSupportoPagamenti">
        <port binding="y:ServiziSupportoPagamentiSOAPBinding" name="ServiziSupportoPagamentiSOAPPort">
            <soap:address location="http://localhost:8080/siecic/backend/rpcrouter"/>
        </port>
    </service>
</definitions>