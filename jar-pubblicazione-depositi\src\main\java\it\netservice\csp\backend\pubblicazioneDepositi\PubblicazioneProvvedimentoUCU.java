/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package it.netservice.csp.backend.pubblicazioneDepositi;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

import it.netservice.penale.model.csp.ElaborazioneProvvedimento;
import it.netservice.penale.model.enums.TipoOscuramentoPubblica;
import it.netservice.penale.model.enums.TipoProvvedimentoMagistrato;

/**
 *
 * <AUTHOR>
 */
public class PubblicazioneProvvedimentoUCU {

    protected long idProvvedimento;

    // campi di elaborazione provvedimento
    @Enumerated(EnumType.STRING)
    private TipoProvvedimentoMagistrato tipoProvvedimento;

    private Boolean contributoUnificatoEsente;

    private Boolean contributoUnificatoStabilita;

    private Boolean motivazioneSemplificata;

    @Enumerated(EnumType.STRING)
    private TipoOscuramentoPubblica oscuramentoDati;

    @Enumerated(EnumType.STRING)
    private TipoProvvedimentoMagistrato tipoVerificato;

    private Boolean firmaDigitale;

    private Boolean rilasciabile;

    private Long numeroPagine;

    private Long idCatControfirmato;

    private Long idCatPubblicato;

    // informazioni inerenti il documentale

    private Long idDocumentoPdoc;

    private String nomeFilePdoc;

    private Long idDocumentoPdocOscurato;

    private String nomeFilePdocOscurato;

    private String utente;

    public PubblicazioneProvvedimentoUCU(ElaborazioneProvvedimento elaborazioneProvvedimento) {
        this.tipoProvvedimento = elaborazioneProvvedimento.getTipoProvvedimento();
        this.contributoUnificatoEsente = elaborazioneProvvedimento.getContributoUnificatoEsente();
        this.contributoUnificatoStabilita = elaborazioneProvvedimento.getContributoUnificatoStabilita();
        this.motivazioneSemplificata = elaborazioneProvvedimento.getMotivazioneSemplificata();
        this.oscuramentoDati = elaborazioneProvvedimento.getOscuramentoDati();
        this.tipoVerificato = elaborazioneProvvedimento.getTipoVerificato();
        this.firmaDigitale = elaborazioneProvvedimento.getFirmaDigitale();
        this.rilasciabile = elaborazioneProvvedimento.getRilasciabile();
        this.numeroPagine = elaborazioneProvvedimento.getNumeroPagine();
        this.idCatControfirmato = elaborazioneProvvedimento.getIdCatControfirmato();
        this.idCatPubblicato = elaborazioneProvvedimento.getIdCatPubblicato();
    }

    public TipoProvvedimentoMagistrato getTipoProvvedimento() {
        return tipoProvvedimento;
    }

    public Boolean getContributoUnificatoEsente() {
        return contributoUnificatoEsente;
    }

    public Boolean getContributoUnificatoStabilita() {
        return contributoUnificatoStabilita;
    }

    public Boolean getMotivazioneSemplificata() {
        return motivazioneSemplificata;
    }

    public TipoOscuramentoPubblica getOscuramentoDati() {
        return oscuramentoDati;
    }

    public TipoProvvedimentoMagistrato getTipoVerificato() {
        return tipoVerificato;
    }

    public Boolean getFirmaDigitale() {
        return firmaDigitale;
    }

    public Boolean getRilasciabile() {
        return rilasciabile;
    }

    public Long getNumeroPagine() {
        return numeroPagine;
    }

    public Long getIdCatControfirmato() {
        return idCatControfirmato;
    }

    public Long getIdCatPubblicato() {
        return idCatPubblicato;
    }

    public Long getIdDocumentoPdoc() {
        return idDocumentoPdoc;
    }

    public void setIdDocumentoPdoc(Long idDocumentoPdoc) {
        this.idDocumentoPdoc = idDocumentoPdoc;
    }

    public String getNomeFilePdoc() {
        return nomeFilePdoc;
    }

    public void setNomeFilePdoc(String nomeFilePdoc) {
        this.nomeFilePdoc = nomeFilePdoc;
    }

    public long getIdProvvedimento() {
        return idProvvedimento;
    }

    public void setIdProvvedimento(long idProvvedimento) {
        this.idProvvedimento = idProvvedimento;
    }

    public Long getIdDocumentoPdocOscurato() {
        return idDocumentoPdocOscurato;
    }

    public void setIdDocumentoPdocOscurato(Long idDocumentoPdocOscurato) {
        this.idDocumentoPdocOscurato = idDocumentoPdocOscurato;
    }

    public String getNomeFilePdocOscurato() {
        return nomeFilePdocOscurato;
    }

    public void setNomeFilePdocOscurato(String nomeFilePdocOscurato) {
        this.nomeFilePdocOscurato = nomeFilePdocOscurato;
    }

    public String getUtente() {
        return utente;
    }

    public void setUtente(String utente) {
        this.utente = utente;
    }

}
