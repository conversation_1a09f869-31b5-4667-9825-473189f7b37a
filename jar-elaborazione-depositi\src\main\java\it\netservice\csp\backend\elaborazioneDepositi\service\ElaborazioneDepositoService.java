package it.netservice.csp.backend.elaborazioneDepositi.service;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;

import org.apache.log4j.Logger;

import it.netservice.common.rest.backend.CriteriaBuilderHelper;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.elaborazioneDepositi.parser.DocTypeMapper;
import it.netservice.csp.backend.elaborazioneDepositi.parser.ElaborazioneDepositoParser;
import it.netservice.csp.backend.excpetion.CreateElaborazioneException;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.CspCommonRestWarningException;
import it.netservice.csp.backend.excpetion.ElaborazioneException;
import it.netservice.csp.backend.ext.service.ServiceBuilder;
import it.netservice.csp.backend.firma.ContaPaginePDF;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.csp.backend.ws.atti.IdAtto;
import it.netservice.csp.backend.ws.atti.IdFascicolo;
import it.netservice.csp.backend.ws.atti.ServiziAttoInformatico;
import it.netservice.csp.backend.ws.depositi.ContentSummaryType;
import it.netservice.csp.backend.ws.depositi.EnvSummaryType;
import it.netservice.csp.backend.ws.depositi.LogEsiti;
import it.netservice.penale.model.csp.ElaborazioneProvvedimento;
import it.netservice.penale.model.enums.TipoProvvedimentoMagistrato;
import it.netservice.penale.model.sic.Deposito;
import it.netservice.penale.model.sic.ElaborazioneDeposito;
import it.netservice.penale.model.sic.RicorsoSic;

/**
 * <AUTHOR>
 */
@ApplicationScoped
public class ElaborazioneDepositoService {

    private static final Logger LOGGER = Logger.getLogger(ElaborazioneDepositoService.class);

    @Inject
    private ServiceBuilder serviceBuilder;
    @Inject
    private DepositoService depositoService;

    public ElaborazioneDeposito findElaborazioneDepositoById(EntityManager entityManager, String id) throws ElaborazioneException {
        if (id == null) {
            throw new ElaborazioneException("Riferimento deposito non corretto. id deposito null", RestErrorCodeEnum.ID_DEPOSITO_NULL);
        }
        CriteriaBuilderHelper<ElaborazioneDeposito> criteriaBuilder = new CriteriaBuilderHelper<>(ElaborazioneDeposito.class);
        return criteriaBuilder.uniqueResult(entityManager, new ElaborazioneDepositoSearchParams(id));
    }

    public ElaborazioneDeposito findElaborazioneDepositoByIdDeposito(EntityManager entityManager, Long idCat, EntityTransaction transaction)
            throws ElaborazioneException {
        if (idCat == null) {
            throw new ElaborazioneException("idCat deposito null", RestErrorCodeEnum.IDCAT_DEPOSITO_NULL);
        }
        CriteriaBuilderHelper<ElaborazioneDeposito> criteriaBuilder = new CriteriaBuilderHelper<>(ElaborazioneDeposito.class);
        List<ElaborazioneDeposito> list = criteriaBuilder.resultList(entityManager, new ElaborazioneDepositoSearchParams(idCat));
        ElaborazioneDeposito elaborazioneDeposito = null;
        if (list.isEmpty()) {
            elaborazioneDeposito = createElaborazioneDeposito(entityManager, idCat, transaction);
        } else {
            elaborazioneDeposito = list.get(0);
        }
        // se e' un ente pubblico devo buttare tutto nel cognome
        // if (elaborazioneDeposito.getParti() != null) {
        // for (ParteElaborazioneDeposito p : elaborazioneDeposito.getParti()) {
        // if ("ENP".equals(p.getNaturaGiuridica())) {
        // p.setNome(null);
        // }
        // }
        // }
        return elaborazioneDeposito;
    }

    // public class ProvvedimentoLavorazioneSearchParams extends CriteriaParametersBackend<ProvvedimentoLavorazione> {
    //
    // @SearchParameter(operator = Operator.EQUAL)
    // String fkIdCat;
    //
    // @SearchParameter(operator = Operator.EQUAL)
    // String idWcc;
    //
    // ProvvedimentoLavorazioneSearchParams(String fkIdCat, String idWcc) {
    // this.fkIdCat = fkIdCat;
    // this.idWcc = idWcc;
    // }
    // }
    //
    // public boolean findMinutaAccettataByIdDepositoDefinitivo(EntityManager entityManager, Long idCat, EntityTransaction transaction)
    // throws ElaborazioneServiceException {
    // if (idCat == null) {
    // throw new ElaborazioneServiceException("idCat null");
    // }
    // CriteriaBuilderHelper<ProvvedimentoLavorazione> criteriaBuilder = new CriteriaBuilderHelper<>(ProvvedimentoLavorazione.class);
    // ProvvedimentoLavorazione provvedimentoLavorazioneDefinitivo = null;
    // try {
    // provvedimentoLavorazioneDefinitivo = criteriaBuilder.uniqueResult(entityManager,
    // new ProvvedimentoLavorazioneSearchParams(idCat.toString(), null));
    // } catch (NoResultException ex) {
    // return false;
    // }
    //
    // Calendar myCal = Calendar.getInstance();
    // myCal.set(Calendar.YEAR, 2022);
    // myCal.set(Calendar.MONTH, 4);
    // myCal.set(Calendar.DAY_OF_MONTH, 24);
    // Date dataPrecedenteDoppioDeposito = myCal.getTime();
    // if (provvedimentoLavorazioneDefinitivo.getDataDeposito().before(dataPrecedenteDoppioDeposito)) {
    // return true;
    // }
    // List<ProvvedimentoLavorazione> listProvvedimentiLavorazione = criteriaBuilder.resultList(entityManager,
    // new ProvvedimentoLavorazioneSearchParams(null, provvedimentoLavorazioneDefinitivo.getIdWcc()));
    // for (ProvvedimentoLavorazione provv : listProvvedimentiLavorazione) {
    // if (provv.getTipoProvvLav() != provvedimentoLavorazioneDefinitivo.getTipoProvvLav()) {
    // Deposito deposito = entityManager.find(Deposito.class, Long.parseLong(provv.getFkIdCat()));
    // if (Deposito.Stato.ACCETTATO.equals(deposito.getStato())) {
    // return true;
    // }
    // }
    // }
    // return false;
    // }
    //
    private ElaborazioneDeposito createElaborazioneDeposito(EntityManager entityManager, Long idCat, EntityTransaction transaction)
            throws ElaborazioneException {
        LOGGER.info("Inizio creazione elaborazione deposito " + idCat);
        ElaborazioneDeposito elaborazioneDeposito = null;
        String rootElementName;
        boolean transactionFromEntityManager = transaction == null;

        if (transactionFromEntityManager) {
            transaction = entityManager.getTransaction();

            if (!transaction.isActive()) {
                transaction.begin();
            }
        }
        try {
            Deposito deposito = entityManager.find(Deposito.class, idCat);
            // eseguo il check se il deposito è stato pubblicato o depositato dal sic
            depositoService.checkHasBeenValorisedByTheSic(entityManager, deposito.getTipo(), deposito.getIdCat());
            if (deposito.getIdCatDatiAtto() == null) {
                return null;
            }
            byte[] content = downloadAtto(deposito.getIdCatDatiAtto());
            try (InputStream contentStream = new ByteArrayInputStream(content)) {
                rootElementName = DocTypeMapper.getRootElement(contentStream);
            }
            try (InputStream contentStream = new ByteArrayInputStream(content)) {
                elaborazioneDeposito = ElaborazioneDepositoParser.getDatiAttoAsObject(contentStream, rootElementName, null);
            }

            elaborazioneDeposito.setNumeroRicorso(deposito.getNumeroRicorso());
            elaborazioneDeposito.setAnnoRicorso(deposito.getAnnoRicorso());
            elaborazioneDeposito.setDeposito(deposito);

            if (deposito.isDepositoMagistratoPubblicaInAccettazione()) {
                try {
                    // setta il valore del tipo verificato
                    ((ElaborazioneProvvedimento) elaborazioneDeposito)
                            .setTipoVerificato(mapTipiPubblicazioneMagistrato(deposito.getTipo()));
                    ((ElaborazioneProvvedimento) elaborazioneDeposito).setRilasciabile(Boolean.TRUE);
                    // contapagine atto magistrato
                    int numPagine = ContaPaginePDF.contaPaginePDF(downloadAttoContent(deposito.getIdCat()));
                    ((ElaborazioneProvvedimento) elaborazioneDeposito).setNumeroPagine(new Long(numPagine));

                } catch (Throwable t) {
                    ((ElaborazioneProvvedimento) elaborazioneDeposito).setNumeroPagine(new Long(0));
                    LOGGER.error("Errore durante il conteggio pagine del deposito ATTO. idCat:" + idCat, t);
                }

                // provvedimenti definitivi si cerca la ultima udienza con esito D...
                try {
                    SicPenaleRepository repo = new SicPenaleRepository(entityManager);
                    // valorizzazione in base ai valori sic se il provvedimento Ã¨ presente
                    long dataMax = 0;
                    // Sentenza sentenzaRicorso = null;
                    //
                    // RicorsoSic ricorso = repo.getRicorsoByElaborazioneDeposito(elaborazioneDeposito);
                    //
                    // for (Sentenza sentenza : ricorso.getSentenze()) {
                    // if (sentenza.getIdUdienza() != null) {
                    // UdienzaSic udienza = repo.findEntity(UdienzaSic.class, sentenza.getIdUdienza());
                    // if (udienza != null && udienza.getDataUdienza() != null && udienza.getDataUdienza().getTime() > dataMax
                    // && "D".equals(sentenza.getEsitoUdienza())) {
                    // sentenzaRicorso = sentenza;
                    // dataMax = udienza.getDataUdienza().getTime();
                    // }
                    // }
                    // }
                    //
                    // if (sentenzaRicorso == null) {
                    // // mettere warning di modo che si capisca nella interfaccia
                    // LOGGER.info("ATTO pervenuto prima della registrazione del provvedimento");
                    // } else {
                    // ((ElaborazioneProvvedimento) elaborazioneDeposito)
                    // .setMotivazioneSemplificata(map10(sentenzaRicorso.getSemplificata()));
                    // DispositivoUdienzaSic dispositivoUdienzaSic = repo.getDispositivoUdienzaSic(sentenzaRicorso.getSentenza(),
                    // repo.getIdSezione(sentenzaRicorso.getSezione()).longValue(),
                    // PubblicazioneUtil.mapTipoSent(((ElaborazioneProvvedimento) elaborazioneDeposito).getTipoVerificato()));
                    // if (dispositivoUdienzaSic != null) {
                    // ((ElaborazioneProvvedimento) elaborazioneDeposito)
                    // .setContributoUnificatoStabilita(mapSINO(dispositivoUdienzaSic.getContributoUnificato()));
                    // ((ElaborazioneProvvedimento) elaborazioneDeposito)
                    // .setIdDispositivo(dispositivoUdienzaSic.getIdDispositivo());
                    // }
                    // }
                } catch (Throwable t) {
                    LOGGER.error("Errore durante la valorizzazione dal sic dell' ATTO. idCat:" + idCat, t);
                }
            }

            entityManager.persist(elaborazioneDeposito);
            if (transactionFromEntityManager) {
                transaction.commit();
            }
            return elaborazioneDeposito;
        } catch (Throwable ex) {
            if (transactionFromEntityManager && transaction.isActive()) {
                transaction.rollback();
            }
            if (elaborazioneDeposito != null) {
                // errore in fase di persistenza (puo' avvenire nel caso in cui un valore del dati atto supera la dimensione di una colonna
                // db)
                // si restituisce comunque l'oggetto all'utente in modo che possa elaborare la busta e correggere i dati
                LOGGER.error("Errore durante il salvataggio dell'elaborazione deposito. idCat:" + idCat, ex);
                elaborazioneDeposito.setWithErrors(true);
                throw new CreateElaborazioneException(elaborazioneDeposito, "Errore durante il salvataggio dell'elaborazione deposito.",
                        RestErrorCodeEnum.ELABORAZIONE_DEPOSITO_SAVE_ERROR, ex);
            } else {
                LOGGER.error("Errore durante la creazione elaborazione deposito. idCat:" + idCat, ex);
                throw new ElaborazioneException("Errore durante la creazione elaborazione deposito",
                        RestErrorCodeEnum.ELABORAZIONE_DEPOSITO_SAVE_ERROR, ex);
            }
        }
    }

    public ElaborazioneDeposito saveElaborazioneDeposito(EntityManager entityManager, ElaborazioneDeposito elaborazioneDeposito)
            throws CspBackendException {
        LOGGER.info("Inizio salvataggio elaborazione deposito " + elaborazioneDeposito.getId());
        // LOGGING NRG/ID_UDIEN - Inizio saveElaborazioneDeposito
        LOGGER.info(String.format(
                "[LOGGING NRG/ID_UDIEN] saveElaborazioneDeposito - START - IDCAT: %d, ElabDep.NumeroRicorso: %s, ElabDep.AnnoRicorso: %s",
                elaborazioneDeposito.getDeposito().getIdCat(), elaborazioneDeposito.getNumeroRicorso(),
                elaborazioneDeposito.getAnnoRicorso()));
        try {
            Deposito deposito = entityManager.find(Deposito.class, elaborazioneDeposito.getDeposito().getIdCat());
            if (deposito == null) {
                throw new ElaborazioneException("Dati deposito non trovati", RestErrorCodeEnum.DEPOSITO_NOT_FOUND);
            }
            if (Deposito.Stato.ACCETTATO.equals(deposito.getStato()) || Deposito.Stato.RIFIUTATO.equals(deposito.getStato())) {
                LOGGER.error("Impossibile salvare: il deposito risulta già accettato/rifiutato. idCat:"
                        + elaborazioneDeposito.getDeposito().getIdCat() + ", stato:" + deposito.getStato());
                throw new ElaborazioneException("Impossibile salvare: il deposito risulta già accettato/rifiutato",
                        RestErrorCodeEnum.DEPOSITO_ACCETTATO_RIFIUTATO);
            }

            if (elaborazioneDeposito.getNumeroRicorso() != null) {
                SicPenaleRepository repo = new SicPenaleRepository(entityManager);
                RicorsoSic ricorso = repo.getRicorsoByElaborazioneDeposito(elaborazioneDeposito);
                if (ricorso == null) {
                    LOGGER.error(String.format("Impossibile salvare. Ricorso %s/%s non presente nei registri.",
                            elaborazioneDeposito.getNumeroRicorso(), elaborazioneDeposito.getAnnoRicorso()));
                    throw new CspCommonRestWarningException(
                            String.format("Impossibile salvare. Ricorso %s/%s non presente nei registri.",
                                    elaborazioneDeposito.getNumeroRicorso(), elaborazioneDeposito.getAnnoRicorso()),
                            RestErrorCodeEnum.RICORSO_NOT_FOUND);
                }
                deposito.setRicorso(ricorso);
            } else {
                deposito.setRicorso(null);
                deposito.setNumeroRicorso(null);
                deposito.setAnnoRicorso(null);
            }

            // LOGGING NRG/ID_UDIEN - Prima di saveDepositoAndElaborazione in saveElaborazioneDeposito
            LOGGER.info(String.format(
                    "[LOGGING NRG/ID_UDIEN] saveElaborazioneDeposito - Prima di chiamare saveDepositoAndElaborazione. IDCAT: %d, Deposito.nrg: %s",
                    deposito.getIdCat(), deposito.getNrg()));
            if (deposito.getRicorso() != null) {
                LOGGER.info(String.format(
                        "[LOGGING NRG/ID_UDIEN] saveElaborazioneDeposito - Prima di chiamare saveDepositoAndElaborazione. IDCAT: %d, Deposito.getRicorso().getNrg(): %s",
                        deposito.getIdCat(), deposito.getRicorso().getNrg()));
            } else {
                LOGGER.info(String.format(
                        "[LOGGING NRG/ID_UDIEN] saveElaborazioneDeposito - Prima di chiamare saveDepositoAndElaborazione. IDCAT: %d, Deposito.getRicorso() is NULL.",
                        deposito.getIdCat()));
            }
            return saveDepositoAndElaborazione(entityManager, elaborazioneDeposito, deposito);
        } catch (CspBackendException e) {
            throw e;
        } catch (Throwable e) {
            LOGGER.error("Errore durante il salvataggio dell'elaborazione del deposito");
            throw new ElaborazioneException("Errore nel salvataggio dell'elaborazione", RestErrorCodeEnum.ELABORAZIONE_DEPOSITO_SAVE_ERROR,
                    e);
        }
    }

    private ElaborazioneDeposito saveDepositoAndElaborazione(EntityManager entityManager, ElaborazioneDeposito elaborazioneDeposito,
            Deposito deposito) throws ElaborazioneException {
        Long idCat = deposito.getIdCat();
        try {
            // LOGGING NRG/ID_UDIEN - Prima di merge Deposito in saveDepositoAndElaborazione
            LOGGER.info(String.format(
                    "[LOGGING NRG/ID_UDIEN] saveDepositoAndElaborazione - Prima di entityManager.merge(deposito). IDCAT: %d, Deposito.nrg: %s",
                    idCat, deposito.getNrg()));
            if (deposito.getRicorso() != null) {
                LOGGER.info(String.format(
                        "[LOGGING NRG/ID_UDIEN] saveDepositoAndElaborazione - Prima di entityManager.merge(deposito). IDCAT: %d, Deposito.getRicorso().getNrg(): %s",
                        idCat, deposito.getRicorso().getNrg()));
            } else {
                LOGGER.info(String.format(
                        "[LOGGING NRG/ID_UDIEN] saveDepositoAndElaborazione - Prima di entityManager.merge(deposito). IDCAT: %d, Deposito.getRicorso() is NULL.",
                        idCat));
            }
            deposito.setIntroduttivo(elaborazioneDeposito.getNumeroRicorso() == null);
            entityManager.merge(deposito);

            elaborazioneDeposito.setDeposito(deposito);
            elaborazioneDeposito.setDataUltimaModifica(new Date().getTime());

            entityManager.merge(elaborazioneDeposito);
            LOGGER.info("Salvataggio elaborazione deposito completata");

            return elaborazioneDeposito;
        } catch (Exception e) {
            LOGGER.error("Errore durante il salvataggio dell'elaborazione del deposito. idCat:" + idCat);
            throw new ElaborazioneException("Errore nel salvataggio dell'elaborazione", RestErrorCodeEnum.ELABORAZIONE_DEPOSITO_SAVE_ERROR,
                    e);
        }
    }
    //
    // private List<Deposito> getDepositiCompl(EntityManager entityManager, String refId) {
    // Collection<String> c = new ArrayList();
    // List<Deposito> listaComplementari = entityManager
    // .createNativeQuery("select * from CSPBACKEND_DEPOSITI where refid = '" + refId + "' and tipo = 'DepositoComplementare'",
    // Deposito.class)
    // .getResultList();
    // return listaComplementari;
    // }

    public void eliminaElaborazioneDeposito(EntityManager entityManager, String id, boolean ripristina) throws CspBackendException {
        if (id == null) {
            throw new CspBackendException("Riferimento deposito non corretto. id deposito null", RestErrorCodeEnum.ID_DEPOSITO_NULL, 433);
        }
        LOGGER.info("Eliminazione elaborazione deposito " + id);
        try {
            ElaborazioneDeposito elaborazioneDeposito = entityManager.find(ElaborazioneDeposito.class, id);
            Deposito deposito = elaborazioneDeposito.getDeposito();
            if (deposito == null) {
                throw new ElaborazioneException("Dati deposito non trovati", RestErrorCodeEnum.DEPOSITO_NOT_FOUND);
            }
            if (Deposito.Stato.ACCETTATO.equals(deposito.getStato()) || Deposito.Stato.RIFIUTATO.equals(deposito.getStato())) {
                LOGGER.error("Impossibile salvare: il deposito risulta già accettato/rifiutato. idCat:"
                        + elaborazioneDeposito.getDeposito().getIdCat() + " - stato:" + deposito.getStato());
                throw new ElaborazioneException("Impossibile salvare: il deposito risulta già accettato/rifiutato",
                        RestErrorCodeEnum.DEPOSITO_ACCETTATO_RIFIUTATO);
            }

            boolean depositoToSave = false;

            if (ripristina) {
                // ripristina il tipo originario se il tipo è stato modificato
                if (deposito.getTipoModificato() != null) {
                    deposito.setTipo(deposito.getTipoModificato());
                    deposito.setTipoModificato(null);
                    depositoToSave = true;
                }
            }

            RicorsoSic ricorso = deposito.getRicorso();
            // rimuovo l'associazione tra deposito e ricorso che potrebbe essere stata aggiunta in fase di elaborazione deposito
            if (ricorso != null) {
                deposito.setRicorso(null);
                depositoToSave = true;

            }
            if (depositoToSave) {
                entityManager.merge(deposito);
            }

            entityManager.remove(elaborazioneDeposito);
        } catch (Throwable e) {
            LOGGER.error("Errore durante l'eliminazione dell'elaborazione. idElabDeposito:" + id);
            throw new ElaborazioneException("Errore nell'eliminazione dell'elaborazione",
                    RestErrorCodeEnum.ELABORAZIONE_DEPOSITO_DELETE_ERROR, e);
        }
    }

    private byte[] downloadAtto(Long catId) {
        ServiziAttoInformatico service = serviceBuilder.getAttoInformaticoService();
        IdAtto idAtto = new IdAtto();
        IdFascicolo idFascicolo = new IdFascicolo();
        idFascicolo.setRegistro(System.getProperty("gestorelocale.registro"));
        idFascicolo.setUfficio(System.getProperty("gestorelocale.ufficio"));
        idAtto.setFascicolo(idFascicolo);
        idAtto.setId(String.valueOf(catId));
        return service.download(idAtto, false);
    }

    private byte[] downloadAttoContent(Long catId) throws Throwable {
        List<String> rts = getContentDeposito(serviceBuilder, catId.toString(), "ATTO");
        ServiziAttoInformatico service = serviceBuilder.getAttoInformaticoService();
        IdAtto idAtto = new IdAtto();
        IdFascicolo idFascicolo = new IdFascicolo();
        idFascicolo.setRegistro(System.getProperty("gestorelocale.registro"));
        idFascicolo.setUfficio(System.getProperty("gestorelocale.ufficio"));
        idAtto.setId(rts.get(0));
        idAtto.setFascicolo(idFascicolo);

        final byte[] content = service.download(idAtto, true);
        return service.download(idAtto, false);
    }

    public static List<String> getContentDeposito(ServiceBuilder serviceBuilder, String idDeposito, String tipo) throws Throwable {
        List<String> result = new ArrayList<>();
        if (idDeposito == null) {
            throw new CspBackendException("Riferimento deposito non corretto. idDeposito null", RestErrorCodeEnum.ID_DEPOSITO_NULL, 433);
        }
        LogEsiti service = serviceBuilder.getLogEsitiService();
        EnvSummaryType[] events = service.querySummary(new String[] { idDeposito }).getEnvSummary();
        if (events == null || events.length == 0) {
            LOGGER.error("Deposito non trovato sul GL. idDeposito:" + idDeposito);
            throw new CspBackendException("Deposito non trovato sul GL", RestErrorCodeEnum.GL_DEPOSITO_NOT_FOUND, 433);
        }
        EnvSummaryType ev = events[0];
        if (ev.getContents() != null && ev.getContents().getContentSummary() != null) {
            for (ContentSummaryType content : ev.getContents().getContentSummary()) {
                if (content.getDocType().equals(tipo)) {
                    result.add(content.getCatId());
                }
            }
        }
        return result;

    }

    private TipoProvvedimentoMagistrato mapTipiPubblicazioneMagistrato(String tipo) {

        if ("Ordinanza".equals(tipo)) {
            return TipoProvvedimentoMagistrato.Ordinanza;
        } else if ("Sentenza".equals(tipo)) {
            return TipoProvvedimentoMagistrato.Sentenza;
        } else {
            return null;
        }
    }

    private Boolean map10(String fieldSINO) {
        if ("1".equals(fieldSINO)) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }

    private Boolean mapSINO(String fieldSINO) {
        if ("SI".equals(fieldSINO)) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }

}
