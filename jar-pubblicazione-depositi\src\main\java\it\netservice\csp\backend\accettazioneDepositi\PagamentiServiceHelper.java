package it.netservice.csp.backend.accettazioneDepositi;

import java.net.MalformedURLException;
import java.rmi.RemoteException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;
import java.util.regex.Pattern;

import javax.xml.rpc.ServiceException;

import it.netservice.csc.backend.ws.pagamenti.ServiziSupportoPagamenti;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.ext.service.ServiceBuilder;
import it.netservice.csp.backend.ws.pagamenti.types.Ricevuta;
import it.netservice.csp.backend.ws.pagamenti.types.RicevutaF23;
import it.netservice.csp.backend.ws.pagamenti.types.RicevutaMarcaBollo;
import it.netservice.csp.backend.ws.pagamenti.types.RicevutaPoste;

/**
 *
 * <AUTHOR>
 */
public abstract class PagamentiServiceHelper {

    private static final Pattern PATTERN_AGENZIA = Pattern.compile("^\\d{2}\\/\\d{3}$");
    private static final Pattern PATTERN_VCY = Pattern.compile("^\\d{4}$");

    public static void associaPagamento(ServiceBuilder serviceBuilder, DatiAssociazionePagamento datiPagamento, String codiceUfficio)
            throws CspBackendException, ParseException, RemoteException, MalformedURLException, ServiceException {
        if (datiPagamento.getTipoPagamento() == null) {
            throw new CspBackendException("Tipologia del pagamento non specificata");
        }
        ServiziSupportoPagamenti pagamenti = serviceBuilder.getSupportoPagamentiService();
        Calendar cal = Calendar.getInstance();
        if (datiPagamento.getDataPagamento() != null) {
            // assurdo passaggio di date tra UTC e local date altrimenti jaxws passa l'ora in UTC e perde un po' di ore
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            dateFormat.setTimeZone(TimeZone.getTimeZone("Europe/Rome"));
            String stringDate = dateFormat.format(datiPagamento.getDataPagamento());
            SimpleDateFormat localDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            cal.setTime(localDateFormat.parse(stringDate));
        }
        switch (datiPagamento.getTipoPagamento()) {
            case BOLLETTINO_POSTALE: {
                String[] agenziaVcy = datiPagamento.getCodicePagamento().split("-");
                RicevutaPoste ricevuta = new RicevutaPoste(datiPagamento.getImporto(), datiPagamento.getCausale(), "CASS", cal, "CASS",
                        datiPagamento.getIdFascicolo(), codiceUfficio, null, agenziaVcy[0], agenziaVcy[1]);
                pagamenti.associaPagamentoTradizionale(codiceUfficio, "CASS", datiPagamento.getIdFascicolo(),
                        new RicevutaPoste[] { ricevuta }, datiPagamento.getCausale());
            }
                break;
            case F23: {
                RicevutaF23 ricevuta = new RicevutaF23(datiPagamento.getImporto(), datiPagamento.getCausale(), "CASS", cal, "CASS",
                        datiPagamento.getIdFascicolo(), codiceUfficio, null, datiPagamento.getCodicePagamento());
                pagamenti.associaPagamentoTradizionale(codiceUfficio, "CASS", datiPagamento.getIdFascicolo(),
                        new RicevutaF23[] { ricevuta }, datiPagamento.getCausale());
            }
                break;
            case MARCA_DA_BOLLO: {
                RicevutaMarcaBollo ricevuta = new RicevutaMarcaBollo(datiPagamento.getImporto(), datiPagamento.getCausale(), "CASS", cal,
                        "CASS", datiPagamento.getIdFascicolo(), codiceUfficio, null, datiPagamento.getCodicePagamento());
                pagamenti.associaPagamentoTradizionale(codiceUfficio, "CASS", datiPagamento.getIdFascicolo(),
                        new RicevutaMarcaBollo[] { ricevuta }, datiPagamento.getCausale());
            }
                break;
            case PAGAMENTO_TELEMATICO: {
                pagamenti.associaPagamentoTelematico(codiceUfficio, "CASS", datiPagamento.getIdFascicolo(), datiPagamento.getPagatore(),
                        datiPagamento.getImporto(), datiPagamento.getCodicePagamento());
                break;
            }
            default: {
                throw new CspBackendException("Tipo pagamento tradizionale non riconosciuto");
            }
        }
    }

    public static String verificaBollettinoPostale(RicevutaPoste boll) {
        boolean agenziaOk = PATTERN_AGENZIA.matcher(boll.getCodAgenzia()).find();
        boolean vcyOk = PATTERN_VCY.matcher(boll.getCodVcy()).find();
        if (!agenziaOk) {
            return "Formato codice agenzia non valido";
        }
        if (!vcyOk) {
            return "Formato codice VCY non valido";
        }
        return null;
    }

    public static String verificaMarcaBollo(RicevutaMarcaBollo marca) {
        if (marca.getCodMarca().length() > 14) {
            return "Formato codice marca da bollo non valido";
        }
        return null;
    }

    public static String verificaF23(RicevutaF23 f23) {
        if (f23.getCodF23().length() < 17) {
            return "Formato codice F23 non valido";
        }
        return null;
    }

    public static class DownloadCrsResponse {

        private String crs;

        public DownloadCrsResponse() {
        }

        public String getCrs() {
            return crs;
        }

        public void setCrs(String crs) {
            this.crs = crs;
        }

    }

    public static class DatiAssociazionePagamento {

        private String idFascicolo;
        private Float importo;
        private String pagatore;
        private String codicePagamento;
        private String causale;
        private Date dataPagamento;
        TipoPagamento tipoPagamento;

        public DatiAssociazionePagamento() {
        }

        public String getIdFascicolo() {
            return idFascicolo;
        }

        public void setIdFascicolo(String idFascicolo) {
            this.idFascicolo = idFascicolo;
        }

        public Float getImporto() {
            return importo;
        }

        public void setImporto(Float importo) {
            this.importo = importo;
        }

        public String getPagatore() {
            return pagatore;
        }

        public void setPagatore(String pagatore) {
            this.pagatore = pagatore;
        }

        public String getCodicePagamento() {
            return codicePagamento;
        }

        public void setCodicePagamento(String codicePagamento) {
            this.codicePagamento = codicePagamento;
        }

        public String getCausale() {
            return causale;
        }

        public void setCausale(String causale) {
            this.causale = causale;
        }

        public Date getDataPagamento() {
            return dataPagamento;
        }

        public void setDataPagamento(Date dataPagamento) {
            this.dataPagamento = dataPagamento;
        }

        public TipoPagamento getTipoPagamento() {
            return tipoPagamento;
        }

        public void setTipoPagamento(TipoPagamento tipoPagamento) {
            this.tipoPagamento = tipoPagamento;
        }

    }

    public static class DatiVerificaPagamento {

        String idFascicolo;
        String idPagamento;
        Date dataPagamento;
        TipoPagamento tipoPagamento;

        public DatiVerificaPagamento() {
        }

        public String getIdFascicolo() {
            return idFascicolo;
        }

        public void setIdFascicolo(String idFascicolo) {
            this.idFascicolo = idFascicolo;
        }

        public String getIdPagamento() {
            return idPagamento;
        }

        public void setIdPagamento(String idPagamento) {
            this.idPagamento = idPagamento;
        }

        public TipoPagamento getTipoPagamento() {
            return tipoPagamento;
        }

        public void setTipoPagamento(TipoPagamento tipoPagamento) {
            this.tipoPagamento = tipoPagamento;
        }

        public Date getDataPagamento() {
            return dataPagamento;
        }

        public void setDataPagamento(Date dataPagamento) {
            this.dataPagamento = dataPagamento;
        }

    }

    public static class VerificaResponse {

        private Ricevuta datiPagamento;
        private boolean disponibile;
        private String message;

        public VerificaResponse() {
        }

        public VerificaResponse(Ricevuta datiPagamento, boolean disponibile) {
            this.datiPagamento = datiPagamento;
            this.disponibile = disponibile;
        }

        public VerificaResponse(Ricevuta datiPagamento, boolean disponibile, String message) {
            this.datiPagamento = datiPagamento;
            this.disponibile = disponibile;
            this.message = message;
        }

        public Ricevuta getDatiPagamento() {
            return datiPagamento;
        }

        public void setDatiPagamento(Ricevuta datiPagamento) {
            this.datiPagamento = datiPagamento;
        }

        public boolean isDisponibile() {
            return disponibile;
        }

        public void setDisponibile(boolean disponibile) {
            this.disponibile = disponibile;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }

}
