package it.netservice.csp.backend.service.depositi;

import static org.apache.poi.ss.usermodel.IndexedColors.BLACK;

import java.io.*;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.ws.rs.core.Response;
import javax.ws.rs.core.StreamingOutput;

import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.DepositoException;
import it.netservice.csp.backend.utils.ExcelGenerator;
import it.netservice.penale.model.sic.Deposito;
import it.netservice.penale.model.sic.csp.DepositoView;

public class DepositiService {
    private static final Logger LOGGER = Logger.getLogger(DepositiService.class);

    private static final String contentDispositionHeader = String.format("attachment; filename=\"%s\"", "esportazione_depositi.xlsx");

    private static final String fontFamily = "Arial";
    private static final short fontSizeTableResult = 10;
    private static final short fontSizeTableCriteria = 8;
    private static final short getRowHeightTableCriteria = 300;
    private static final short rowHeightTableResult = 500;
    private static final short borderSizeTableCriteria = 1;
    private static final short borderSizeTableResult = 1;

    public Response esportaDepositi(List<DepositoView> depositi) throws CspBackendException {
        if (depositi == null) {
            LOGGER.warn("Nessuna lista da esportare. Lista depositi null");
            return Response.ok().build();
        }
        LOGGER.info("Avvio procedura di export dei depositi trovati. Totale depositi: " + depositi.size());
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            LOGGER.info("Totale depositi da esportare: " + depositi.size());
            esportaDepositi(depositi, workbook);
            File f = new File("esportazione_depositi.xlsx");
            f.deleteOnExit();
            FileOutputStream outputStream = new FileOutputStream(f);
            workbook.write(outputStream);
            final byte[] content = Files.readAllBytes(f.toPath());
            StreamingOutput s = new StreamingOutput() {
                @Override
                public void write(OutputStream output) throws IOException {
                    output.write(content);
                }
            };
            LOGGER.info("Procedura dei depositi completata");
            return Response.ok(s).header("Content-Disposition", contentDispositionHeader)
                    .header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet").build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            throw new DepositoException("Errore durante la generazione del file", RestErrorCodeEnum.EXPORT_DEPOSITI_ERROR, e);
        }
    }

    private void esportaDepositi(List<DepositoView> depositi, XSSFWorkbook workbook) {
        Sheet sheet = workbook.createSheet("Depositi");
        workbook.setSheetName(workbook.getSheetIndex(sheet), "Esportazione");
        ExcelGenerator excelGeneratorGenerali = new ExcelGenerator(sheet, workbook, 0);
        setExcelTitle(excelGeneratorGenerali, "DEPOSITI");
        setCriteriImpostatiTableFromList(excelGeneratorGenerali);
        setDepositiTable(excelGeneratorGenerali, depositi);
        createFooter(excelGeneratorGenerali);
    }

    private void setCriteriImpostatiTableFromList(ExcelGenerator excelGenerator) {
        excelGenerator.generateSpacer(1);
        excelGenerator.setFont(fontFamily, BLACK.getIndex(), fontSizeTableCriteria, false);
        excelGenerator.createCellStyle(IndexedColors.WHITE.getIndex(), (short) 1, borderSizeTableCriteria);
        excelGenerator.setFont(fontFamily, BLACK.getIndex(), fontSizeTableCriteria, true);
        excelGenerator.createCellStyle(IndexedColors.GREY_25_PERCENT.getIndex(), (short) 1, borderSizeTableCriteria);
        excelGenerator.generateTableFromRows(2, getRowHeightTableCriteria,
                Arrays.asList("Data estrazione", new SimpleDateFormat("dd/MM/yyyy").format(new Date())));
    }

    private void setDepositiTable(ExcelGenerator excelGenerator, List<DepositoView> depositi) {
        excelGenerator.generateSpacer(1);
        excelGenerator.setFont(fontFamily, BLACK.getIndex(), fontSizeTableResult, true);
        excelGenerator.createCellStyle(IndexedColors.WHITE.getIndex(), (short) 1, borderSizeTableResult);
        List<String> nomiColonne = Arrays.asList("Sezione udienza", "Data udienza", "Tipo udienza", "Collegio udienza", "NRG",
                "Depositante", "Atto", "Presidente Collegio", "Data deposito", "Oscuramento sic", "Oscuramento DESK/CSP",
                "Preso in carico da", "Stato deposito", "Data lavorazione", "N. Racc. Gen");
        excelGenerator.generateTableFromRows(nomiColonne.size(), rowHeightTableResult, nomiColonne);
        excelGenerator.setFont(fontFamily, BLACK.getIndex(), (short) 7, false);
        excelGenerator.createCellStyle(IndexedColors.WHITE.getIndex(), (short) 1, borderSizeTableResult);
        excelGenerator.generateTableFromRows(nomiColonne.size(), rowHeightTableResult, createTableDepositiFromList(depositi));
    }

    private List<String>[] createTableDepositiFromList(List<DepositoView> depositoViews) {

        List<String>[] table = new List[depositoViews.size()];
        for (int i = 0; i < depositoViews.size(); i++) {
            DepositoView deposito = depositoViews.get(i);
            String nrg = getStringIfNotNull(deposito.getNumeroRicorso() + "/" + deposito.getAnnoRicorso());
            String dataUdienza = "";
            String dataDeposito = "";
            String dataLavorazione = "";
            String tipo = "";
            String stato = "";
            try {
                dataUdienza = getStringIfNotNull(new SimpleDateFormat("dd/MM/yyyy").format(deposito.getDataUdienza()));
            } catch (Exception e) {
                dataUdienza = "";
            }

            try {
                dataDeposito = getStringIfNotNull(new SimpleDateFormat("dd/MM/yyyy hh:mm").format(deposito.getDataDeposito()));
            } catch (Exception e) {
                dataDeposito = "";
            }

            try {
                dataLavorazione = getStringIfNotNull(new SimpleDateFormat("dd/MM/yyyy hh:mm").format(
                        deposito.getStato() == Deposito.Stato.RIFIUTATO ? deposito.getDataRifiuto() : deposito.getDataAccettazione()));
            } catch (Exception e) {
                dataLavorazione = "";
            }
            String oscuramentoDeskCsp = deposito.getOscuramentoDeskCsp() == 1 ? "sì" : "no";
            String oscuramentoSic = deposito.getOscuramentoSic() == 1 ? "sì" : "no";

            if (deposito.getTipo().equalsIgnoreCase("MinutaSentenza")) {
                tipo = "Minuta di Sentenza";
            } else if (deposito.getTipo().equalsIgnoreCase("MinutaOrdinanza")) {
                tipo = "Minuta di Ordinanza";
            } else {
                tipo = deposito.getTipo();
            }

            if (deposito.getStato().equals(Deposito.Stato.ACCETTATO)) {
                if (deposito.getTipo().equalsIgnoreCase("MinutaSentenza") || deposito.getTipo().equalsIgnoreCase("MinutaOrdinanza")) {
                    stato = "Accettato";
                } else if (deposito.getTipo().equalsIgnoreCase("Sentenza") || deposito.getTipo().equalsIgnoreCase("Ordinanza")) {
                    stato = "Pubblicato";
                }
            } else if (deposito.getStato().equals(Deposito.Stato.RIFIUTATO)) {
                stato = "Rifiutato";
            } else if (deposito.getStato().equals(Deposito.Stato.NEW)) {
                stato = "Da accettare";
            } else if (deposito.getStato().equals(Deposito.Stato.ERROREPUBBLICAZIONE)) {
                stato = "Errore di pubblicazione";
            } else if (deposito.getStato().equals(Deposito.Stato.PUBBLICATO)) {
                stato = "Pubblicato";
            } else {
                stato = deposito.getStato().name();
            }

            table[i] = Arrays.asList(getStringIfNotNull(deposito.getSezioneUdienza()), dataUdienza,
                    getStringIfNotNull(deposito.getTipoUdienza() == null ? null : deposito.getTipoUdienza()),
                    getStringIfNotNull(deposito.getAula()), nrg, getStringIfNotNull(deposito.getDepositante()), getStringIfNotNull(tipo),
                    getStringIfNotNull(deposito.getPresCollegio()), dataDeposito, oscuramentoSic, oscuramentoDeskCsp,
                    getStringIfNotNull(deposito.getUtenteInCarico()), getStringIfNotNull(stato), dataLavorazione,
                    getStringIfNotNull(deposito.getNumRaccGenerale()));
        }
        return table;
    }

    private <T extends Serializable> String getStringIfNotNull(T value) {
        if (value == null)
            return "";
        return String.valueOf(value);
    }

    private void createFooter(ExcelGenerator excelGenerator) {
        excelGenerator.setDefaultStyle();
    }

    private void setExcelTitle(ExcelGenerator excelGenerator, String title) {
        excelGenerator.setFont(fontFamily, BLACK.getIndex(), (short) 12, true);
        excelGenerator.createCellStyle(IndexedColors.WHITE.getIndex(), (short) 1, (short) 0);
        excelGenerator.generateSimpleRow(title);
    }
}
