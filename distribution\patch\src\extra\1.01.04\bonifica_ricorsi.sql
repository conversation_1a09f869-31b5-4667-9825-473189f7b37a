

-- query estrazione dei ricorsi rinviati con data minuta valorizzata erroneamente su cui va impostata la data minuta a null



SELECT
	DISTINCT ptr.ID_RICUDIEN,
	ptr.NRG,
	ptr2.NRGREALE,
	pte.ESITO,
	ptr.LASTUDIE,
	pts.*
FROM
	PENALE_T_RICUDIEN ptr
JOIN PENALE_T_RICORSO ptr2 ON
	PTR.NRG = PTR2.NRG
JOIN PENALE_T_ESITO pte ON
	ptr.ID_RICUDIEN = PTE.ID_RICUDIEN
JOIN PENALE_T_ESITOSENT pte2 ON
	pte2.ID_ESITO = pte.ID_ESITO
LEFT JOIN PENALE_T_SENTENZA pts ON
	pts.ID_SENT = pte2.ID_SENT
WHERE
	PTE.ESITO = 1425
	AND pts.DATAMINUTA IS NOT NULL;




-- Script di update a null della data minuta  per i ricorsi rinviati
-- valorizzare (XXXXXX,XXXXXX ) con i NRG recuperati dalla query precedente.




	UPDATE
	MIGRA.PENALE_T_SENTENZA x
SET
	x.DEPOSITO_TELEMATICO = 0,
	x.DATAMINUTA = NULL
WHERE
	x.ID_SENT IN (
	SELECT
		DISTINCT pte2.ID_SENT
		--count(DISTINCT PTR.ID_RICUDIEN)
	FROM
		PENALE_T_RICUDIEN ptr
	JOIN PENALE_T_RICORSO ptr2 ON
		PTR.NRG = PTR2.NRG
	JOIN PENALE_T_ESITO pte ON
		ptr.ID_RICUDIEN = PTE.ID_RICUDIEN
	JOIN PENALE_T_ESITOSENT pte2 ON
		pte2.ID_ESITO = pte.ID_ESITO
	WHERE
		ptr.NRG IN (XXXXXX,XXXXXX )
		AND PTE.ESITO = 1425);



-- estrapola i ricorsi  accettati dal CSP che non hanno  data minuta valorizzata
-- valorizzare (XXXXXX,XXXXXX ) con i NRG recuperati dalla query precedente.


	SELECT
	DISTINCT ptr.ID_RICUDIEN,
	ptr.NRG,
	ptr2.NRGREALE,
	pte.ESITO,
	ptr.LASTUDIE,
	pts.DATAMINUTA
FROM
	PENALE_T_RICUDIEN ptr
JOIN PENALE_T_RICORSO ptr2 ON
	PTR.NRG = PTR2.NRG
JOIN PENALE_T_ESITO pte ON
	ptr.ID_RICUDIEN = PTE.ID_RICUDIEN
JOIN PENALE_T_ESITOSENT pte2 ON
	pte2.ID_ESITO = pte.ID_ESITO
JOIN PENALE_T_SENTENZA pts ON
	pts.ID_SENT = pte2.ID_SENT
WHERE pts.DATAMINUTA IS NULL
	AND
	 ptr.NRG IN (XXXXXX,XXXXXX )
	AND PTE.ESITO = 1423;







-- query di aggiornamento dei ricorsi accettati con data minuta non valorizzata per valorizzarla correttamente
-- valorizzare (XXXXXX,XXXXXX ) con i NRG recuperati dalla query precedente.
UPDATE
    MIGRA.PENALE_T_SENTENZA x
SET
    x.DEPOSITO_TELEMATICO = 1,
    x.DATAMINUTA = (
        SELECT
            trunc(
                    CAST (
                            from_tz (
                                    CAST ( ( DATE '1970-01-01' + NUMTODSINTERVAL( MIN(cd.datadeposito) / 1000, 'SECOND' ) ) AS timestamp ), 'UTC'
                                ) AT time ZONE 'Europe/Rome' AS DATE
                        )
                ) AS TRUNCATED_DATE
        FROM
            PENALE_T_RICUDIEN ptr
                JOIN PENALE_T_RICORSO ptr2 ON
                    PTR.NRG = PTR2.NRG
                JOIN PENALE_T_ESITO pte ON
                    ptr.ID_RICUDIEN = PTE.ID_RICUDIEN
                JOIN PENALE_T_ESITOSENT pte2 ON
                    pte2.ID_ESITO = pte.ID_ESITO
                JOIN PENALE_T_SENTENZA pts ON
                    pts.ID_SENT = pte2.ID_SENT
                JOIN CSPBACKEND_DEPOSITI cd ON
                    cd.NRG = ptr.NRG
        WHERE
            pts.DATAMINUTA IS NULL
          AND
                pts.ID_SENT = x.ID_SENT
          AND PTE.ESITO = 1423)
WHERE
        x.ID_SENT IN (
        SELECT
            DISTINCT pte2.ID_SENT
        FROM
            PENALE_T_RICUDIEN ptr
                JOIN PENALE_T_RICORSO ptr2 ON
                    PTR.NRG = PTR2.NRG
                JOIN PENALE_T_ESITO pte ON
                    ptr.ID_RICUDIEN = PTE.ID_RICUDIEN
                JOIN PENALE_T_ESITOSENT pte2 ON
                    pte2.ID_ESITO = pte.ID_ESITO
                JOIN PENALE_T_SENTENZA pts ON
                    pts.ID_SENT = pte2.ID_SENT
        WHERE
            pts.DATAMINUTA IS NULL
          AND
                ptr.NRG IN (XXXXXX, XXXXXX )
          AND PTE.ESITO = 1423);


-- query di verifica dell'update precedente  e non deve estrarre i fascicoli appena aggiornati :

SELECT
	DISTINCT ptr.ID_RICUDIEN,
	ptr.NRG,
	ptr2.NRGREALE,
	pte.ESITO,
	ptr.LASTUDIE,
	pts.DATAMINUTA
FROM
	PENALE_T_RICUDIEN ptr
JOIN PENALE_T_RICORSO ptr2 ON
	PTR.NRG = PTR2.NRG
JOIN PENALE_T_ESITO pte ON
	ptr.ID_RICUDIEN = PTE.ID_RICUDIEN
JOIN PENALE_T_ESITOSENT pte2 ON
	pte2.ID_ESITO = pte.ID_ESITO
JOIN PENALE_T_SENTENZA pts ON
	pts.ID_SENT = pte2.ID_SENT
WHERE
	 ptr.NRG IN (XXXXXX, XXXXXX )
	AND PTE.ESITO = 1423;
