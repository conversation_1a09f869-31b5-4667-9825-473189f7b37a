<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:apachesoap="http://xml.apache.org/xml-soap" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:y="http://www.giustizia.it/gl/sgr/cassazione/gestione/atti" xmlns:types="http://www.giustizia.it/gl/sgr/cassazione/gestione/atti/types" targetNamespace="http://www.giustizia.it/gl/sgr/cassazione/gestione/atti">
    <types>
	<xs:schema xmlns="http://www.giustizia.it/gl/sgr/cassazione/gestione/atti/types" targetNamespace="http://www.giustizia.it/gl/sgr/cassazione/gestione/atti/types">
	    <xs:complexType name="AttoDaAcquisire">
		<xs:all>
		    <xs:element name="idCatFascicolo" type="xs:string"/>
		    <xs:element name="nomeFile" type="xs:string"/>
		    <xs:element name="contenuto" type="apachesoap:DataHandler"/>
		</xs:all>
	    </xs:complexType>
	    <xs:complexType name="IdFascicolo">
		<xs:all>
		    <xs:element name="anno" type="xs:string" nillable="true"/>
		    <xs:element name="numero" type="xs:string" nillable="true"/>
		    <xs:element name="subProcedimento" type="xs:string" nillable="true"/>
		    <xs:element name="registro" type="xs:string" nillable="false"/>
		    <xs:element name="ufficio" type="xs:string" nillable="false"/>
		    <xs:element name="dataUltimaModifica" type="xs:dateTime" nillable="true"/>
		    <xs:element name="attorePrincipale" type="xs:string" nillable="true"/>
		    <xs:element name="convenutoPrincipale" type="xs:string" nillable="true"/>
		    <xs:element name="idSgr" type="xs:string" nillable="true"/>
		    <xs:element name="idRepository" type="xs:string" nillable="true"/>
		</xs:all>
	    </xs:complexType>
	    <xs:complexType name="IdAtto">
		<xs:all>
		    <xs:element name="id" type="xs:string" nillable="true"/>
		    <xs:element name="fascicolo" type="IdFascicolo" nillable="false"/>
		</xs:all>
	    </xs:complexType>
	</xs:schema>
    </types>

    <message name="acquisisciAttoReq">
	<part name="contenutiScan" type="types:AttoDaAcquisire"/>
    </message>
    <message name="acquisisciAttoRes">
	<part name="result" type="types:IdAtto"/>
    </message>

    <portType name="ServiziGestioneAtti">

	<operation name="acquisisciAtto">
	    <input message="y:acquisisciAttoReq"/>
	    <output message="y:acquisisciAttoRes"/>
	</operation>

    </portType>
    <binding name="ServiziGestioneAttiSOAPBinding" type="y:ServiziGestioneAtti">

	<soap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http"/>
	<operation name="acquisisciAtto">
	    <input>
		<soap:body use="encoded" namespace="http://www.giustizia.it/gl/sgr/cassazione/gestione/atti"/>
	    </input>
	    <output>
		<soap:body use="encoded" namespace="http://www.giustizia.it/gl/sgr/cassazione/gestione/atti"/>
	    </output>
	</operation>

    </binding>
    <service name="WsServiziGestioneAtti">
	<port name="ServiziGestioneAttiSOAPPort" binding="y:ServiziGestioneAttiSOAPBinding">
	    <soap:address location="http://localhost:8080/wasp/rpcrouter"/>
	</port>
    </service>
</definitions>
