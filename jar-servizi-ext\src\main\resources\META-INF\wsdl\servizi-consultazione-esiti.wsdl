<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tns="urn:LogEsiti" targetNamespace="urn:LogEsiti">
  <types>
    <xs:schema xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="urn:LogEsiti" targetNamespace="urn:LogEsiti">
      <xs:import namespace="http://schemas.xmlsoap.org/soap/encoding/" schemaLocation="soap-encoding.xsd"/>
      <xs:import namespace="urn:LogEsiti" schemaLocation="consultazione-esiti-types.xsd"/>
    </xs:schema>
  </types>
  <message name="queryEventsRequest">
    <part name="evtClass" type="xs:string"/>
    <part name="evtCode" type="xs:string"/>
    <part name="fromDate" type="xs:dateTime"/>
    <part name="toDate" type="xs:dateTime"/>
  </message>
  <message name="queryEventsResponse">
    <part name="return" type="tns:eventList"/>
  </message>
  <message name="queryCatIdEventsRequest">
    <part name="catId" type="xs:string"/>
  </message>
  <message name="envSummaryRequest">
    <part name="docClass" type="xs:string"/>
    <part name="status"   type="xs:string"/>
    <part name="register" type="xs:string"/>
    <part name="fromDate" type="xs:dateTime"/>
    <part name="toDate"   type="xs:dateTime"/>
    <part name="cuda"     type="tns:CudaIdType"/>
    <part name="onlyUrgente"   type="xs:boolean"/>
  </message>
  <message name="envSummaryResponse">
    <part name="return" type="tns:envSummaryList"/>
  </message>
  <message name="envSummaryArrayRequest">
    <part name="catIdList" type="tns:stringArrayType"/>
  </message>
  <message name="querySummaryComplRequest">
    <part name="catId" type="xs:string"/>
    <part name="registro" type="xs:string"/>
    <part name="ufficio" type="xs:string"/>
    <part name="refId" type="xs:string"/>
  </message>
  <portType name="LogEsiti">
    <operation name="queryCatIdEvents">
      <input message="tns:queryCatIdEventsRequest"/>
      <output message="tns:queryEventsResponse"/>
    </operation>
    <operation name="envelopeSummary">
      <input message="tns:envSummaryRequest"/>
      <output message="tns:envSummaryResponse"/>
    </operation>  
    <operation name="querySummary">
      <input message="tns:envSummaryArrayRequest"/>
      <output message="tns:envSummaryResponse"/>
    </operation>
    <operation name="querySummaryCompl">
      <input message="tns:querySummaryComplRequest"/>
      <output message="tns:envSummaryResponse"/>
    </operation>
  </portType>
  <binding name="LogEsitiSOAPBind" type="tns:LogEsiti">
    <soap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http"/>
    <operation name="queryCatIdEvents">
      <input>
        <soap:body use="encoded" namespace="urn:LogEsiti"/>
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:LogEsiti"/>
      </output>
    </operation>
    <operation name="envelopeSummary">
      <soap:operation soapAction="urn:#envelopeSummary"/>
      <input>
        <soap:body use="encoded" namespace="urn:LogEsiti"/>
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:LogEsiti"/>
      </output>
    </operation>
    <operation name="querySummary">
      <soap:operation soapAction="urn:#querySummary"/>
      <input>
        <soap:body use="encoded" namespace="urn:LogEsiti"/>
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:LogEsiti"/>
      </output>
    </operation>
    <operation name="querySummaryCompl">
      <soap:operation soapAction="urn:#querySummaryCompl"/>
      <input>
        <soap:body use="encoded" namespace="urn:LogEsiti"/>
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:LogEsiti"/>
      </output>
    </operation>
  </binding>
  <service name="wsLogEsiti">
    <port name="LogEsitiPort" binding="tns:LogEsitiSOAPBind">
      <soap:address location="http://localhost:8080/wasp/rpcrouter"/>
    </port>
  </service>
</definitions>
