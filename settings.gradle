pluginManagement {
    repositories {
    mavenLocal()
    maven { url 'https://nexuspa.netserv.it/repository/gradle/' }
	maven { url "https://nexuspa.netserv.it/repository/snapshots" }
	maven { url "https://nexuspa.netserv.it/repository/releases" }    
        gradlePluginPortal()
    }
}

rootProject.name = 'csp-backend'

include(':distribution:patch')
include(':war-cspbackend')
include(':jar-servizi-ext')
include(':jar-gestione-sic')
include(':jar-ucu-model')
include(':jar-csp-common')
include(':jar-gestione-pdf')
include(':jar-elaborazione-depositi')
include(':jar-pubblicazione-depositi')
include(':jar-ucu-model')
include(':ear-cspbackend')