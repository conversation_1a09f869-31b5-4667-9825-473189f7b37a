package it.netservice.csp.backend.rest.auth;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.Response;

import org.apache.log4j.Logger;

import sic.common.util.SICEncDec;

import com.auth0.jwt.interfaces.DecodedJWT;

import it.netservice.csp.backend.dto.ErrorTypeEnum;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.excpetion.AuthenticationExcpetion;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.rest.AbstractCspBackendController;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.penale.model.sic.PenaleParam;
import it.netservice.penale.model.sic.Utente;

/**
 * <AUTHOR>
 */
@NoSicAuthService
@Path("/auth")
public class AuthenticationController extends AbstractCspBackendController {

    private static final Logger LOGGER = Logger.getLogger(AuthenticationController.class);

    @Inject
    private TokenManager tokenManager;

    @Inject
    private SecurityUtil securityUtil;

    @POST
    public Response authRequest(@Context HttpServletRequest context, AuthRequestType req) throws CspBackendException {
        if (req.userName == null || (req.pwd == null && req.pwdCry == null)) {
            throw new CspBackendException("Username e/o password non forniti", RestErrorCodeEnum.FIELD_MANDATORY_ERROR, 433);
        }
        LOGGER.info("Richiesta di accesso con username:" + req.userName + ", avvio ricerca utente");
        EntityManager em = emfCaricamento.createEntityManager();
        try {
            if (req.pwd != null && req.pwdCry == null) {
                req.pwdCry = new SICEncDec().goEncrypt(req.pwd);
            }
            Utente ud = null;
            try {
                ud = em.createQuery("FROM Utente ud WHERE upper(ud.identificativo) LIKE upper(:userName) AND ud.password = :pwdCry",
                        Utente.class).setParameter("userName", req.userName).setParameter("pwdCry", req.pwdCry).getSingleResult();
            } catch (NoResultException e) {
                LOGGER.info("Nome utente e/o password errati. Username:" + req.userName);
                throw new CspBackendException("Nome utente e/o password errati", RestErrorCodeEnum.USER_PASS_INVALID, 433,
                        ErrorTypeEnum.WARNING);
            }
            LOGGER.info("Utente trovato, idUtente:" + ud.getIdUtente());

            List<Object[]> list = em
                    .createNativeQuery("SELECT pf.id_funz, pf.livello, c.nome, b.id_utente, b.identificativo, b.ufficio, a.tipo_utente "
                            + " from MIGRA.PENALE_PROFILI_FUNZ pf,"
                            + " MIGRA.PENALE_FUNZIONI c, MIGRA.PENALE_UTENTI b, MIGRA.PENALE_PROFILI p, MIGRA.PENALE_PROFILI_UTENTE a "
                            + " where pf.ID_FUNZ=c.ID_FUNZIONE AND c.id_funzione in (912) "
                            + " AND a.id_profilo = p.id_profilo  AND a.id_utente = b.id_utente AND pf.ID_PROFILO=p.ID_PROFILO AND (a.DATA_FINE IS NULL OR a.DATA_FINE > SYSDATE)"
                            + " AND b.identificativo= '" + ud.getIdentificativo() + "'")
                    .getResultList();

            if (list == null || list.isEmpty()) {
                LOGGER.error("Utente non abilitato alle funzioni del CSP. idUtente:" + ud.getIdUtente());
                throw new CspBackendException("Utente non abilitato alle funzioni del CSP", RestErrorCodeEnum.USER_UNAUTH_CSP, 433);
            }

            Object[] arr = list.get(0); // PUNTO DI ROTTURA DELLA LOGIN

            ud.setAdmin(Arrays.asList("SC", "SP").contains(arr[6]));

            int maxReadOnlyVal = 0;
            for (Object[] element : list) {
                if (((BigDecimal) element[0]).intValue() == 705 && ((BigDecimal) element[1]).intValue() > maxReadOnlyVal) {
                    maxReadOnlyVal = ((BigDecimal) element[1]).intValue();
                }
            }
            ud.setReadOnly(maxReadOnlyVal == 1 ? true : false);

            boolean sentenzaMinuta = em.createNativeQuery(
                    "SELECT * FROM PENALE_PROFILI_FUNZ cpf WHERE " + "cpf.id_profilo= " + ud.getIdProfilo() + " and cpf.id_funz = 38")
                    .getResultList().isEmpty();
            ud.setSentenzaMinuta(!sentenzaMinuta);

            // questo diritto viene usato con improprietà di linguaggio
            // viene usato come accetta e pubblica il provvedimento
            boolean depositoSentenza = em.createNativeQuery(
                    "SELECT * FROM PENALE_PROFILI_FUNZ cpf WHERE " + "cpf.id_profilo= " + ud.getIdProfilo() + " and cpf.id_funz = 41")
                    .getResultList().isEmpty();
            ud.setDepositoSentenza(!depositoSentenza);

            // add come sopra ma con id 42 (che è numero raccolta generale)
            boolean numRaccGenerale = em.createNativeQuery(
                    "SELECT * FROM PENALE_PROFILI_FUNZ cpf WHERE " + "cpf.id_profilo= " + ud.getIdProfilo() + " and cpf.id_funz = 42")
                    .getResultList().isEmpty();
            ud.setNumRaccGenerale(!numRaccGenerale);

            boolean gestionePagamenti = em.createNativeQuery(
                    "SELECT * FROM PENALE_PROFILI_FUNZ cpf WHERE " + "cpf.id_profilo= " + ud.getIdProfilo() + " and cpf.id_funz = 911")
                    .getResultList().isEmpty();
            ud.setGestionePagamenti(!gestionePagamenti);

            boolean verificaDati = em.createNativeQuery(
                    "SELECT * FROM PENALE_PROFILI_FUNZ cpf WHERE " + "cpf.id_profilo= " + ud.getIdProfilo() + " and cpf.id_funz = 912")
                    .getResultList().isEmpty();
            ud.setVerificaDati(!verificaDati);

            boolean verificaPagamenti = em.createNativeQuery(
                    "SELECT * FROM PENALE_PROFILI_FUNZ cpf WHERE " + "cpf.id_profilo= " + ud.getIdProfilo() + " and cpf.id_funz = 913")
                    .getResultList().isEmpty();
            ud.setVerificaPagamenti(!verificaPagamenti);

            boolean ruoloUdienza = em.createNativeQuery(
                    "SELECT * FROM PENALE_PROFILI_FUNZ cpf WHERE " + "cpf.id_profilo= " + ud.getIdProfilo() + " and cpf.id_funz = 914")
                    .getResultList().isEmpty();
            ud.setRuoloUdienza(!ruoloUdienza);

            boolean isConfigEnabled = em.createNativeQuery(
                    "SELECT * FROM PENALE_PROFILI_FUNZ cpf WHERE " + "cpf.id_profilo= " + ud.getIdProfilo() + " and cpf.id_funz = 915")
                    .getResultList().isEmpty();
            ud.setConfigEnabled(!isConfigEnabled);

            if (ud.getDataFineValidita() != null && ud.getDataFineValidita().before(new Date())) {
                LOGGER.error(String.format("L'account associato all'utente %s non e' piu' valido", req.userName));
                throw new AuthenticationExcpetion(String.format("L'account associato all'utente %s non e' piu' valido", req.userName),
                        RestErrorCodeEnum.ACCOUNT_NOT_VALID);
            }

            List<String> sezioniList = em.createNativeQuery(
                    "SELECT DISTINCT (SELECT PP.SIGLA FROM PENALE_PARAM PP WHERE PP.ID_PARAM = PU.ID_UFFICIO AND PP.TIPOTAB='SEZIONI') AS SEZIONE "
                            + "FROM PENALE_PROFILI_UTENTE PU WHERE PU.ID_UTENTE = " + ud.getIdUtente()
                            + " AND PU.ID_UFFICIO IN (88,89,90,91,92,93,94,248) AND PU.DATA_FINE IS NULL ORDER BY SEZIONE")
                    .getResultList();

            Set<String> sigleSezioni = new HashSet<String>(sezioniList);

            ud.setSigleSezioni(sigleSezioni);
            Set<Long> sezioni = new HashSet<>();
            sezioni.add(ud.getSezione().getIdParam());
            if ("S6".equals(ud.getSezione().getSigla())) {
                sezioni.addAll(getSezioniSesta());
            }
            ud.setIdSezioni(sezioni);
            if ("RG".equals(ud.getSezione().getSigla())) {
                ud.setIdSezioneRG(getSezione("RG").getIdParam());
            }
            if ("S6".equals(ud.getSezione().getSigla())) {
                ud.setIdSezioneSesta(getSezione("S6").getIdParam());
            }
            String token = tokenManager.createToken(ud);
            // se req.pwd != null vuol dire che arrivo dal login csc-client, quindi inizializzo la sessione
            if (req.pwd != null) {
                login(context, token);
            }
            return Response.ok(token).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore in fase di autenticazione. Username:" + req.userName);
            throw new AuthenticationExcpetion("Errore in fase di autenticazione.", RestErrorCodeEnum.AUTHENTICATION_GENERIC_ERROR, e);
        } finally {
            em.close();
        }
    }

    @GET
    public String login(@Context HttpServletRequest context, @QueryParam(value = "token") String token) throws CspBackendException {
        DecodedJWT jwt = tokenManager.verifyToken(token);
        if (jwt == null) {
            throw new AuthenticationExcpetion("Token non valido. Token null", RestErrorCodeEnum.TOKEN_NULL);
        }
        securityUtil.initSession(context, jwt);
        return jwt.getToken();
    }

    private List<Long> getSezioniSesta() {

        List<Long> result = new ArrayList<>();
        EntityManager em = emfCaricamento.createEntityManager();
        try {
            List<BigDecimal> bdList = em
                    .createNativeQuery("SELECT id_param from MIGRA.PENALE_PARAM sp WHERE "
                            + "sigla in('S6', 'M1', 'M2', 'M3', 'ML', 'MT') and tipotab = 'SEZIONI' AND sp.tipoTab = 'SEZIONI'")
                    .getResultList();
            for (BigDecimal bd : bdList) {
                result.add(bd.longValue());
            }
            return result;
        } finally {
            em.close();
        }
    }

    private PenaleParam getSezione(String siglaSezione) {
        EntityManager em = emfCaricamento.createEntityManager();
        try {
            SicPenaleRepository repo = new SicPenaleRepository(em);
            return repo.getParam("SEZIONI", siglaSezione);
        } finally {
            em.close();
        }
    }

    public static class AuthRequestType {

        private String userName;
        private String pwd;
        private String pwdCry;

        public AuthRequestType() {
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getPwd() {
            return pwd;
        }

        public void setPwd(String pwd) {
            this.pwd = pwd;
        }

        public String getPwdCry() {
            return pwdCry;
        }

        public void setPwdCry(String pwdCry) {
            this.pwdCry = pwdCry;
        }

    }
}
