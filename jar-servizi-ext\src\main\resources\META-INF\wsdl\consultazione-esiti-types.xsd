<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" 
    xmlns="http://schemas.xmlsoap.org/wsdl/" 
    xmlns:tns="urn:LogEsiti" 
    xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" 
    xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" 
    xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" 
    targetNamespace="urn:LogEsiti">
	<xs:import namespace="http://schemas.xmlsoap.org/soap/encoding/" schemaLocation="soap-encoding.xsd"/>
	<xs:complexType name="eventParamType">
        <xs:sequence>
          <xs:element name="name" type="xs:string"/>
          <xs:element name="value" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="eventType">
        <xs:sequence>
          <xs:element name="evtId" type="xs:string"/>
          <xs:element name="evtClass" type="xs:string"/>
          <xs:element name="evtCode" type="xs:string"/>
          <xs:element name="evtDesc" type="xs:string"/>
          <xs:element name="catId" type="xs:string"/>
          <xs:element name="timeStamp" type="xs:dateTime"/>
          <xs:element name="param" type="tns:eventParamType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="eventList">
        <xs:sequence>
          <xs:element name="event" type="tns:eventType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="extraInfo">
        <xs:sequence>
          <xs:element name="name" type="xs:string"/>
          <xs:element name="value" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="extraInfoList">
        <xs:sequence>
          <xs:element name="extraInfo" type="tns:extraInfo" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="envSummaryType">
        <xs:sequence>
          <xs:element name="catId" type="xs:string"/>
          <xs:element name="sender" type="xs:string"/>
          <xs:element name="received" type="xs:dateTime"/>
          <xs:element name="idMsgGC" type="xs:string"/>
          <xs:element name="idMsgPdA" type="xs:string"/>
          <xs:element name="mainCatId" type="xs:string"/>
          <xs:element name="docClass" type="xs:string"/>
          <xs:element name="docType" type="xs:string"/>
          <xs:element name="destination" type="xs:string" minOccurs="0"/>
          <xs:element name="numPro" type="xs:string" minOccurs="0"/>
          <xs:element name="register" type="xs:string" minOccurs="0"/>
          <xs:element name="status" type="xs:string"/>
          <xs:element name="notes" type="xs:string" nillable="true"/>
          <xs:element name="urgente" type="xs:boolean" minOccurs="0"/>
          <xs:element name="completo" type="xs:boolean" minOccurs="0"/>
          <xs:element name="refId" type="xs:string" minOccurs="0"/>
          <xs:element name="extraInfo" type="tns:extraInfoList" minOccurs="0"/>
          <xs:element name="contents" type="tns:contentSummaryList"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="envSummaryList">
        <xs:sequence>
          <xs:element name="envSummary" type="tns:envSummaryType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="contentSummaryType">
        <xs:sequence>
          <xs:element name="catId" type="xs:string"/>
          <xs:element name="partId" type="xs:string"/>
          <xs:element name="docType" type="xs:string"/>
          <xs:element name="name" type="xs:string"/>
          <xs:element name="status" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="contentSummaryList">
        <xs:sequence>
          <xs:element name="contentSummary" type="tns:contentSummaryType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="stringArrayType">
        <xs:complexContent>
          <xs:restriction base="soapenc:Array">
            <xs:attribute ref="soapenc:arrayType" wsdl:arrayType="xs:string[]"/>
          </xs:restriction>
        </xs:complexContent>
      </xs:complexType>
      <xs:complexType name="CudaIdType">
        <xs:all>
          <xs:element name="areaCode" type="xs:string" nillable="true"/>
          <xs:element name="year"     type="xs:string" nillable="true"/>
          <xs:element name="msgId"    type="xs:string" nillable="true"/>
        </xs:all>
      </xs:complexType>
</xs:schema>