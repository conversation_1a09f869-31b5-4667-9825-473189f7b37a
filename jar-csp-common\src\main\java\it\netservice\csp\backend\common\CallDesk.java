package it.netservice.csp.backend.common;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import okhttp3.Credentials;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;

import org.apache.log4j.Logger;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;

public class CallDesk<T extends Object> {

    private static final Logger LOGGER = Logger.getLogger(CallDesk.class);

    private final OkHttpClient okHttpClient = new OkHttpClient.Builder().connectTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS).readTimeout(30, TimeUnit.SECONDS).build();

    public String callDeskService(String urlParziale, T body, String requestType, List<HeaderOkHttp> headers) throws IOException {
        String cred = Credentials.basic("desk", "servizi-desk");
        String url = System.getProperty("servizi-desk.url") + "/servizi-desk/jsonapi" + urlParziale;
        Request.Builder requestBuilder = new Request.Builder().url(url).addHeader("Authorization", cred);
        if (headers != null) {
            for (HeaderOkHttp header : headers) {
                requestBuilder.addHeader(header.getKey(), header.getValue());
            }
        } else {
            requestBuilder.addHeader("UserCode", "SGR");
        }
        switch (requestType) {
            case "GET":
                return getResponseOkhttp(requestBuilder.get().build()).body().string();
            case "DELETE":
                return getResponseOkhttp(requestBuilder.delete(createRequestBody(body)).build()).body().string();
            case "PUT":
                return getResponseOkhttp(requestBuilder.put(createRequestBody(body)).build()).body().string();
            case "POST":
                return getResponseOkhttp(requestBuilder.post(createRequestBody(body)).build()).body().string();
            default:
                throw new IOException("requestType errato");
        }
    }

    private okhttp3.Response getResponseOkhttp(Request req) throws IOException {
        return okHttpClient.newCall(req).execute();
    }

    private RequestBody createRequestBody(T body) throws JsonProcessingException {
        if (body != null) {
            ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();
            String json = ow.writeValueAsString(body);
            return RequestBody.create(MediaType.get("application/json; charset=utf-8"), json);
        }
        return null;
    }
}
