package it.netservice.csp.backend.sic.service.depositi;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import org.apache.log4j.Logger;

import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.GestioneSicException;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.csp.backend.sic.service.repository.SicRepositoryException;
import it.netservice.penale.model.sic.AttoSic;
import it.netservice.penale.model.sic.ElaborazioneDeposito;
import it.netservice.penale.model.sic.PenaleFunzioni;
import it.netservice.penale.model.sic.RicorsoSic;
import it.netservice.penale.model.sic.Utente;

/**
 * @param <T>
 *            Tipologia deposito da elaborare
 * @param <U>
 *            Tipo repository SIC
 * <AUTHOR>
 */
public abstract class DepositoAbstract<T extends ElaborazioneDeposito, U extends SicPenaleRepository> {

    protected static final Logger LOGGER = Logger.getLogger(DepositoAbstract.class);

    protected final U repo;
    protected final Utente operatore;
    protected final PenaleFunzioni funzione;
    protected final Date oggi;
    protected Long nrg;
    protected Long nrgReale;
    protected RicorsoSic ricorso;
    protected Long idAtto;
    protected AttoSic atto;

    // Questa è una porcata, lo so. Ma non se ne esce. Il problema è che la tipologia di spesa
    // dipende dall'atto depositato. Pertanto per il tipo di deposito ci possono essere solo le tipologie di spesa indicate.
    private final static Map<String, List<String>> SIGLECONTRIBUTI;

    static {
        SIGLECONTRIBUTI = new HashMap<>();
        SIGLECONTRIBUTI.put("Ricorso", Arrays.asList("01", "03", "04", "05", "06", "07", "12", "13", "14", "15"));
        SIGLECONTRIBUTI.put("RicorsoErroreMateriale", Arrays.asList("01", "03", "04", "05", "06", "07", "12", "13", "14", "15"));
        SIGLECONTRIBUTI.put("RevocazioneExArt391ter", Arrays.asList("01", "03", "04", "05", "06", "07", "12", "13", "14", "15"));
        SIGLECONTRIBUTI.put("RevocazioneExArt391quater", Arrays.asList("01", "03", "04", "05", "06", "07", "12", "13", "14", "15"));
        SIGLECONTRIBUTI.put("ControRicorso", Arrays.asList("20", "16", "17", "18", "19"));
        SIGLECONTRIBUTI.put("ControRicorsoIncidentale", Arrays.asList("02", "08", "09", "10", "11"));
        SIGLECONTRIBUTI.put("IntegrazioneSpeseGiustizia", Arrays.asList("01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11",
                "12", "13", "14", "15", "16", "17", "18", "19", "20"));
    }

    public DepositoAbstract(U repo, String operatore) throws GestioneSicException {
        try {
            this.repo = repo;
            this.operatore = repo.findUtenteByUsername(operatore);
            this.funzione = repo.findFunzione("CSP Client");
            this.oggi = Calendar.getInstance(TimeZone.getTimeZone("Europe/Rome")).getTime();
        } catch (SicRepositoryException ex) {
            throw new GestioneSicException("Errore inizializzazione iscrizione deposito", RestErrorCodeEnum.INIT_ISCRIZIONE_DEPOSITO_ERROR,
                    ex);
        }
    }

    protected abstract void accetta(T elabDep) throws CspBackendException;

    protected abstract void rifiuta(T elabDep) throws CspBackendException;

    public RicorsoSic accettaAtto(T elabDep) throws CspBackendException {
        accetta(elabDep);
        return ricorso;
    }

    public RicorsoSic rifiutaAtto(T elabDep) throws CspBackendException {
        rifiuta(elabDep);
        return ricorso;
    }

    /**
     * Questo metodo DEVE essere invocato solo per gli atti di parte
     *
     * @param elabDep
     * @param tipoAtto
     * @throws GestioneSicException
     */
    public void insertAttoSuccessivo(T elabDep, String tipoAtto) throws GestioneSicException {
        LOGGER.info("insertAttoSuccessivo inizio");
        ricorso = repo.getRicorsoByElaborazioneDeposito(elabDep);
        nrg = ricorso.getNrg();
        nrgReale = ricorso.getNrgReale();

        atto = new AttoSic();

        atto.setData(new Date(elabDep.getDeposito().getDataDeposito()));
        atto.setOperatore(operatore.getIdUtente());
        atto.setOggi(new Date());
        atto.setIdFunzioneByPenaleFunzioni(funzione);
        atto.setDataEffettIns(oggi);

        if (operatore != null) {
            atto.setIdufficioeffetins(operatore.getSezione().getIdParam());
        }

        atto.setRicorso(ricorso);

        // PenaleParam paramAnagAtti = repo.getParam("NATURA_ATTI", "SU");

        /*
         * PenaleAnagAtti anagAtti; if ("".equals(tipoAtto) || tipoAtto == null) { anagAtti = repo.getAnagAtti("TM", paramAnagAtti); } else
         * { anagAtti = repo.getAnagAttiByTagXml(tipoAtto, paramAnagAtti); } if (anagAtti == null) { anagAtti =
         * repo.getAnagAttiByLikeTagXml(tipoAtto, paramAnagAtti); if (anagAtti == null) { anagAtti = repo.getAnagAtti("TM", paramAnagAtti);
         * } } atto.setIdAnagatti(anagAtti.getIdAnagAtti()); atto.setDovesta(repo.getIdParam("SEZIONI", ricorso.getDoveSta()));
         */

        idAtto = atto.getIdAtto();

        // ricorso.getAtti().add(atto);

        LOGGER.info("insertAttoSuccessivo fine");
    }

    public RicorsoSic getRicorso() {
        return ricorso;
    }

    protected Date longToDate(Long l) {
        Date result = null;
        if (l != null) {
            result = new Date(l);
            result.setHours(0);
            result.setMinutes(0);
            result.setSeconds(0);
        }
        return result;
    }

    // Bleah!
    protected String stringifyValore(Double valore) {
        if (valore == null) {
            return null;
        }
        String str = String.format("%.2f", valore);
        return str.replace('.', ',');
    }

    public static String getSiglaAvvocato(Boolean cassazionista) {
        String siglaAvvoc = "NC";
        if (cassazionista != null && cassazionista) {
            siglaAvvoc = "CA";
        }
        return siglaAvvoc;
    }

}
