package it.netservice.csp.backend.elaborazioneDepositi.parser;

import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URL;
import java.util.Date;

import javax.xml.XMLConstants;

import org.apache.commons.beanutils.ConversionException;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.beanutils.Converter;
import org.apache.commons.beanutils.converters.DateConverter;
import org.apache.commons.digester3.Digester;
import org.apache.commons.digester3.ExtendedBaseRules;
import org.apache.commons.digester3.binder.DigesterLoader;

import it.netservice.penale.model.enums.TipoProvvedimentoMagistrato;
import it.netservice.penale.model.sic.ElaborazioneDeposito;

/**
 * <AUTHOR>
 */
public class ElaborazioneDepositoParser {

    private static final String JAXP_SCHEMA_LANGUAGE = "http://java.sun.com/xml/jaxp/properties/schemaLanguage";
    private static final String JAXP_SCHEMA_SOURCE = "http://java.sun.com/xml/jaxp/properties/schemaSource";

    private static final String PATTERN1 = "yyyy-MM-dd HH:mm:ss";
    private static final String PATTERN2 = "yyyy-MM-dd+HH:mm";
    private static final String PATTERN3 = "yyyy-MM-dd+HH:mm:ss";
    private static final String PATTERN4 = "yyyy-MM-ddThh:mm:ss";
    private static final String PATTERN5 = "yyyy-MM-dd";
    private static final String PATTERN6 = "yyyy-MM";

    static {

        DateConverter dateConverter = new DateConverter();
        dateConverter.setPatterns(new String[] { PATTERN1, PATTERN2, PATTERN3, PATTERN4, PATTERN5, PATTERN6 });
        ConvertUtils.register(dateConverter, Date.class);

        EnumConverter enumConverter = new EnumConverter();
        ConvertUtils.register(enumConverter, TipoProvvedimentoMagistrato.class);
    }

    private ElaborazioneDepositoParser() {
    }

    public static ElaborazioneDeposito getDatiAttoAsObject(InputStream xmlStream, String rootElement, URL schema) throws Exception {
        DigesterLoader loader = DigesterLoader.newLoader(new AttiRulesModule(rootElement)).setNamespaceAware(true).setXIncludeAware(false)
                .setValidating(schema != null);

        Digester digester = loader.newDigester(new ExtendedBaseRules());
        if (schema != null) {
            digester.setProperty(JAXP_SCHEMA_LANGUAGE, XMLConstants.W3C_XML_SCHEMA_NS_URI);
            digester.setProperty(JAXP_SCHEMA_SOURCE, schema);
        }
        ElaborazioneDeposito document = null;
        try {
            document = digester.parse(xmlStream);
        } catch (Exception e) {
            if (document == null) {
                e.printStackTrace();
            }
        }

        xmlStream.close();
        return document;
    }

    public static class EnumConverter implements Converter {

        @Override
        public <T> T convert(Class<T> type, Object value) {
            Class<?> superclass = type.getSuperclass();
            if ("java.lang.Enum".equals(superclass.getName())) {
                Method valueOf = null;
                try {
                    valueOf = type.getMethod("valueOf", new Class[] { String.class });
                } catch (NoSuchMethodException e) {
                    // Non deve succedere!
                    throw new ConversionException("Errore imprevisto nella conversione dell'enum", e);
                }
                T enm = null;
                try {
                    enm = (T) valueOf.invoke(null, new Object[] { value });
                } catch (IllegalAccessException | InvocationTargetException e) {
                    // Non deve succedere!
                    throw new ConversionException("Errore imprevisto nella conversione dell'enum", e);
                }
                return enm;
            } else {
                throw new ConversionException(type + "non è un'enum");
            }
        }

    }
}
