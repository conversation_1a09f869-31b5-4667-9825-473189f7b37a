<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:xs="http://www.w3.org/2001/XMLSchema"
             xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/"
             xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="urn:Accettazione"
             targetNamespace="urn:Accettazione">
  <types/>
  
  <message name="accettaDepositoRequest">
    <part name="idBusta" type="xs:string"/>
    <part name="introduttivo" type="xs:boolean"/>
    <part name="hasComplementari" type="xs:boolean"/>
    <part name="readableNrg" type="xs:string"/>
    <part name="msg" type="xs:string"/>
  </message>
  <message name="accettaDepositoResponse">
    <part name="result" type="xs:string"/>
  </message>
  <message name="accettaDepositoComplementareRequest">
    <part name="idBusta" type="xs:string"/>
    <part name="refId" type="xs:string"/>
    <part name="readableNrg" type="xs:string"/>
  </message>
  <message name="accettaDepositoComplementareResponse">
    <part name="result" type="xs:string"/>
  </message>
  <message name="rifiutaDepositoRequest">
    <part name="idBusta" type="xs:string"/>
    <part name="msg" type="xs:string"/>
  </message>
  <message name="voidResponse">
  </message>
  <message name="reinviaEsitiReq">
    <part name="idBusta" type="xs:string"/>
  </message>
  <message name="reinviaEsitiRes"/>
  <message name="assegnaDepositoReq">
    <part name="idBusta" type="xs:string"/>
    <part name="fascicolo" type="xs:string"/>
    <part name="registro" type="xs:string"/>
    <part name="introduttivo" type="xs:boolean"/>
  </message>
  <message name="assegnaDepositoRes"/>
  <message name="invioUfficioCopieReq">
    <part name="idBusta" type="xs:string"/>
    <part name="idSentenza" type="xs:string"/>
  </message>
  <message name="operazioniPostAccettazioneReq">
    <part name="idBusta" type="xs:string"/>
    <part name="idSentenza" type="xs:string"/>
  </message>
  <message name="errorePubblicazioneReq">
    <part name="idBusta" type="xs:string"/>
    <part name="operatore" type="xs:string"/>
  </message>

  <portType name="ServiziAccettazione">
    <operation name="accettaDeposito">
      <input message="tns:accettaDepositoRequest"/>
      <output message="tns:accettaDepositoResponse"/>
    </operation>
    <operation name="accettaDepositoComplementare">
      <input message="tns:accettaDepositoComplementareRequest"/>
      <output message="tns:accettaDepositoComplementareResponse"/>
    </operation>
    <operation name="rifiutaDeposito">
      <input message="tns:rifiutaDepositoRequest"/>
      <output message="tns:voidResponse"/>
    </operation>
    <operation name="reinviaEsiti">
      <input message="tns:reinviaEsitiReq"/>
      <output message="tns:reinviaEsitiRes"/>
    </operation>
    <operation name="assegnaDeposito">
      <input message="tns:assegnaDepositoReq"/>
      <output message="tns:assegnaDepositoRes"/>
    </operation>
    <operation name="invioUfficioCopie">
      <input message="tns:invioUfficioCopieReq"/>
      <output message="tns:voidResponse"/>
    </operation>
    <operation name="operazioniPostAccettazione">
      <input message="tns:operazioniPostAccettazioneReq"/>
      <output message="tns:voidResponse"/>
    </operation>
    <operation name="errorePubblicazione">
      <input message="tns:errorePubblicazioneReq"/>
      <output message="tns:voidResponse"/>
    </operation>
    <operation name="errorePubblicazionePenale">
      <input message="tns:errorePubblicazioneReq"/>
      <output message="tns:voidResponse"/>
    </operation>
  </portType>

  <binding name="ServiziAccettazioneSOAPBinding" type="tns:ServiziAccettazione">
    <soap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http"/>
    <operation name="accettaDeposito">
      <input>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </output>
    </operation>
    <operation name="accettaDepositoComplementare">
      <input>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </output>
    </operation>
    <operation name="rifiutaDeposito">
      <input>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </output>
    </operation>
    <operation name="reinviaEsiti">
      <soap:operation soapAction="urn:#reinviaEsiti"/>
      <input>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </output>
    </operation>
    <operation name="assegnaDeposito">
      <soap:operation soapAction="urn:#assegnaDeposito"/>
      <input>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </output>
    </operation>
    <operation name="invioUfficioCopie">
      <soap:operation soapAction="urn:#invioUfficioCopie"/>
      <input>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </output>
    </operation>
    <operation name="operazioniPostAccettazione">
      <soap:operation soapAction="urn:#operazioniPostAccettazione"/>
      <input>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </output>
    </operation>
    <operation name="errorePubblicazione">
      <soap:operation soapAction="urn:#errorePubblicazione"/>
      <input>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </output>
    </operation>
    <operation name="errorePubblicazionePenale">
      <soap:operation soapAction="urn:#errorePubblicazionePenale"/>
      <input>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </input>
      <output>
        <soap:body use="encoded" namespace="urn:Accettazione"/>
      </output>
    </operation>
  </binding>

  <service name="wsServiziAccettazione">
    <port name="ServiziAccettazionePort" binding="tns:ServiziAccettazioneSOAPBinding">
      <soap:address location="http://localhost:8080/wasp/rpcrouter"/>
    </port>
  </service>
</definitions>