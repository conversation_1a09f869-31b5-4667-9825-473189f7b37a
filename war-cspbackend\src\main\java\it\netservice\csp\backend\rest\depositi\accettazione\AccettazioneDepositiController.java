package it.netservice.csp.backend.rest.depositi.accettazione;

import java.io.Serializable;
import java.net.MalformedURLException;
import java.rmi.RemoteException;
import java.util.ArrayList;

import javax.annotation.security.RolesAllowed;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.Response;
import javax.xml.rpc.ServiceException;

import org.apache.log4j.Logger;

import com.auth0.jwt.interfaces.DecodedJWT;

import it.netservice.common.rest.CommonRestWarning;
import it.netservice.csp.backend.accettazioneDepositi.AccettazioneDepositiService;
import it.netservice.csp.backend.accettazioneDepositi.DatiAccettazioneDepositoDto;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.elaborazioneDepositi.service.DepositoService;
import it.netservice.csp.backend.elaborazioneDepositi.service.ElaborazioneDepositoService;
import it.netservice.csp.backend.excpetion.AccettazioneDepositiExcpetion;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.CspCommonRestWarningException;
import it.netservice.csp.backend.ext.service.ServiceBuilder;
import it.netservice.csp.backend.rest.AbstractCspBackendController;
import it.netservice.csp.backend.rest.auth.SecurityUtil;
import it.netservice.csp.backend.rest.auth.TokenManager;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.penale.model.sic.Deposito;
import it.netservice.penale.model.sic.ElaborazioneDeposito;
import it.netservice.penale.model.sic.Utente;

/**
 * <AUTHOR>
 */
@RolesAllowed({ "CSPCLIENT" })
@Path("/accettazioneDepositi")
public class AccettazioneDepositiController extends AbstractCspBackendController {

    private static final Logger LOGGER = Logger.getLogger(AccettazioneDepositiController.class);

    @Inject
    private ServiceBuilder serviceBuilder;

    @Inject
    private TokenManager tokenManager;

    @Inject
    private ElaborazioneDepositoService elaborazioneDepositoService;

    @Inject
    private SecurityUtil util;

    @Inject
    private AccettazioneDepositiService accettazioneDepositiService;

    @Inject
    private DepositoService depositoService;

    @POST
    @Path("/accetta")
    public Response accetta(@Context HttpServletRequest context, DatiAccettazioneDepositoDto datiAccettazioneDto) throws Throwable {
        if (datiAccettazioneDto == null) {
            throw new AccettazioneDepositiExcpetion("Dati per l'accettazione non ricevuti");
        }
        EntityManager entityManager = emfCaricamento.createEntityManager();

        try {
            DecodedJWT jwt = tokenManager.verifyToken(context.getHeader("x-auth-token"));
            long idDeposito = datiAccettazioneDto.getIdDeposito();

            util.logComandoDeposito(util.getOperatore(jwt), "accetta deposito", idDeposito);
            util.checkSessionToValuedInToken(context, entityManager, idDeposito);
            util.checkUtenzaReadOnly(jwt);

            return accettazioneDepositiService.accetta(util.getOperatore(jwt), util.getIdUtente(jwt), idDeposito, true, null, null, null,
                    datiAccettazioneDto.getMotivazione());
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } catch (Throwable t) {
            if (t instanceof CspBackendException) {
                throw (CspBackendException) t;
            }
            LOGGER.error("Errore durante l'accettazione del deposito. idDeposito:" + datiAccettazioneDto.getIdDeposito());
            throw new AccettazioneDepositiExcpetion("Errore durante l'accettazione del deposito",
                    RestErrorCodeEnum.ACCETTAZIONE_DEPOSITI_GENERIC_ERROR, t);
        } finally {
            entityManager.close();
        }
    }

    @POST
    @Path("/massiva")
    public Response accettaMassiva(@Context HttpServletRequest context, DatiAccettazioneMassiva dati) throws Throwable {
        dati.setError(0);
        dati.setOk(0);
        try {
            for (Long idDeposito : dati.getDepositiSelezionati()) {
                EntityManager entityManager = emfCaricamento.createEntityManager();
                try {
                    DecodedJWT jwt = tokenManager.verifyToken(context.getHeader("x-auth-token"));
                    if (idDeposito == null) {
                        throw new CspBackendException("Riferimento deposito non corretto. idDeposito null",
                                RestErrorCodeEnum.ID_DEPOSITO_NULL, 433);
                    }
                    /**
                     * Prendi in carico
                     */
                    util.logComandoDeposito(util.getOperatore(jwt), "presa in carico", idDeposito);
                    // util.checkUtenzaDepositoView(context, entityManager, idDeposito);

                    String operatore = (String) context.getSession().getAttribute("idOperatore");
                    SicPenaleRepository repo = new SicPenaleRepository(entityManager);
                    Utente utente = repo.findUtenteByUsername(operatore);
                    if (utente == null) {
                        LOGGER.error("Utente non trovato nella base dati. Operatore:" + operatore);
                        throw new CspBackendException("Utente non trovato nella base dati", RestErrorCodeEnum.USER_NOT_FOUND, 433);
                    }
                    // conteggio non ci siano altri depositi in carico all' operatore
                    int inCarico = depositoService.countDepositiInCarico(entityManager, utente.getIdUtente());
                    if (inCarico != 0) {
                        depositoService.rimuoviInCarico(entityManager, utente.getIdUtente());
                    }
                    depositoService.prendiInCarico(entityManager, idDeposito, utente.getIdUtente());

                    /**
                     * Accetta depositi presi in carico
                     */
                    accettazioneDepositiService.accetta(util.getOperatore(jwt), util.getIdUtente(jwt), idDeposito, true, null, null, null,
                            null);
                    dati.setOk(dati.getOk() + 1);
                } catch (Exception e) {
                    LOGGER.error("Errore durante l'accettazione massiva. idDeposito:" + idDeposito, e);
                    dati.setError(dati.getError() + 1);
                } finally {
                    entityManager.close();
                }
            }
            return Response.ok(dati).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante l'accettazione massiva dei depositi");
            throw new AccettazioneDepositiExcpetion("Errore durante l'accettazione massiva dei depositi",
                    RestErrorCodeEnum.ACCETTAZIONE_DEPOSITI_GENERIC_ERROR, e);
        }
    }

    /**
     * Registrazione dei pagamenti su un deposito accettato
     *
     * @param context
     *            contesto servlet
     * @param idDeposito
     *            identificativo deposito
     * @return numero/anno fascicolo
     * @throws CspBackendException
     */
    @POST
    @Path("/accetta/pagamenti")
    public Response accettaPagamenti(@Context HttpServletRequest context, @QueryParam("idDeposito") Long idDeposito)
            throws CspBackendException, CommonRestWarning {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            LOGGER.info("Inizio invalidazione pagamenti");
            if (context.getSession().getAttribute("idOperatore") == null) {
                throw new CspBackendException("Sessione scaduta o assente", RestErrorCodeEnum.SESSION_EXPIRED, 433);
            }
            if (idDeposito == null) {
                throw new CspBackendException("Riferimento deposito non corretto. idDeposito null", RestErrorCodeEnum.ID_DEPOSITO_NULL,
                        433);
            }
            util.logComandoDeposito(util.getOperatore(tokenManager.verifyToken(context.getHeader("x-auth-token"))), "accetta pagamenti",
                    idDeposito);
            // util.checkUtenzaDepositoView(context, entityManager, idDeposito);
            ElaborazioneDeposito elabDep = elaborazioneDepositoService.findElaborazioneDepositoByIdDeposito(entityManager, idDeposito,
                    null);

            if (Deposito.Stato.NEW.equals(elabDep.getDeposito().getStato())) {
                LOGGER.warn("Impossibile registrare i pagamenti: il deposito non è ancora stato lavorato. idDeposito:"
                        + elabDep.getDeposito().getIdCat() + ", idElaborato:" + elabDep.getId());
                throw new CspCommonRestWarningException("Impossibile registrare i pagamenti: il deposito non è ancora stato lavorato",
                        RestErrorCodeEnum.DEPOSITO_NON_LAVORATO);
            }
            accettazioneDepositiService.invalidaPagamentiGL(elabDep, elabDep.getDeposito().getNrg().toString());
            LOGGER.info("Fine invalidazione pagamenti");
            return Response.ok(elabDep.getNumeroRicorso() + "/" + elabDep.getAnnoRicorso()).build();
        } catch (Throwable t) {
            if (t instanceof CspBackendException) {
                throw (CspBackendException) t;
            }
            throw t;
        } finally {
            entityManager.close();
        }
    }

    @POST
    @Path("/test/errorePubblicazione")
    public Response ErrorePubblicazione(@Context HttpServletRequest context, @QueryParam("idDeposito") Long idDeposito) throws Throwable {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        Deposito deposito = entityManager.find(Deposito.class, idDeposito);
        DecodedJWT jwt = tokenManager.verifyToken(context.getHeader("x-auth-token"));
        accettazioneDepositiService.errorePubblicazioneTest(deposito, util.getOperatore(jwt), entityManager, util.getIdUtente(jwt));
        return Response.ok().build();
    }

    @POST
    @Path("/rifiuta")
    public Response rifiuta(@Context HttpServletRequest context, DatiAccettazioneDepositoDto datiRifiuto)
            throws CspBackendException, MalformedURLException, ServiceException, RemoteException {
        if (datiRifiuto == null) {
            throw new AccettazioneDepositiExcpetion("Dati per il rifiuto non ricevuti");
        }
        EntityManager entityManager = emfCaricamento.createEntityManager();
        EntityTransaction transaction = entityManager.getTransaction();
        transaction.begin();

        try {
            DecodedJWT jwt = tokenManager.verifyToken(context.getHeader("x-auth-token"));

            util.logComandoDeposito(util.getOperatore(jwt), "rifiuta deposito", datiRifiuto.getIdDeposito());
            util.checkSessionToValuedInToken(context, entityManager, datiRifiuto.getIdDeposito());
            Long idUtente = util.getIdUtente(jwt);
            String operatore = util.getOperatore(jwt);

            accettazioneDepositiService.rifiuta(datiRifiuto, idUtente, operatore);
            return Response.ok().build();
        } catch (Throwable e) {
            if (transaction.isActive()) {
                transaction.rollback();
            }
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante il rifiuto del deposito. idDeposito" + datiRifiuto.getIdDeposito());
            throw new AccettazioneDepositiExcpetion("Errore durante il rifiuto del deposito",
                    RestErrorCodeEnum.RIFIUTA_DEPOSITO_GENERIC_ERROR, e);
        } finally {
            if (entityManager.isOpen()) {
                entityManager.close();
            }
        }
    }

    private static class DatiAccettazioneMassiva implements Serializable {

        private ArrayList<Long> depositiSelezionati;
        private int ok;
        private int error;

        public DatiAccettazioneMassiva() {
        }

        public DatiAccettazioneMassiva(int ok, int error) {
            this.ok = ok;
            this.error = error;
        }

        public ArrayList<Long> getDepositiSelezionati() {
            return depositiSelezionati;
        }

        public void setDepositiSelezionati(ArrayList<Long> depositiSelezionati) {
            this.depositiSelezionati = depositiSelezionati;
        }

        public int getOk() {
            return ok;
        }

        public void setOk(int ok) {
            this.ok = ok;
        }

        public int getError() {
            return error;
        }

        public void setError(int error) {
            this.error = error;
        }
    }
}
