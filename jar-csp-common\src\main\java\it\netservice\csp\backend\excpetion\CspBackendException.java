package it.netservice.csp.backend.excpetion;

import com.google.gson.annotations.Expose;

import it.netservice.csp.backend.dto.ErrorTypeEnum;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;

/**
 *
 * <AUTHOR>
 */
public class CspBackendException extends Exception {
    @Expose
    RestErrorCodeEnum errorCode;
    @Expose
    ErrorTypeEnum errorType;

    @Expose
    String reason;
    @Expose
    Integer status = null;

    public CspBackendException() {
    }

    public CspBackendException(String message) {
        super(message);
        errorCode = RestErrorCodeEnum.GENERIC_ERROR;
        reason = null;
        errorType = ErrorTypeEnum.ERROR;
    }

    public CspBackendException(String message, Integer status) {
        super(message);
        errorCode = RestErrorCodeEnum.GENERIC_ERROR;
        reason = null;
        this.status = status;
        errorType = ErrorTypeEnum.ERROR;
    }

    public CspBackendException(String message, RestErrorCodeEnum errorCode) {
        super(message);
        this.errorCode = errorCode;
        reason = null;
        errorType = ErrorTypeEnum.ERROR;
    }

    public CspBackendException(String message, RestErrorCodeEnum errorCode, Integer status) {
        super(message);
        this.errorCode = errorCode;
        reason = null;
        this.status = status;
        errorType = ErrorTypeEnum.ERROR;
    }

    public CspBackendException(String message, RestErrorCodeEnum errorCode, Integer status, ErrorTypeEnum errorType) {
        super(message);
        this.errorCode = errorCode;
        reason = null;
        this.status = status;
        this.errorType = errorType;
    }

    public CspBackendException(String message, RestErrorCodeEnum errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        reason = null;
        errorType = ErrorTypeEnum.ERROR;
    }

    public CspBackendException(String message, RestErrorCodeEnum errorCode, Integer status, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        reason = null;
        this.status = status;
        errorType = ErrorTypeEnum.ERROR;
    }

    public CspBackendException(String message, RestErrorCodeEnum errorCode, Integer status, Throwable cause, ErrorTypeEnum errorType) {
        super(message, cause);
        this.errorCode = errorCode;
        reason = null;
        this.status = status;
        this.errorType = errorType;
    }

    public CspBackendException(String message, RestErrorCodeEnum errorCode, String reason) {
        super(message);
        this.errorCode = errorCode;
        this.reason = reason;
        errorType = ErrorTypeEnum.ERROR;
    }

    public CspBackendException(String message, RestErrorCodeEnum errorCode, String reason, Integer status) {
        super(message);
        this.errorCode = errorCode;
        this.reason = reason;
        this.status = status;
        errorType = ErrorTypeEnum.ERROR;
    }

    public CspBackendException(String message, RestErrorCodeEnum errorCode, String reason, Integer status, ErrorTypeEnum errorType) {
        super(message);
        this.errorCode = errorCode;
        this.reason = reason;
        this.status = status;
        this.errorType = errorType;
    }

    public CspBackendException(String message, RestErrorCodeEnum errorCode, String reason, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.reason = reason;
        this.status = null;
        errorType = ErrorTypeEnum.ERROR;
    }

    public CspBackendException(String message, RestErrorCodeEnum errorCode, String reason, Integer status, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.reason = reason;
        this.status = status;
        errorType = ErrorTypeEnum.ERROR;
    }

    public CspBackendException(String message, RestErrorCodeEnum errorCode, String reason, Integer status, Throwable cause,
            ErrorTypeEnum errorType) {
        super(message, cause);
        this.errorCode = errorCode;
        this.reason = reason;
        this.status = status;
        this.errorType = errorType;
    }

    public CspBackendException(String message, Throwable cause) {
        super(message, cause);
    }

    public RestErrorCodeEnum getErrorCode() {
        return errorCode;
    }

    public String getReason() {
        return reason;
    }

    public Integer getStatus() {
        return status;
    }

    public ErrorTypeEnum getErrorType() {
        return errorType;
    }
}
