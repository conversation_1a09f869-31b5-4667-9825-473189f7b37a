/**
 *
 */
package it.netservice.csp.backend.rest.depositi.elaborazione;

import javax.annotation.security.RolesAllowed;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.Response;

import org.apache.log4j.Logger;

import com.auth0.jwt.interfaces.DecodedJWT;

import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.elaborazioneDepositi.service.ElaborazioneDepositoService;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.ElaborazioneException;
import it.netservice.csp.backend.rest.AbstractCspBackendController;
import it.netservice.csp.backend.rest.auth.SecurityUtil;
import it.netservice.csp.backend.rest.auth.TokenManager;
import it.netservice.penale.model.sic.ElaborazioneDeposito;

/**
 * Servizi per l'elaborazione di un nuovo deposito
 *
 * <AUTHOR>
 */
@RolesAllowed({ "CSPCLIENT" })
@Path("/elaborazionedeposito")
public class ElaborazioneDepositoController extends AbstractCspBackendController {

    private static final Logger LOGGER = Logger.getLogger(ElaborazioneDepositoController.class);

    @Inject
    private ElaborazioneDepositoService elaborazioneDepositoService;

    @Inject
    private TokenManager tokenManager;

    @Inject
    private SecurityUtil util;

    @GET
    public Response getElaborazioneDeposito(@Context HttpServletRequest context, @QueryParam("id") String id) throws CspBackendException {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            ElaborazioneDeposito elabDep = elaborazioneDepositoService.findElaborazioneDepositoById(entityManager, id);

            util.logComandoElaborazione(context, "get elaborazione", id);
            // util.checkUtenzaElaborazioneDeposito(context, entityManager, elabDep);

            return Response.ok(elabDep).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante il recupero delle informazioni del deposito. idCatDeposito:" + id);
            throw new ElaborazioneException("Errore durante il recupero delle informazioni del deposito",
                    RestErrorCodeEnum.ELABORAZIONE_DEPOSITO_GENERIC_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET
    @Path("/hasMinutaAccettata")
    public Response hasMinutaAccettata(@Context HttpServletRequest context, @QueryParam("idDeposito") Long idCat)
            throws CspBackendException {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            // boolean hasMinuta = elaborazioneDepositoService.findMinutaAccettataByIdDepositoDefinitivo(entityManager, idCat, null);
            return Response.ok().build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante il recupero delle informazioni del deposito. idCatDeposito:" + idCat);
            throw new ElaborazioneException("Errore durante il recupero delle informazioni del deposito",
                    RestErrorCodeEnum.ELABORAZIONE_DEPOSITO_GENERIC_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET
    @Path("/byIdDeposito")
    public Response getElaborazioneDepositoByIdDeposito(@Context HttpServletRequest context, @QueryParam("idDeposito") Long idCat)
            throws CspBackendException {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            ElaborazioneDeposito elabDep = elaborazioneDepositoService.findElaborazioneDepositoByIdDeposito(entityManager, idCat, null);

            util.logComandoElaborazione(context, "get elaborazione by id deposito", elabDep.getId());
            // util.checkUtenzaElaborazioneDeposito(context, entityManager, elabDep);

            return Response.ok(elabDep).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante il recupero delle informazioni del deposito. idCatDeposito:" + idCat);
            throw new ElaborazioneException("Errore durante il recupero delle informazioni del deposito",
                    RestErrorCodeEnum.ELABORAZIONE_DEPOSITO_GENERIC_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @POST
    public Response saveElaborazioneDeposito(@Context HttpServletRequest context, ElaborazioneDeposito elaborazioneDeposito)
            throws CspBackendException {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        EntityTransaction transaction = entityManager.getTransaction();
        transaction.begin();
        try {
            DecodedJWT jwt = tokenManager.verifyToken(context.getHeader("x-auth-token"));

            SecurityUtil.checkUtenzaReadOnly(jwt);
            util.logComandoElaborazione(context, "salva elaborazione", elaborazioneDeposito.getId());
            // anche qui causa problemi di visibilità cambiando il riferimento ricorso in corsa
            // util.checkUtenzaElaborazioneDeposito(context, entityManager, elaborazioneDeposito);

            elaborazioneDeposito = elaborazioneDepositoService.saveElaborazioneDeposito(entityManager, elaborazioneDeposito);
            transaction.commit();
            return Response.ok(elaborazioneDeposito).build();
        } catch (Throwable e) {
            if (transaction.isActive()) {
                transaction.rollback();
            }
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante il salvataggio dell'elaborazione del deposito");
            throw new ElaborazioneException("Errore nel salvataggio dell'elaborazione", RestErrorCodeEnum.ELABORAZIONE_DEPOSITO_SAVE_ERROR,
                    e);
        } finally {
            entityManager.close();
        }
    }

    @GET
    @Path("/elimina")
    public Response eliminaElaborazioneDeposito(@Context HttpServletRequest context, @QueryParam("id") String id)
            throws CspBackendException {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        EntityTransaction transaction = entityManager.getTransaction();
        transaction.begin();
        try {
            util.logComandoElaborazione(context, "elimina elaborazione", id);
            // util.checkUtenzaElaborazioneDeposito(context, entityManager, id);

            elaborazioneDepositoService.eliminaElaborazioneDeposito(entityManager, id, true);
            transaction.commit();
            LOGGER.info("Eliminazione elaborazione deposito " + id + " completata");
            return Response.ok().build();
        } catch (Throwable e) {
            if (transaction.isActive()) {
                transaction.rollback();
            }
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante l'eliminazione dell'elaborazione. idElabDeposito:" + id);
            throw new ElaborazioneException("Errore nell'eliminazione dell'elaborazione",
                    RestErrorCodeEnum.ELABORAZIONE_DEPOSITO_DELETE_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

}
