plugins {
    id "ns-patch" version "${patchPluginVersion}"
}

patch {
    basename = 'SERVIZI-CSP'
    includeVersionFile false
} 


releaseNotes {
   withAs true
    
    // withDb a false per evitare che aggiunga la sezione per script db presenti nello stesso progetto.
	withDb false

    //modalitaApplicazioneDB "senza_fermo"
    modalitaApplicazioneDB "con_fermo"

    applicazionePatchAS {
        descrizione = "Per eseguire l'aggiornamento degli application server eseguire la seguente procedura per ogni macchina AS:"
         elenco = ["Effettuare il login alla macchina AS come utente root",
                   "Copiare il file zip in una cartella temporanea e scompattarlo",
                   "Stoppare il servizio jboss",
                   "Eseguire lo script 'install.sh' presente nella cartella 'application_server' (sh install.sh)",
                    // MODULES
                   //"Posizionarsi nella cartella  /opt/jboss-as/modules/cassazione/csc del server AS e spostare la cartella main in /opt/jboss-as/modules/cassazione/csp, ",
                   // "se è già presente la cartella /opt/jboss-as/modules/cassazione/csp/main mantenere la versione corrente dei file già presenti in essa ed eliminare le versioni precendenti di librerie più recenti.",
                   //"Copiare il contenuto  della cartella modules/cassazione/csp/main presente nella patch attuale, dentro la directory /opt/jboss-as/modules/cassazione/csp/main," + System.getProperty("line.separator") +\
                   // "Dare i permessi opportuni ai file copiati del passo precedente.",
                    "Riavviare il servizio jboss"]
    }

    dipendenzeSistemi {
        descrizione = 
                     "Applicare prima la patch di GL-CASS 3.30.04 e di CUA-CASS 2.00.00"
    }


    applicazionePatchDB {
        descrizione = "Applicare prima la patch CASSAZIONE_PENALE_DB_1.04.00"
    }

    modificheDB {
        descrizione = "Sono riportate nella patch CASSAZIONE_PENALE_DB_1.04.00"
    }

   
/*     modificheConfigurazione {
        descrizione = "Nei files csp-backend.properties e standalone.xml, presenti nella cartella CONF della patch, sono riportate le configurazioni attualmente disponibili in ambiente di esercizio."
    } */

	modificheEvolutive{
        descrizione = "Contratto CIG B2BD866C4A - PLO21 - Interventi migliorativi per l’operatività della Settima Sezione Penale"
    }

    impattiSistemi {
        descrizione = "Questa patch ha impatto sul sistema Web Csp"
    }

}



dependencies {
    deploy project(path: ':ear-cspbackend', configuration: 'eardep')
}
