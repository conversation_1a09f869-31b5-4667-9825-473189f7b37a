package it.netservice.csp.backend.dto;

import java.util.Date;

public class PenaleUdienzeDto {

    private String udienza;
    private Long nrg;
    private String sezione;
    private Date dataUdienza;
    private String tipoUdienza;
    private String collegio;
    private String presidente;
    private String cfPresidente;
    private String relatore;
    private String cfRelatore;
    private String estensore;
    private String cfEstensore;
    private String detParti;
    private Integer numeroRicorso;
    private Integer annoRicorso;
    private String tipoRicorso;
    private String stato;
    private Date dataMinuta;
    private Integer numeroSezionale;
    private String annoSezionale;
    private String idProvvedimento;
    private Date dataPubblicazione;
    private String nraccg;
    private String totaleGiorni;

    private Date dataMessaVisionePresidente;
    private Date dataRichiestaUltimaModifica;
    private Date dataDepositoUltimaMinutaModificata;
    private Date dataDepositoProvvedimento;

    public String getUdienza() {
        return udienza;
    }

    public void setUdienza(String udienza) {
        this.udienza = udienza;
    }

    public Long getNrg() {
        return nrg;
    }

    public void setNrg(Long nrg) {
        this.nrg = nrg;
    }

    public String getSezione() {
        return sezione;
    }

    public void setSezione(String sezione) {
        this.sezione = sezione;
    }

    public Date getDataUdienza() {
        return dataUdienza;
    }

    public void setDataUdienza(Date dataUdienza) {
        this.dataUdienza = dataUdienza;
    }

    public String getTipoUdienza() {
        return tipoUdienza;
    }

    public void setTipoUdienza(String tipoUdienza) {
        this.tipoUdienza = tipoUdienza;
    }

    public String getCollegio() {
        return collegio;
    }

    public void setCollegio(String collegio) {
        this.collegio = collegio;
    }

    public String getPresidente() {
        return presidente;
    }

    public void setPresidente(String presidente) {
        this.presidente = presidente;
    }

    public String getCfPresidente() {
        return cfPresidente;
    }

    public void setCfPresidente(String cfPresidente) {
        this.cfPresidente = cfPresidente;
    }

    public String getRelatore() {
        return relatore;
    }

    public void setRelatore(String relatore) {
        this.relatore = relatore;
    }

    public String getCfRelatore() {
        return cfRelatore;
    }

    public void setCfRelatore(String cfRelatore) {
        this.cfRelatore = cfRelatore;
    }

    public String getEstensore() {
        return estensore;
    }

    public void setEstensore(String estensore) {
        this.estensore = estensore;
    }

    public String getCfEstensore() {
        return cfEstensore;
    }

    public void setCfEstensore(String cfEstensore) {
        this.cfEstensore = cfEstensore;
    }

    public String getDetParti() {
        return detParti;
    }

    public void setDetParti(String detParti) {
        this.detParti = detParti;
    }

    public Integer getNumeroRicorso() {
        return numeroRicorso;
    }

    public void setNumeroRicorso(Integer numeroRicorso) {
        this.numeroRicorso = numeroRicorso;
    }

    public Integer getAnnoRicorso() {
        return annoRicorso;
    }

    public void setAnnoRicorso(Integer annoRicorso) {
        this.annoRicorso = annoRicorso;
    }

    public String getTipoRicorso() {
        return tipoRicorso;
    }

    public void setTipoRicorso(String tipoRicorso) {
        this.tipoRicorso = tipoRicorso;
    }

    public String getStato() {
        return stato;
    }

    public void setStato(String stato) {
        this.stato = stato;
    }

    public Date getDataMinuta() {
        return dataMinuta;
    }

    public void setDataMinuta(Date dataMinuta) {
        this.dataMinuta = dataMinuta;
    }

    public Integer getNumeroSezionale() {
        return numeroSezionale;
    }

    public void setNumeroSezionale(Integer numeroSezionale) {
        this.numeroSezionale = numeroSezionale;
    }

    public String getAnnoSezionale() {
        return annoSezionale;
    }

    public void setAnnoSezionale(String annoSezionale) {
        this.annoSezionale = annoSezionale;
    }

    public String getIdProvvedimento() {
        return idProvvedimento;
    }

    public void setIdProvvedimento(String idProvvedimento) {
        this.idProvvedimento = idProvvedimento;
    }

    public Date getDataPubblicazione() {
        return dataPubblicazione;
    }

    public void setDataPubblicazione(Date dataPubblicazione) {
        this.dataPubblicazione = dataPubblicazione;
    }

    public String getNraccg() {
        return nraccg;
    }

    public void setNraccg(String nraccg) {
        this.nraccg = nraccg;
    }

    public String getTotaleGiorni() {
        return totaleGiorni;
    }

    public void setTotaleGiorni(String totaleGiorni) {
        this.totaleGiorni = totaleGiorni;
    }

    public Date getDataMessaVisionePresidente() {
        return dataMessaVisionePresidente;
    }

    public void setDataMessaVisionePresidente(Date dataMessaVisionePresidente) {
        this.dataMessaVisionePresidente = dataMessaVisionePresidente;
    }

    public Date getDataRichiestaUltimaModifica() {
        return dataRichiestaUltimaModifica;
    }

    public void setDataRichiestaUltimaModifica(Date dataRichiestaUltimaModifica) {
        this.dataRichiestaUltimaModifica = dataRichiestaUltimaModifica;
    }

    public Date getDataDepositoUltimaMinutaModificata() {
        return dataDepositoUltimaMinutaModificata;
    }

    public void setDataDepositoUltimaMinutaModificata(Date dataDepositoUltimaMinutaModificata) {
        this.dataDepositoUltimaMinutaModificata = dataDepositoUltimaMinutaModificata;
    }

    public Date getDataDepositoProvvedimento() {
        return dataDepositoProvvedimento;
    }

    public void setDataDepositoProvvedimento(Date dataDepositoProvvedimento) {
        this.dataDepositoProvvedimento = dataDepositoProvvedimento;
    }
}
