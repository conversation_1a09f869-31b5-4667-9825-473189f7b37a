package it.netservice.csp.backend.rest.depositi;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Arrays;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;

import org.apache.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import it.netservice.csp.backend.ext.service.ServiceBuilder;
import it.netservice.csp.backend.ws.atti.IdAtto;
import it.netservice.csp.backend.ws.atti.IdFascicolo;
import it.netservice.csp.backend.ws.atti.ServiziAttoInformatico;

/**
 *
 * <AUTHOR>
 */
public class DatiAttoParserMinimal {

    private static final Logger LOGGER = Logger.getLogger(DatiAttoParserMinimal.class);

    private byte[] content;

    private final ServiceBuilder serviceBuilder;

    private String tipoAtto;
    private Long numeroRicorso;
    private String annoRicorso;
    private String codiceMateriaRicorso;

    public DatiAttoParserMinimal(ServiceBuilder serviceBuilder) {
        this.serviceBuilder = serviceBuilder;
    }

    public void parse(Long idCatDatiAtto) throws IOException, ParserConfigurationException, SAXException {
        ServiziAttoInformatico service = serviceBuilder.getAttoInformaticoService();
        IdAtto idAtto = new IdAtto();
        IdFascicolo idFascicolo = new IdFascicolo();
        idFascicolo.setRegistro(System.getProperty("gestorelocale.registro"));
        idAtto.setFascicolo(idFascicolo);
        idAtto.setId(String.valueOf(idCatDatiAtto));
        content = service.download(idAtto, false);
        DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
        dbFactory.setNamespaceAware(true);
        DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
        ByteArrayInputStream input = new ByteArrayInputStream(content);
        Document doc = dBuilder.parse(input);
        tipoAtto = getRootElement(doc.getFirstChild()).getLocalName();
        if (Arrays.asList("Provvedimento", "ProvvedimentoPG").contains(tipoAtto)) {
            tipoAtto = doc.getElementsByTagNameNS("*", "Tipo").item(0).getFirstChild().getTextContent();
        }
        NodeList procedimentoNodeList = doc.getElementsByTagNameNS("*", "procedimento");
        if (procedimentoNodeList.getLength() > 0) {
            Node procedimentoNode = procedimentoNodeList.item(0);
            NodeList childNodes = procedimentoNode.getChildNodes();
            for (int i = 0; i < childNodes.getLength(); i++) {
                Node node = childNodes.item(i);
                String nodeName = node.getNodeName();
                if (nodeName.contains("numero")) {
                    try {
                        numeroRicorso = Long.valueOf(node.getTextContent());
                    } catch (Exception ex) {
                        LOGGER.warn("Errore durante il parsing del numero ricorso: " + node.getTextContent(), ex);
                    }
                } else if (nodeName.contains("anno")) {
                    annoRicorso = node.getTextContent();
                }
            }
        }
        NodeList materiaNodeList = doc.getElementsByTagNameNS("*", "Materia");
        if (materiaNodeList.getLength() > 0) {
            Node materiaNode = materiaNodeList.item(0);
            NodeList childNodes = materiaNode.getChildNodes();
            for (int i = 0; i < childNodes.getLength(); i++) {
                Node node = childNodes.item(i);
                String nodeName = node.getNodeName();
                if (nodeName.contains("materia")) {
                    try {
                        codiceMateriaRicorso = node.getTextContent();
                    } catch (Exception ex) {
                        LOGGER.warn("Errore durante il parsing della materia: " + node.getTextContent(), ex);
                    }
                }
            }
        }
    }

    private Node getRootElement(Node node) throws SAXException {
        if (node.getNodeType() == Node.ELEMENT_NODE) {
            return node;
        }
        if (node.getNextSibling() == null) {
            throw new SAXException("Impossibile risolvere il rootElement");
        }
        return getRootElement(node.getNextSibling());
    }

    public byte[] getContent() {
        return content;
    }

    public String getTipoAtto() {
        return tipoAtto;
    }

    public Long getNumeroRicorso() {
        return numeroRicorso;
    }

    public String getAnnoRicorso() {
        return annoRicorso;
    }

    public String getCodiceMateriaRicorso() {
        return codiceMateriaRicorso;
    }

}
