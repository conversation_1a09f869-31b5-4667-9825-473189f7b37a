package it.netservice.csp.backend.model.ucu;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedQuery;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * The persistent class for the UCU_DOCUMENTO database table.
 *
 */
@Entity
@Table(name = "UCU_STORIA_DOCUMENTO")
@NamedQuery(name = "UcuStoriaDocumento.findAll", query = "SELECT u FROM UcuStoriaDocumento u")
public class UcuStoriaDocumento implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @SequenceGenerator(name = "UCU_STORIA_DOCUMENTO_IDSTORIADOCUMENTO_GENERATOR", sequenceName = "SEQ_UCU_STORIA_DOCUMENTO", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "UCU_STORIA_DOCUMENTO_IDSTORIADOCUMENTO_GENERATOR")
    @Column(name = "ID_STORIA_DOCUMENTO")
    private long idStoriaDocumento;

    @Column(name = "ID_DOCUMENTO")
    private long idDocumento;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "DT_MODIFICA")
    private Date dtModifica;

    @Column(name = "FLAG_OSC")
    private String flagOsc;

    @Column(name = "FLAG_MIGRATO")
    private String flagMigrato;

    @Column(name = "ID_FILE")
    private BigDecimal idFile;

    @Column(name = "NOME_FILE")
    private String nomeFile;

    @Column(name = "NUM_PAGINE")
    private BigDecimal numPagine;

    @Column(name = "UT_MODIFICA")
    private String utModifica;

    private BigDecimal versione;

    @Column(name = "ID_IMPOSTA_REG")
    private BigDecimal ucuTpImpostaReg;

    @Column(name = "ID_STATO_DOC")
    private BigDecimal ucuTpStatoDoc;

    @Column(name = "ID_TIPO_PROVV")
    private BigDecimal ucuTpTipoProvv;

    @Column(name = "ID_AZIONE")
    private BigDecimal idAzione;

    public UcuStoriaDocumento() {
    }

    public long getIdDocumento() {
        return this.idDocumento;
    }

    public void setIdDocumento(long idDocumento) {
        this.idDocumento = idDocumento;
    }

    public Date getDtModifica() {
        return this.dtModifica;
    }

    public void setDtModifica(Date dtModifica) {
        this.dtModifica = dtModifica;
    }

    public String getFlagOsc() {
        return this.flagOsc;
    }

    public void setFlagOsc(String flagOsc) {
        this.flagOsc = flagOsc;
    }

    public BigDecimal getIdFile() {
        return this.idFile;
    }

    public void setIdFile(BigDecimal idFile) {
        this.idFile = idFile;
    }

    public String getNomeFile() {
        return this.nomeFile;
    }

    public void setNomeFile(String nomeFile) {
        this.nomeFile = nomeFile;
    }

    public BigDecimal getNumPagine() {
        return this.numPagine;
    }

    public void setNumPagine(BigDecimal numPagine) {
        this.numPagine = numPagine;
    }

    public String getUtModifica() {
        return utModifica;
    }

    public void setUtModifica(String utModifica) {
        this.utModifica = utModifica;
    }

    public BigDecimal getVersione() {
        return this.versione;
    }

    public void setVersione(BigDecimal versione) {
        this.versione = versione;
    }

    public BigDecimal getUcuTpImpostaReg() {
        return this.ucuTpImpostaReg;
    }

    public void setUcuTpImpostaReg(BigDecimal ucuTpImpostaReg) {
        this.ucuTpImpostaReg = ucuTpImpostaReg;
    }

    public BigDecimal getUcuTpStatoDoc() {
        return this.ucuTpStatoDoc;
    }

    public void setUcuTpStatoDoc(BigDecimal ucuTpStatoDoc) {
        this.ucuTpStatoDoc = ucuTpStatoDoc;
    }

    public BigDecimal getUcuTpTipoProvv() {
        return this.ucuTpTipoProvv;
    }

    public void setUcuTpTipoProvv(BigDecimal ucuTpTipoProvv) {
        this.ucuTpTipoProvv = ucuTpTipoProvv;
    }

    public String getFlagMigrato() {
        return flagMigrato;
    }

    public void setFlagMigrato(String flagMigrato) {
        this.flagMigrato = flagMigrato;
    }

    public long getIdStoriaDocumento() {
        return idStoriaDocumento;
    }

    public void setIdStoriaDocumento(long idStoriaDocumento) {
        this.idStoriaDocumento = idStoriaDocumento;
    }

    public BigDecimal getIdAzione() {
        return idAzione;
    }

    public void setIdAzione(BigDecimal idAzione) {
        this.idAzione = idAzione;
    }

}
