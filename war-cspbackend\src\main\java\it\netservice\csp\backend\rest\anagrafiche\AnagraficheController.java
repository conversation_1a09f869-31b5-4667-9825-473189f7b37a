/**
 *
 */
package it.netservice.csp.backend.rest.anagrafiche;

import java.util.List;

import javax.annotation.security.RolesAllowed;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.Response;

import org.apache.log4j.Logger;

import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.excpetion.AnagraficheExcpetion;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.ext.service.ServiceBuilder;
import it.netservice.csp.backend.rest.AbstractCspBackendController;
import it.netservice.csp.backend.sic.service.depositi.DateUtils;
import it.netservice.penale.model.common.dettaglioricorso.AnagraficaMagistrato;

/**
 * Servizi di ricerca ricorsi per il desk del magistrato civile.
 *
 * <AUTHOR>
 */
@RolesAllowed({ "CSPCLIENT" })
@Path("/anagrafiche")
public class AnagraficheController extends AbstractCspBackendController {

    private static final Logger LOGGER = Logger.getLogger(AnagraficheController.class);

    @Inject
    private ServiceBuilder serviceBuilder;

    @GET
    @Path("/magistrati")
    public Response getmagistrati(@Context HttpServletRequest context) throws CspBackendException {
        LOGGER.info("Avvio ricerca magistrati");
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            List<AnagraficaMagistrato> magistrati = entityManager.createNamedQuery("dettaglioricorso.Magistrato.findAll").getResultList();
            LOGGER.info("Ricerca magistrati completata. Totale magistrati trovati:" + magistrati.size());
            return Response.ok(magistrati).build();
        } catch (Exception e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            throw new AnagraficheExcpetion("Errore durante la ricerca dei magistrati", RestErrorCodeEnum.FIND_MAGISTRATI_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET
    @Path("/magistratiPenaleAttivi")
    public Response getMagistratiNoPg(@Context HttpServletRequest context) throws CspBackendException {
        LOGGER.info("Avvio ricerca magistrati penale attivi");
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            List<AnagraficaMagistrato> magistrati = entityManager
                    .createNamedQuery("dettaglioricorso.Magistrato.findAllActivePenale", AnagraficaMagistrato.class)
                    .setParameter("currentDate", DateUtils.atStartOfDay(System.currentTimeMillis())).getResultList();

            LOGGER.info("Ricerca magistrati attivi cassazione penale. Totale magistrati trovati:" + magistrati.size());
            return Response.ok(magistrati).build();
        } catch (Exception e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            throw new AnagraficheExcpetion("Errore durante la ricerca dei magistrati penale attivi",
                    RestErrorCodeEnum.FIND_MAGISTRATI_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

}
