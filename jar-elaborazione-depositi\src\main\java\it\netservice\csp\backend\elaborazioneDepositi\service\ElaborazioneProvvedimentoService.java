/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package it.netservice.csp.backend.elaborazioneDepositi.service;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.EntityManager;

import org.apache.log4j.Logger;

import it.netservice.common.rest.backend.CriteriaBuilderHelper;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.excpetion.ElaborazioneException;
import it.netservice.penale.model.csp.ElaborazioneProvvedimento;

/**
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class ElaborazioneProvvedimentoService {

    private static final Logger LOGGER = Logger.getLogger(ElaborazioneProvvedimentoService.class);

    public ElaborazioneProvvedimento findElaborazioneProvvedimentoByIdDeposito(EntityManager entityManager, Long idDeposito)
            throws ElaborazioneException {
        if (idDeposito == null) {
            throw new ElaborazioneException("Riferimento deposito non corretto. id deposito null", RestErrorCodeEnum.ID_DEPOSITO_NULL);
        }
        CriteriaBuilderHelper<ElaborazioneProvvedimento> criteriaBuilder = new CriteriaBuilderHelper<>(ElaborazioneProvvedimento.class);
        return criteriaBuilder.uniqueResult(entityManager, new ElaborazioneProvvedimentoSearchParams(idDeposito));
    }

}
