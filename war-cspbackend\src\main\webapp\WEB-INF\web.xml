<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
	 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	 xmlns:web="http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
	 xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
	 id="csp_backend" version="3.0">

	<display-name>csp-backend</display-name>

	<context-param>
		<param-name>resteasy.role.based.security</param-name>
		<param-value>true</param-value>
	</context-param>

	<security-constraint>
		<web-resource-collection>
			<web-resource-name>JSONAPI</web-resource-name>
			<url-pattern>/jsonapi/*</url-pattern>
			<http-method-omission>OPTIONS</http-method-omission>
		</web-resource-collection>
		<auth-constraint>
			<role-name>CSPCLIENT</role-name>
			<role-name>GLCASS</role-name>
		</auth-constraint>
	</security-constraint>

	<login-config>
		<auth-method>BASIC</auth-method>
		<realm-name>default</realm-name>
	</login-config>

	<security-role>
		<role-name>CSPCLIENT</role-name>
		<role-name>GLCASS</role-name>
	</security-role>

</web-app>