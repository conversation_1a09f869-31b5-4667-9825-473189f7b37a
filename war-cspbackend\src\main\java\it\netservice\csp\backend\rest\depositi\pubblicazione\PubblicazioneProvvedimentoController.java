package it.netservice.csp.backend.rest.depositi.pubblicazione;

import javax.annotation.security.RolesAllowed;
import javax.inject.Inject;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.Response;

import org.apache.log4j.Logger;

import com.auth0.jwt.interfaces.DecodedJWT;

import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.elaborazioneDepositi.service.ElaborazioneProvvedimentoService;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.PubblicazioneException;
import it.netservice.csp.backend.ext.service.ServiceBuilder;
import it.netservice.csp.backend.pubblicazioneDepositi.PubblicazioneService;
import it.netservice.csp.backend.pubblicazioneDepositi.UfficioCopieService;
import it.netservice.csp.backend.rest.AbstractCspBackendController;
import it.netservice.csp.backend.rest.auth.NoSicAuthService;
import it.netservice.csp.backend.rest.auth.SecurityUtil;
import it.netservice.csp.backend.rest.auth.TokenManager;

/**
 * Classe per la pubblicazione dei provvedimenti del Magistrato verso il documentale
 *
 * <AUTHOR>
 */
@NoSicAuthService
@Path("/invocaPubblicazione")
public class PubblicazioneProvvedimentoController extends AbstractCspBackendController {

    private static final Logger LOGGER = Logger.getLogger(PubblicazioneProvvedimentoController.class);

    @Inject
    private ServiceBuilder serviceBuilder;

    @Inject
    private ElaborazioneProvvedimentoService elaborazioneProvvedimentoService;

    @Inject
    private UfficioCopieService ufficioCopieService;

    @Inject
    private PubblicazioneService pubblicazioneService;

    @Inject
    private TokenManager tokenManager;

    @Inject
    private SecurityUtil util;

    @GET
    @RolesAllowed("GLCASS")
    public Response ritrasmettiPubblicazione(@Context HttpServletRequest context, @QueryParam("idCat") String idCat,
            @QueryParam("operatore") String operatore) throws Exception {
        LOGGER.info("Notifica ritrasmetti pubblicazione  provvedimento in arrivo. idCat:" + idCat);
        if (idCat == null) {
            throw new CspBackendException("Riferimento deposito non corretto. idCat null", RestErrorCodeEnum.IDCAT_DEPOSITO_NULL, 433);
        }
        try {
            pubblicazioneService.ritrasmettiPubblicazione(new Long(idCat), operatore);
            pubblicazioneService.inviaUCU(new Long(idCat), operatore);
            return Response.ok("ritrasmissione " + idCat).build();
        } catch (Exception ex) {
            LOGGER.error("Errore in ritrasmetti pubblicazione provvedimento. idCat:" + idCat, ex);
            if (ex instanceof CspBackendException) {
                throw (CspBackendException) ex;
            }
            LOGGER.error("Errore durante la ritrasmissione della pubblicazione. idCat:" + idCat);
            throw new PubblicazioneException("Errore durante la ritrasmissione della pubblicazione",
                    RestErrorCodeEnum.PUBBLICAZIONE_GENERIC_ERROR, ex);
        }
    }

    /**
     * Pubblicazione del provvedimento verso ufficio copie
     *
     * @param context
     *            contesto servlet
     * @param idDeposito
     *            identificativo deposito
     * @return Response con esito dell'operazione
     * @throws CspBackendException
     */
    @GET
    @Path("/inviaUCU")
    public Response inviaUCU(@Context HttpServletRequest context, @QueryParam("idDeposito") Long idDeposito) throws CspBackendException {
        if (idDeposito == null) {
            throw new PubblicazioneException("Riferimento deposito non corretto. idDeposito null");
        }
        LOGGER.info("Richiesta invio ufficio copie. idDeposito:" + idDeposito);
        DecodedJWT jwt = tokenManager.verifyToken(context.getHeader("x-auth-token"));
        try {
            pubblicazioneService.inviaUCU(idDeposito, util.getOperatore(jwt));
            LOGGER.info("Invio ufficio copie effettuato correttamente.");
            return Response.ok("Deposito " + idDeposito + " inviato a UCU correttamente").build();
        } catch (Throwable ex) {
            LOGGER.error("Errore invio verso ufficio copie. idDesposito:" + idDeposito, ex);
            if (ex instanceof CspBackendException) {
                throw (CspBackendException) ex;
            }
            throw ex;
        }
    }

    private String decodeContentType(String fileName) {
        String extension = fileName.substring(fileName.length() - 3).toLowerCase();
        switch (extension) {
            case "pdf":
                return "application/pdf";
            case "xml":
                return "application/xml";
            case "p7m":
                return "application/x-pkcs7-mime";
            default:
                return null;
        }
    }
}
