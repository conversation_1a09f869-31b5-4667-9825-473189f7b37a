package it.netservice.csp.backend.rest.depositi.pubblicazione;

import javax.annotation.security.RolesAllowed;
import javax.inject.Inject;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.Response;

import org.apache.log4j.Logger;

import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.pubblicazioneDepositi.PubblicazioneService;
import it.netservice.csp.backend.rest.AbstractCspBackendController;

@Path("/pubblicazioneSic")
@RolesAllowed("CSPCLIENT")
public class PubblicazioneSic extends AbstractCspBackendController {

    private static final Logger LOGGER = Logger.getLogger(PubblicazioneSic.class);
    @Inject
    PubblicazioneService pubblicazioneService;

    @GET
    @Path("/inizioPubblicazioneSic")
    public Response inizioPubblicazioneSic(@Context HttpServletRequest context, @QueryParam("idPubblicazioneSic") String idPubblicazioneSic)
            throws CspBackendException {
        LOGGER.info("Inizio pubblicazione provvedimento da SIC. idPubblicazioneSic = " + idPubblicazioneSic);
        try {
            // pubblicazioneService.inizioPubblicazioneSic(new Long(idPubblicazioneSic));
            return Response.ok("" + idPubblicazioneSic).build();
        } catch (Exception ex) {
            throw ex;
        }
    }

    @GET
    @Path("/finePubblicazioneSic")
    public Response finePubblicazioneSic(@Context HttpServletRequest context, @QueryParam("idPubblicazioneSic") String idPubblicazioneSic)
            throws CspBackendException {
        LOGGER.info("Fine pubblicazione provvedimento da SIC. idPubblicazioneSic = " + idPubblicazioneSic);
        try {
            // pubblicazioneService.finePubblicazioneSic(new Long(idPubblicazioneSic));
            return Response.ok("" + idPubblicazioneSic).build();
        } catch (Exception ex) {
            throw ex;
        }
    }
}
