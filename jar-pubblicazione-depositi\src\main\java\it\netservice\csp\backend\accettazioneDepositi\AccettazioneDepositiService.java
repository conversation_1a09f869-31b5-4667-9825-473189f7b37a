package it.netservice.csp.backend.accettazioneDepositi;

import java.net.MalformedURLException;
import java.rmi.RemoteException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.EntityTransaction;
import javax.persistence.PersistenceUnit;
import javax.ws.rs.core.Response;
import javax.xml.rpc.ServiceException;

import org.apache.log4j.Logger;

import it.netservice.common.rest.CommonRestWarning;
import it.netservice.csp.backend.dto.ErrorTypeEnum;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.elaborazioneDepositi.service.AggiornamentoStatoProvvDeskService;
import it.netservice.csp.backend.elaborazioneDepositi.service.DepositoService;
import it.netservice.csp.backend.elaborazioneDepositi.service.ElaborazioneDepositoService;
import it.netservice.csp.backend.excpetion.AccettazioneDepositiExcpetion;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.CspCommonRestWarningException;
import it.netservice.csp.backend.ext.service.ServiceBuilder;
import it.netservice.csp.backend.pubblicazioneDepositi.PubblicazioneSemaforo;
import it.netservice.csp.backend.pubblicazioneDepositi.PubblicazioneService;
import it.netservice.csp.backend.sic.service.GestioneSicService;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.csp.backend.ws.accettazione.ServiziAccettazione;
import it.netservice.csp.backend.ws.depositi.ContentSummaryList;
import it.netservice.csp.backend.ws.depositi.ContentSummaryType;
import it.netservice.csp.backend.ws.depositi.EnvSummaryList;
import it.netservice.csp.backend.ws.depositi.EnvSummaryType;
import it.netservice.penale.model.common.dettaglioricorso.NotichePenale;
import it.netservice.penale.model.common.dettaglioricorso.ProvvedimentoNotePenale;
import it.netservice.penale.model.common.dettaglioricorso.ProvvedimentoPenale;
import it.netservice.penale.model.common.dettaglioricorso.StoricoProvvedimento;
import it.netservice.penale.model.csp.Atto;
import it.netservice.penale.model.csp.ElaborazioneProvvedimento;
import it.netservice.penale.model.enums.StatoProvvedimento;
import it.netservice.penale.model.sic.Deposito;
import it.netservice.penale.model.sic.ElaborazioneDeposito;
import it.netservice.penale.model.sic.RicorsoSic;

@ApplicationScoped
public class AccettazioneDepositiService {

    private static final Logger LOGGER = Logger.getLogger(AccettazioneDepositiService.class);

    @PersistenceUnit(unitName = "sic-model-penale")
    protected EntityManagerFactory emfCaricamento;

    @Inject
    private ElaborazioneDepositoService elaborazioneDepositoService;

    @Inject
    private PubblicazioneService pubblicazioneService;
    @Inject
    private AccettazioneUtilService accettazioneUtilService;

    @Inject
    private ServiceBuilder serviceBuilder;

    @Inject
    private GestioneSicService gestioneSic;

    @Inject
    private DepositoService depositoService;

    @Inject
    private AggiornamentoStatoProvvDeskService aggiornamentoStatoProvvDeskService;

    public Response accetta(String operatore, Long idUtente, Long idDeposito, boolean newTransaction, EntityManager entityManagerArg,
            EntityTransaction transactionArg, FileSignedResult signedResult, String messaggio) throws Throwable {
        LOGGER.info("Inizio accettazione per deposito:" + idDeposito);
        if (operatore == null) {
            throw new CspBackendException("Sessione scaduta o assente", RestErrorCodeEnum.SESSION_EXPIRED, 433);
        }

        EntityManager entityManager;
        EntityTransaction transaction;
        ElaborazioneDeposito elabDep = null;
        if (newTransaction) {
            entityManager = emfCaricamento.createEntityManager();
            transaction = entityManager.getTransaction();
            transaction.begin();
        } else {
            entityManager = entityManagerArg;
            transaction = transactionArg;
        }

        try {
            if (idDeposito == null) {
                throw new AccettazioneDepositiExcpetion("Riferimento deposito non corretto. idDeposito null",
                        RestErrorCodeEnum.ID_DEPOSITO_NULL);
            }

            Deposito deposito = getAndCheckDeposito(idUtente, idDeposito, entityManager);

            elabDep = elaborazioneDepositoService.findElaborazioneDepositoByIdDeposito(entityManager, idDeposito, transaction);

            if (deposito.isDepositoMagistratoPubblicaInAccettazione()) {
                LOGGER.info("Inizio controlli pubblicazione provvedimento");
                pubblicazioneService.checkPubblicazione((ElaborazioneProvvedimento) elabDep, deposito);

                PubblicazioneSemaforo.getInstance().registraPubblicazione(elabDep.getDeposito().getIdCat(), false);
            }

            LOGGER.info(String.format("Ricerca ricorso %s/%s", elabDep.getNumeroRicorso(), elabDep.getAnnoRicorso()));
            SicPenaleRepository repo = new SicPenaleRepository(entityManager);
            if (repo.getRicorsoByElaborazioneDeposito(elabDep) == null) {
                LOGGER.error(String.format("Impossibile accettare il deposito: ricorso %s/%s non trovato.", elabDep.getNumeroRicorso(),
                        elabDep.getAnnoRicorso()));
                throw new CspCommonRestWarningException(String.format("Impossibile accettare il deposito: ricorso %s/%s non trovato.",
                        elabDep.getNumeroRicorso(), elabDep.getAnnoRicorso()), RestErrorCodeEnum.RICORSO_NOT_FOUND);
            }
            ServiziAccettazione accettazioneGL = serviceBuilder.getAccettazioneService();

            LOGGER.info("Accettazione SIC");

            RicorsoSic ricorso = gestioneSic.accettaDeposito(entityManager, elabDep, operatore);
            // LOGGING NRG/ID_UDIEN - After gestioneSic.accettaDeposito
            if (ricorso != null) {
                LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] accetta - Dopo gestioneSic.accettaDeposito. IDCAT: %d, Ricorso.nrg: %s",
                        idDeposito, ricorso.getNrg()));
            } else {
                LOGGER.warn(String.format(
                        "[LOGGING NRG/ID_UDIEN] accetta - Dopo gestioneSic.accettaDeposito. IDCAT: %d, RicorsoSic restituito è NULL.",
                        idDeposito));
            }
            /** AGGIORNAMENTO STATO RIMOSSO PER CREAZIONE TRIGGER SULLA PENALE_PROVV_CHANGE_STATUS **/
            /*
             * ricorso.setStatoProvvedimento(StatoProvvedimento.MINUTA_ACCETTATA); repo.merge(ricorso);
             */
            StringBuilder sb = new StringBuilder();
            String nrgReale = ricorso.getNrgReale() != null ? ricorso.getNrgReale().toString() : "";

            String annoRicorso = nrgReale.substring(0, 4);
            String numeroRicorso = Long.valueOf(nrgReale.substring(4, 10)).toString();

            sb.append(numeroRicorso).append('/').append(annoRicorso);

            accettaAtti(entityManager, deposito, elabDep, ricorso, signedResult);

            try {
                LOGGER.info("Inizio accettazione gl cass idDeposito:" + idDeposito + "sb:" + sb.toString());
                accettazioneGL.assegnaDeposito(idDeposito.toString(), ricorso.getNrg().toString(),
                        System.getProperty("gestorelocale.registro"), false);
                String msg = "";
                accettazioneGL.accettaDeposito(idDeposito.toString(), false, false, sb.toString(), msg);

            } catch (Exception ex) {
                if (ex.getMessage() != null && ex.getMessage().contains("Contenuto della busta gia' accettato")) {
                    LOGGER.error("Busta gia' accettata lato GL. idDeposito:" + idDeposito, ex);
                    // OK qua nn va effettuato il rollback perchè è stato gia accettato da capire se la data accettazione deve essere messa
                    // quella del glcass
                    /* TODO */
                    // throw ex;
                } else if (ex.getMessage() != null && ex.getMessage().contains("Deposito principale non ancora accettato")) {
                    LOGGER.warn("Deposito principale non ancora accettato. idDeposito:" + idDeposito, ex);
                    // anche qui va effettuato il rollack altrimenti ci sarebbe incoerenza tra i dati
                    if (transaction.isActive()) {
                        transaction.rollback();
                    }
                    throw new AccettazioneDepositiExcpetion("Deposito principale non ancora accettato",
                            RestErrorCodeEnum.DEPOSITO_PRINCIPALE_NON_ACCETTATO);
                } else if (ex.getMessage() != null
                        && ex.getMessage().contains("Impossibile reperire una connessione, gruppo non gestito")) {
                    LOGGER.error("Errore lato gl-cass revert della transazione, registro non presente su GL cass. codiceRegistro:"
                            + System.getProperty("gestorelocale.registro") + ", idDeposito:" + idDeposito);
                    if (transaction.isActive()) {
                        transaction.rollback();
                    }
                    throw new AccettazioneDepositiExcpetion("Registro nel gestore locale non gestito. Contattare il CED.",
                            RestErrorCodeEnum.GL_REGISTRO_NOT_FOUND);
                } else {
                    LOGGER.warn("Errore lato gl-cass revert della transazione. idDeposito:" + idDeposito);
                    if (transaction.isActive()) {
                        transaction.rollback();
                    }
                    throw new AccettazioneDepositiExcpetion("Errore lato gl-cass", RestErrorCodeEnum.GL_GENERIC_ERROR, ex);
                }
            }
            // TODO per la demo di novembre rimane qui questa commit, ma andrebbe capito se si può spostare come ultima istruzione
            if (newTransaction) {
                // LOGGING NRG/ID_UDIEN - Query PENALE_PROVVEDIMENTI before commit
                LOGGER.info(String.format(
                        "[LOGGING NRG/ID_UDIEN] accetta - Querying PENALE_PROVVEDIMENTI for IDCAT: %d before final commit.", idDeposito));
                try {
                    javax.persistence.Query penaleProvQuery = entityManager
                            .createNativeQuery("SELECT pp.ID_UDIEN, pp.NRG FROM PENALE_PROVVEDIMENTI pp WHERE pp.FK_IDCAT = :idCatParam");
                    penaleProvQuery.setParameter("idCatParam", idDeposito);
                    // Use getResultList and check size to handle NoResultException gracefully
                    java.util.List<Object[]> results = penaleProvQuery.getResultList();
                    if (results.isEmpty()) {
                        LOGGER.warn(String.format(
                                "[LOGGING NRG/ID_UDIEN] accetta - No record found in PENALE_PROVVEDIMENTI for FK_IDCAT = %d", idDeposito));
                    } else {
                        Object[] result = results.get(0); // Assuming only one record is expected or relevant
                        Object idUdienRaw = result[0];
                        Object nrgRaw = result[1];
                        String idUdienStr = (idUdienRaw != null) ? idUdienRaw.toString() : "NULL";
                        String nrgStr = (nrgRaw != null) ? nrgRaw.toString() : "NULL";
                        LOGGER.info(String.format(
                                "[LOGGING NRG/ID_UDIEN] accetta - PENALE_PROVVEDIMENTI query result for IDCAT: %d -> ID_UDIEN: %s, NRG: %s",
                                idDeposito, idUdienStr, nrgStr));
                        if (idUdienRaw == null) {
                            LOGGER.warn(String.format(
                                    "[LOGGING NRG/ID_UDIEN] accetta - PENALE_PROVVEDIMENTI ID_UDIEN is NULL for IDCAT: %d", idDeposito));
                        }
                        if (results.size() > 1) {
                            LOGGER.warn(String.format(
                                    "[LOGGING NRG/ID_UDIEN] accetta - Multiple records found in PENALE_PROVVEDIMENTI for FK_IDCAT = %d. Logged the first one.",
                                    idDeposito));
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error(
                            String.format("[LOGGING NRG/ID_UDIEN] accetta - Error querying PENALE_PROVVEDIMENTI for IDCAT: %d", idDeposito),
                            e);
                }
                transaction.commit();
            }
            LOGGER.info("Fine accettazione GL. Deposito accettato correttamente. idDeposito:" + deposito.getIdCat() + ", nrgReale:"
                    + nrgReale + ", Deposito tipo:" + deposito.getTipo());

            String cfDestinatario = null;
            /* TODO */
            // try {
            // cfDestinatario = notificheDeskService.notificaDesk(entityManager, ricorso, elabDep, false);
            // } catch (Throwable t) {
            // LOGGER.warn("Deposito accettato correttamente. Errori durante l'invio delle notifiche.", t);
            // }
            // try {
            // aggiornamentoStatoProvvDeskService.aggiornaStatoProvvedimento(entityManager, elabDep.getDeposito().getIdCat());
            // } catch (Throwable t) {
            // LOGGER.warn("Deposito accettato correttamente. Errori durante l'aggiornamento dello stato di provvedimento.", t);
            // }

            // VIENE eseguita solo nel caso che il provvediemnto deve essere pubblicato
            if (deposito.isDepositoMagistratoPubblicaInAccettazione()) {
                try {
                    LOGGER.info("Inizio pubblicazione provvedimento e degli atti firmati:" + signedResult);
                    /*
                     * switch (elabDep.getDeposito().getTipo()) { case "MinutaOrdinanza": case "MinutaOrdinanzaInterlocutoria": case
                     * "MinutaSentenza": break; case "Sentenza": case "Ordinanza":
                     * pubblicazioneService.pubblicaProvvedimento((ElaborazioneProvvedimento) elabDep, deposito, operatore, numeroRicorso,
                     * annoRicorso); break; default: break; }
                     */
                    pubblicazioneService.pubblicaProvvedimento((ElaborazioneProvvedimento) elabDep, deposito, operatore, numeroRicorso,
                            annoRicorso, signedResult);
                } catch (Throwable t) {
                    pubblicazioneService.registraErrorePubblicazione(deposito, operatore, true);
                    accettazioneUtilService.aggiornaStatoDesk(repo, deposito, idUtente, entityManager,
                            StatoProvvedimento.ERRORE_DI_PUBBLICAZIONE, StatoProvvedimento.INVIATO_IN_CANCEL_PRESIDENTE, null);
                    LOGGER.warn("Deposito accettato correttamente. Errori durante la pubblicazione. idDeposito:" + idDeposito);
                    throw new CspCommonRestWarningException("Deposito accettato correttamente. Errori durante la pubblicazione.",
                            RestErrorCodeEnum.PUBBLICAZIONE_ERROR, t, ErrorTypeEnum.SUCCESS);
                }
            } else {
                accettazioneUtilService.aggiornaStatoDesk(repo, deposito, idUtente, entityManager, StatoProvvedimento.MINUTA_ACCETTATA,
                        StatoProvvedimento.INVIATO_IN_CANCELLERIA_RELATORE, messaggio);
            }

            if (cfDestinatario == null) {
                cfDestinatario = "";
            }
            return Response.ok(numeroRicorso + "/" + annoRicorso + "?" + cfDestinatario + "*&" + ricorso.getNrg()).build();
        } catch (Throwable t) {
            if (transaction.isActive()) {
                transaction.rollback();
            }
            if (t instanceof CspBackendException) {
                throw (CspBackendException) t;
            }
            LOGGER.error("Errore durante l'accettazione. idDeposito:" + idDeposito);
            throw new AccettazioneDepositiExcpetion("Errore durante l'accettazione", RestErrorCodeEnum.ACCETTAZIONE_DEPOSITI_GENERIC_ERROR,
                    t);
        } finally {
            entityManager.close();
            if (elabDep != null && elabDep.getDeposito() != null) {
                PubblicazioneSemaforo.getInstance().sbloccaPubblicazione(elabDep.getDeposito().getIdCat(), false);
            }
        }
    }

    public void rifiuta(DatiAccettazioneDepositoDto datiRifiuto, Long idUtente, String operatore)
            throws CspBackendException, MalformedURLException, ServiceException, RemoteException {
        LOGGER.info("Inizio rifiuto deposito, idDeposito:" + datiRifiuto.getIdDeposito());
        EntityManager entityManager = emfCaricamento.createEntityManager();
        SicPenaleRepository repo = new SicPenaleRepository(entityManager);
        EntityTransaction transaction = entityManager.getTransaction();
        transaction.begin();

        try {
            Deposito deposito = getAndCheckDeposito(idUtente, datiRifiuto.getIdDeposito(), entityManager);
            /**
             * Cambio stato per desk penale in tabella PENALE_PROVVEDIMENTI
             */
            Long idDeposito = deposito.getIdCat();
            ProvvedimentoPenale provvedimentoPenale = repo.findProvvedimentoByIdCat(idDeposito);
            if (provvedimentoPenale == null) {
                LOGGER.error("Impossibile rifiutare il deposito. Provvedimento non trovato. idDeposito:" + idDeposito);
                throw new CspCommonRestWarningException("Impossibile rifiutare il deposito. Provvedimento non trovato.",
                        RestErrorCodeEnum.DEPOSITO_NOT_FOUND);
            }
            // inserisci la notifica nella tabella notifiche.
            String descrizioneNotifica = "Busta Rifiutata: " + datiRifiuto.getMotivazione();
            NotichePenale notifiche = accettazioneUtilService.buildNotifica(deposito.getNrg(), provvedimentoPenale.getIdUdienza(),
                    Long.parseLong(provvedimentoPenale.getIdUtente()), descrizioneNotifica);

            /**
             * Cambio stato per desk penale in tabella PENALE_PROVV_CHANGE_STATUS
             */
            StoricoProvvedimento storicoProvvedimento = accettazioneUtilService.buildStoricoProvvedimento(provvedimentoPenale.getIdProvv(),
                    StatoProvvedimento.BUSTA_RIFIUTATA, provvedimentoPenale.getStatoProvvedimento(), idUtente);

            try {
                provvedimentoPenale.setStatoProvvedimento(StatoProvvedimento.BUSTA_RIFIUTATA);
                provvedimentoPenale.setDataUltimaModifica(Calendar.getInstance().getTime());
                repo.merge(provvedimentoPenale);
            } catch (Exception ex) {
                LOGGER.error("Errore durante l'aggiornamento del provvedimento. idDeposito:" + idDeposito + ", idProvvedimento:"
                        + provvedimentoPenale.getIdProvv());
                throw new AccettazioneDepositiExcpetion("Errore durante l'aggiornamento del provvedimento",
                        RestErrorCodeEnum.ACCETTAZIONE_DEPOSITI_SAVE_ERROR, ex);

            }

            try {
                entityManager.persist(storicoProvvedimento);
            } catch (Exception ex) {
                LOGGER.error("Errore durante salvataggio dello storico provvedimento. idDeposito:" + idDeposito + ", idProvvedimento:"
                        + provvedimentoPenale.getIdProvv());
                throw new AccettazioneDepositiExcpetion("Errore durante salvataggio dello storico provvedimento",
                        RestErrorCodeEnum.ACCETTAZIONE_DEPOSITI_SAVE_ERROR, ex);
            }
            // persisto la notifica quando viene rifiutata la busta
            try {
                entityManager.persist(notifiche);
            } catch (Exception ex) {
                LOGGER.error("Errore durante salvataggio della notifica. idDeposito:" + idDeposito + ", idProvvedimento:"
                        + provvedimentoPenale.getIdProvv());
                throw new AccettazioneDepositiExcpetion("Errore durante salvataggio della notifica",
                        RestErrorCodeEnum.ACCETTAZIONE_DEPOSITI_SAVE_ERROR, ex);
            }
            ServiziAccettazione accettazione = serviceBuilder.getAccettazioneService();
            DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
            String dataRifiuto = dateFormat.format(new Date());
            try {
                accettazione.assegnaDeposito(datiRifiuto.getIdDeposito().toString(), deposito.getNrg().toString(),
                        System.getProperty("gestorelocale.registro"), deposito.getIntroduttivo());

                accettazione.rifiutaDeposito(String.valueOf(datiRifiuto.getIdDeposito()),
                        "Atti rifiutati il " + dataRifiuto + ". " + datiRifiuto.getMotivazione());

            } catch (Exception ex) {
                if (ex.getMessage() != null && ex.getMessage().contains("Errore di gestione registri, impossibile eseguire")) {
                    LOGGER.warn("Busta gia' rifiutata lato GL. idCatBusta:" + deposito.getIdCat() + ", nrg:" + deposito.getNrg());
                } else {
                    LOGGER.error("Errore durante salvataggio del provvedimento. idDeposito:" + idDeposito + ", nrg:" + deposito.getNrg());
                    throw new AccettazioneDepositiExcpetion("Errore durante salvataggio del provvedimento",
                            RestErrorCodeEnum.ACCETTAZIONE_DEPOSITI_SAVE_ERROR);
                }
            }
            // inizio inserimento atti rifiutati nella tabella anche in caso che il rifiuto è stato gia eseguito
            LOGGER.info("Inserimento atti rifiutati nella tabella atti con idCatBusta:" + deposito.getIdCat());
            ElaborazioneDeposito elaborazioneDepositoByIdDeposito = elaborazioneDepositoService
                    .findElaborazioneDepositoByIdDeposito(entityManager, idDeposito, transaction);
            EnvSummaryList querySummary = serviceBuilder.getLogEsitiService().querySummary(new String[] { String.valueOf(idDeposito) });
            if (querySummary == null) {
                throw new CspBackendException(String.format("querySummary null. idDeposito=%s", idDeposito),
                        RestErrorCodeEnum.ACCETTAZIONE_DEPOSITI_GENERIC_ERROR, 433);
            }
            EnvSummaryType[] envSummaries = querySummary.getEnvSummary();
            if (envSummaries == null) {
                throw new CspBackendException(String.format("envSummary null. idDeposito=%s", idDeposito),
                        RestErrorCodeEnum.ACCETTAZIONE_DEPOSITI_GENERIC_ERROR, 433);
            }
            salvataggioAtti(entityManager, deposito, (ElaborazioneProvvedimento) elaborazioneDepositoByIdDeposito,
                    deposito.getNrg().toString(), null, idDeposito, envSummaries, true);
            LOGGER.info("Fine inserimento atti deposito:" + deposito.getIdCat());
            // salviamo la note sul db
            ProvvedimentoNotePenale note = accettazioneUtilService.buildNota(provvedimentoPenale.getIdProvv(), datiRifiuto.getMotivazione(),
                    idUtente, null);

            try {
                entityManager.persist(note);
            } catch (Exception ex) {
                LOGGER.error("Errore durante salvataggio delle note. idDeposito:" + idDeposito + ", idProvvedimento:"
                        + provvedimentoPenale.getIdProvv());
                throw new AccettazioneDepositiExcpetion("Errore durante salvataggio delle note",
                        RestErrorCodeEnum.ACCETTAZIONE_DEPOSITI_SAVE_ERROR, ex);
            }
            deposito.setStato(Deposito.Stato.RIFIUTATO);

            entityManager.merge(deposito);
            finalizzaRifiutoDeposito(datiRifiuto.getIdDeposito(), datiRifiuto.getMotivazione(), operatore, entityManager);
            transaction.commit();
            LOGGER.info("Fine rifiuto deposito:" + deposito);
        } catch (Throwable e) {
            if (transaction.isActive()) {
                transaction.rollback();
            }
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            throw e;
        } finally {
            if (entityManager.isOpen()) {
                entityManager.close();
            }
        }
    }

    private Deposito getAndCheckDeposito(Long idUtente, Long idDeposito, EntityManager entityManager) throws CspBackendException {
        Deposito deposito = entityManager.find(Deposito.class, idDeposito);
        if (deposito == null) {
            LOGGER.warn("Impossibile eseguire l'operazione. Deposito non trovato. idDeposito:" + idDeposito);
            throw new CspCommonRestWarningException("Impossibile accettare il deposito. Deposito non trovato",
                    RestErrorCodeEnum.DEPOSITO_NOT_FOUND);
        }
        depositoService.checkHasBeenValorisedByTheSic(entityManager, deposito.getTipo(), deposito.getIdCat());
        if (!Deposito.Stato.NEW.equals(deposito.getStato())) {
            LOGGER.warn("Impossibile eseguire l'operazione. Il deposito è già stato accettato/rifiutato. idDeposito:" + idDeposito);
            throw new CspCommonRestWarningException("Impossibile accettare il deposito. Il deposito è già stato accettato/rifiutato",
                    RestErrorCodeEnum.DEPOSITO_ACCETTATO_RIFIUTATO);
        }
        if (deposito.getIdUtente() == null || !deposito.getIdUtente().equals(idUtente)) {
            LOGGER.warn("Impossibile eseguire l'operazione. L'utente deve essere lo stesso che lo ha preso in carico. idDeposito:"
                    + idDeposito + ", idUtente deposito:" + deposito.getIdUtente());
            throw new CspCommonRestWarningException(
                    "Impossibile eseguire l'operazione. L'utente deve essere lo stesso che lo ha preso in carico.",
                    RestErrorCodeEnum.USER_DEPOSITO_DIFF);
        }
        return deposito;
    }

    /**
     * Servizio usato per generare l'errore pubblicazione usato solo per test
     *
     * @param deposito
     * @param operatore
     * @param entityManager
     */
    public void errorePubblicazioneTest(Deposito deposito, String operatore, EntityManager entityManager, Long idUtente) throws Throwable {
        pubblicazioneService.registraErrorePubblicazione(deposito, operatore, true);
        SicPenaleRepository repo = new SicPenaleRepository(entityManager);
        accettazioneUtilService.aggiornaStatoDesk(repo, deposito, idUtente, entityManager, StatoProvvedimento.ERRORE_DI_PUBBLICAZIONE,
                StatoProvvedimento.INVIATO_IN_CANCEL_PRESIDENTE, null);
        throw new CspCommonRestWarningException("Deposito accettato correttamente. Errori durante la pubblicazione.",
                RestErrorCodeEnum.PUBBLICAZIONE_ERROR);
    }

    private void accettaAtti(EntityManager entityManager, Deposito deposito, ElaborazioneDeposito elabDep, RicorsoSic ricorso,
            FileSignedResult signedResult)
            throws CspBackendException, NumberFormatException, ServiceException, MalformedURLException, RemoteException {

        LOGGER.info("Preaccettazione atti");

        Long idDeposito = deposito.getIdCat();
        EnvSummaryList querySummary = serviceBuilder.getLogEsitiService().querySummary(new String[] { String.valueOf(idDeposito) });
        if (querySummary == null) {
            throw new CspBackendException(String.format("querySummary null. idDeposito=%s", idDeposito),
                    RestErrorCodeEnum.ACCETTAZIONE_DEPOSITI_GENERIC_ERROR, 433);
        }
        EnvSummaryType[] envSummaries = querySummary.getEnvSummary();
        if (envSummaries == null) {
            throw new CspBackendException(String.format("envSummary null. idDeposito=%s", idDeposito),
                    RestErrorCodeEnum.ACCETTAZIONE_DEPOSITI_GENERIC_ERROR, 433);
        }
        salvataggioAtti(entityManager, deposito, (ElaborazioneProvvedimento) elabDep, ricorso.getNrg().toString(), signedResult, idDeposito,
                envSummaries, false);
        deposito.setRicorso(ricorso);
        deposito.setDataAccettazione(new Date().getTime());
        // if (deposito.isDepositoMagistratoPubblicaInAccettazione()) {
        // deposito.setStato(Deposito.Stato.INPUBBLICAZIONE);
        // } else {
        deposito.setStato(Deposito.Stato.ACCETTATO);
        // }
        entityManager.merge(deposito);

        LOGGER.info("Fine inserimento atti");
    }

    /**
     * Elaboro il deposito del glcass e salvo gli atti nel DB,
     *
     * @param entityManager
     * @param deposito
     * @param elabDep
     * @param nrg
     * @param signedResult
     * @param idDeposito
     * @param envSummaries
     * @param rifiutata
     * @throws CspBackendException
     */

    public static void salvataggioAtti(EntityManager entityManager, Deposito deposito, ElaborazioneProvvedimento elabDep, String nrg,
            FileSignedResult signedResult, Long idDeposito, EnvSummaryType[] envSummaries, boolean rifiutata) throws CspBackendException {
        LOGGER.info("Inizio salvataggio atti. idDeposito:" + idDeposito);
        for (EnvSummaryType envSummary : envSummaries) {
            ContentSummaryList contents = envSummary.getContents();
            if (contents == null) {
                throw new CspBackendException(String.format("contents null. idDeposito=%s", idDeposito),
                        RestErrorCodeEnum.ACCETTAZIONE_DEPOSITI_GENERIC_ERROR, 433);
            }
            ContentSummaryType[] contentSummaryTypes = contents.getContentSummary();
            if (contentSummaryTypes == null) {
                throw new CspBackendException(String.format("contentSummary null. idDeposito=%s", idDeposito),
                        RestErrorCodeEnum.ACCETTAZIONE_DEPOSITI_GENERIC_ERROR, 433);
            }
            for (ContentSummaryType contentSummaryType : contentSummaryTypes) {
                // allegato complementare
                if (contentSummaryType.getCatId() == null) {
                    continue;
                }

                Long idCat = getIdCatFileSignedAndPublish(deposito, elabDep, signedResult, rifiutata, contentSummaryType);
                Atto atto = new Atto();
                atto.setIdCat(idCat);
                String idContenitore = String.valueOf(idDeposito);
                /*
                 * if (elabDep instanceof ElaborazioneDepositoComplementare) { Query query =
                 * entityManager.createNativeQuery("select max(idcat) from CSPBACKEND_DEPOSITI where refid = '" +
                 * ((ElaborazioneDepositoComplementare) elabDep).getRefIdPrincipale() + "' and tipo <> 'DepositoComplementare'"); BigDecimal
                 * idCont = (BigDecimal) query.getSingleResult(); idContenitore = String.valueOf(idCont); }
                 */
                atto.setIdContenitore(idContenitore);
                atto.setIdCatBusta(String.valueOf(idDeposito));
                atto.setNrg(nrg);
                atto.setAutore(envSummary.getSender());
                atto.setTipoGl(contentSummaryType.getDocType());
                atto.setTipo(deposito.getTipo());
                atto.setNomeFile(contentSummaryType.getName());
                atto.setData(new Date().getTime());
                atto.setTelematico(true);
                entityManager.merge(atto);

                LOGGER.info("Salvataggio atti completato.");
            }
        }
    }

    private static Long getIdCatFileSignedAndPublish(Deposito deposito, ElaborazioneProvvedimento elabDep, FileSignedResult signedResult,
            boolean rifiutata, ContentSummaryType contentSummaryType) {
        Long idCat = Long.valueOf(contentSummaryType.getCatId());
        // CAso in cui il provvedimento e pubblicato

        // if (!rifiutata && deposito.isDepositoMagistratoPubblicaInAccettazione() && contentSummaryType.getDocType() != null) {
        // switch (contentSummaryType.getDocType()) {
        // case "ATTO":
        // idCat = elabDep.getIdCatControfirmato();
        // break;
        // case "DA":
        // idCat = signedResult != null && signedResult.idAttoControfirmatoXML != null ? Long
        // .valueOf(signedResult.idAttoControfirmatoXML.getId()) : idCat;
        // break;
        // case "SM":
        // idCat = signedResult != null && signedResult.idAttoControfirmatoOscurato != null ? Long
        // .valueOf(signedResult.idAttoControfirmatoOscurato.getId()) : idCat;
        // break;
        // default:
        // break;
        // }
        // }
        return idCat;
    }

    public void invalidaPagamentiGL(ElaborazioneDeposito elabDep, String nrg) throws CommonRestWarning {
        // boolean success = true;
        // for (SpeseGiustiziaElaborazioneDeposito spesa : elabDep.getSpeseGiustizia()) {
        // for (PagamentoElaborazioneDeposito pag : spesa.getEstremiPagamento()) {
        // try {
        // PagamentiServiceHelper.DatiAssociazionePagamento datiPag = new PagamentiServiceHelper.DatiAssociazionePagamento();
        // Date date = new Date();
        // if (pag.getDataPagamento() != null) {
        // date = new Date(pag.getDataPagamento());
        // }
        // datiPag.setDataPagamento(date);
        // datiPag.setImporto(pag.getImporto().floatValue());
        // datiPag.setIdFascicolo(nrg);
        //
        // String codicePagamento = null;
        //
        // if (pag instanceof BollettinoPostaleElaborazioneDeposito) {
        // datiPag.setTipoPagamento(TipoPagamento.BOLLETTINO_POSTALE);
        // codicePagamento = ((BollettinoPostaleElaborazioneDeposito) pag).getCodAgenziaPoste() + "-"
        // + ((BollettinoPostaleElaborazioneDeposito) pag).getCodVcyPoste();
        // } else if (pag instanceof MarcaBolloElaborazioneDeposito) {
        // datiPag.setTipoPagamento(TipoPagamento.MARCA_DA_BOLLO);
        // codicePagamento = ((MarcaBolloElaborazioneDeposito) pag).getCodMarca();
        // } else if (pag instanceof ModF23ElaborazioneDeposito) {
        // codicePagamento = ((ModF23ElaborazioneDeposito) pag).getCodF23();
        // datiPag.setTipoPagamento(TipoPagamento.F23);
        // }
        // datiPag.setCodicePagamento(codicePagamento);
        // datiPag.setCausale(" ");
        // datiPag.setPagatore(null);
        //
        // PagamentiServiceHelper.associaPagamento(serviceBuilder, datiPag, System.getProperty("gestorelocale.ufficio"));
        // LOGGER.info("Fine invalidazione pagamenti");
        // } catch (Exception ex) {
        // LOGGER.error("Errore nell'annullamento del pagamento tradizionale", ex);
        // success = false;
        // }
        // }
        //
        // }
        //
        // List<ContentDeposito> rts;
        // try {
        // rts = depositoService.getContentDeposito(serviceBuilder, elabDep.getDeposito().getIdCat().toString(), "RT");
        // } catch (Exception e) {
        // throw new CommonRestWarning("Errore invalidazione pagamenti");
        // }
        //
        // RTParser rtParser = new RTParser(serviceBuilder);
        //
        // for (ContentDeposito rt : rts) {
        // try {
        // rtParser.parse(Long.parseLong(rt.getCatId()));
        // PagamentiServiceHelper.DatiAssociazionePagamento datiPag = new PagamentiServiceHelper.DatiAssociazionePagamento();
        // datiPag.setCodicePagamento(rtParser.getCodicePagamento());
        // datiPag.setImporto(rtParser.getImporto());
        // datiPag.setPagatore(rtParser.getPagatore());
        // datiPag.setTipoPagamento(TipoPagamento.PAGAMENTO_TELEMATICO);
        // datiPag.setIdFascicolo(nrg);
        // PagamentiServiceHelper.associaPagamento(serviceBuilder, datiPag, System.getProperty("gestorelocale.ufficio"));
        // } catch (Exception ex) {
        // LOGGER.error("Errore nell'annullamento del pagamento telematico", ex);
        // success = false;
        // }
        // }
        // if (!success) {
        // throw new CommonRestWarning("Errore invalidazione pagamenti");
        // }
    }

    /**
     * Metodo di finalizzazione rifiuto. Le operazioni vengono fatte in una transazione separata per evitare che il rifiuto non vada a buon
     * fine perchè la creazione dell'elaborazioneDeposito va in errore (es. campi obbligatori db mancanti)
     *
     * @param idDeposito
     * @param operatore
     */
    public void finalizzaRifiutoDeposito(Long idDeposito, String motivazione, String operatore, EntityManager entityManager) {
        EntityTransaction transaction = entityManager.getTransaction();
        try {
            ElaborazioneDeposito elabDep = elaborazioneDepositoService.findElaborazioneDepositoByIdDeposito(entityManager, idDeposito,
                    transaction);
            gestioneSic.rifiutaDeposito(entityManager, elabDep, operatore);

            // List<StoriaSpoglio> storiaSpoglio = getStoriaSpoglio(elabDep.getDeposito().getNrg() + "", entityManager);
            // boolean spoglioDefinito = false;
            // boolean esitoSpoglio = false;
            //
            // for (StoriaSpoglio storia : storiaSpoglio) {
            // if (TipoStoriaSpoglio.getTipoProvvNoCambio().contains(storia.getAttivita())
            // || TipoStoriaSpoglio.DepositoEsitoSpoglioRifiutato.equals(storia.getAttivita())) {
            // continue;
            // }
            // if (TipoStoriaSpoglio.getTipoProvvSpoglioDef().contains(storia.getAttivita())) {
            // spoglioDefinito = true;
            // break;
            // }
            // if (TipoStoriaSpoglio.getTipoProvvEsitoSpoglio().contains(storia.getAttivita())) {
            // esitoSpoglio = true;
            // break;
            // }
            // }
            //
            // CriteriaBuilderHelper<EsitoSpoglioRic> criteriaBuilder = new CriteriaBuilderHelper<>(EsitoSpoglioRic.class);
            // List<EsitoSpoglioRic> esitoList = criteriaBuilder.resultList(entityManager,
            // new SearchParamsEsitoSpoglioRic(elabDep.getDeposito().getIdCat() + ""));
            // EsitoSpoglioRic esitoNrg = null;
            // if (esitoList != null && esitoList.size() > 0) {
            // esitoNrg = esitoList.get(0);
            // }
            // if (esitoNrg != null) {
            // if (!spoglioDefinito && !esitoSpoglio) {
            // CriteriaBuilderHelper<Spoglio> criteriaBuilderSpoglio = new CriteriaBuilderHelper<>(Spoglio.class);
            // List<Spoglio> spoglioList = criteriaBuilderSpoglio.resultList(entityManager,
            // new SearchParamsSpoglio(elabDep.getDeposito().getNrg() + ""));
            // if (spoglioList.size() > 0) {
            // Spoglio spoglioToUpdate = spoglioList.get(0);
            // spoglioToUpdate.setTipoProvvLav(TipoProvvedimentoLavorazione.NESSUNO);
            // entityManager.merge(spoglioToUpdate);
            // }
            // } else if (spoglioDefinito) {
            // CriteriaBuilderHelper<Spoglio> criteriaBuilderSpoglio = new CriteriaBuilderHelper<>(Spoglio.class);
            // List<Spoglio> spoglioList = criteriaBuilderSpoglio.resultList(entityManager,
            // new SearchParamsSpoglio(elabDep.getDeposito().getNrg() + ""));
            // if (spoglioList.size() > 0) {
            // Spoglio spoglioToUpdate = spoglioList.get(0);
            // spoglioToUpdate.setTipoProvvLav(TipoProvvedimentoLavorazione.NESSUNO);
            // entityManager.merge(spoglioToUpdate);
            // }
            // } else if (esitoSpoglio) {
            // esitoNrg.setIdCat(null);
            // esitoNrg.setDepositoEffettuato(false);
            // esitoNrg.setDataDeposito(null);
            // entityManager.merge(esitoNrg);
            // }
            // insertStoriaSpoglio(esitoNrg.getNrg(), TipoStoriaSpoglio.DepositoEsitoSpoglioRifiutato, "CSC", motivazione, entityManager);
            // }
        } catch (Throwable ex) {
            LOGGER.error("Errore durante la finalizzazione del rifiuto. idDeposito:" + idDeposito, ex);
        } finally {
            entityManager.close();
        }

    }

}
