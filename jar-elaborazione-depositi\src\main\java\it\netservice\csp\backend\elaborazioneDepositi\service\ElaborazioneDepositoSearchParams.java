/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package it.netservice.csp.backend.elaborazioneDepositi.service;

import it.netservice.common.rest.backend.CriteriaParametersBackend;
import it.netservice.penale.model.sic.ElaborazioneDeposito;

/**
 *
 * <AUTHOR>
 */
public class ElaborazioneDepositoSearchParams extends CriteriaParametersBackend<ElaborazioneDeposito> {

    @SearchParameter(operator = Operator.EQUAL)
    String id = null;

    @SearchParameter(joins = {
            @SearchJoin(property = "deposito") }, property = "idCat", operator = CriteriaParametersBackend.Operator.EQUAL)
    Long idCatDeposito = null;

    ElaborazioneDepositoSearchParams(String id) {
        this.id = id;
    }

    ElaborazioneDepositoSearchParams(Long idCatDeposito) {
        this.idCatDeposito = idCatDeposito;
    }
}
