package it.netservice.csp.backend.common;

import java.util.ArrayList;
import java.util.List;

import javax.enterprise.context.ApplicationScoped;

import org.apache.log4j.Logger;

import it.netservice.adnclient.AdnClient;
import it.netservice.adnclient.AdnClientException;
import it.netservice.adnclient.AdnSearchFilter;
import it.netservice.adnclient.AdnSearchKey;
import it.netservice.adnclient.AdnSession;
import it.netservice.adnclient.AdnUser;

/**
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class AdnHandler {

    private static final Logger LOGGER = Logger.getLogger(AdnHandler.class);

    private String ldapUserBaseDnMagistrati = System.getProperty("ldap.userBaseDnMagistrati");

    private final AdnClient adnClient;

    public AdnHandler() {
        try {
            if (isNullOrEmpty(System.getProperty("ldap.host")) || isNullOrEmpty(System.getProperty("ldap.port"))
                    || isNullOrEmpty(System.getProperty("ldap.user")) || isNullOrEmpty(System.getProperty("ldap.password"))
                    || isNullOrEmpty(System.getProperty("ldap.userBaseDnMagistrati"))) {
                throw new AdnClientException("Errore nell'inizializzazione dell'adn, non tutte le proprietà sono state impostate.");
            }
            adnClient = AdnClient.getInstance();
            adnClient.init(System.getProperty("ldap.host"), System.getProperty("ldap.port"), System.getProperty("ldap.user"),
                    System.getProperty("ldap.password"));
        } catch (Exception ex) {
            throw new RuntimeException("Impossibile inizializzare il gestore ADN", ex);
        }
    }

    public AdnUser getUtente(String codiceFiscale) throws AdnClientException {
        try (AdnSession adnSession = adnClient.startSession()) {
            List<AdnSearchFilter> filters = new ArrayList<>();
            filters.add(new AdnSearchFilter(AdnSearchKey.NOMEUFFICIO, "CASSAZIONE"));
            filters.add(new AdnSearchFilter(AdnSearchKey.CODICEFISCALE, codiceFiscale));
            List<AdnUser> result = adnSession.getUtenti(ldapUserBaseDnMagistrati, filters);
            return !result.isEmpty() ? result.get(0) : null;
        }
    }

    private boolean isNullOrEmpty(String toCheck) {
        return toCheck == null || toCheck.isEmpty();
    }

}
