package it.netservice.csp.backend.accettazioneDepositi;

import java.util.Calendar;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.EntityTransaction;
import javax.persistence.PersistenceUnit;

import org.apache.log4j.Logger;

import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.excpetion.AccettazioneDepositiExcpetion;
import it.netservice.csp.backend.excpetion.CspCommonRestWarningException;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.penale.model.common.dettaglioricorso.NotichePenale;
import it.netservice.penale.model.common.dettaglioricorso.ProvvedimentoNotePenale;
import it.netservice.penale.model.common.dettaglioricorso.ProvvedimentoPenale;
import it.netservice.penale.model.common.dettaglioricorso.StoricoProvvedimento;
import it.netservice.penale.model.enums.StatoInserimentoNote;
import it.netservice.penale.model.enums.StatoProvvedimento;
import it.netservice.penale.model.enums.TipoNotifiche;
import it.netservice.penale.model.sic.Deposito;

@ApplicationScoped
public class AccettazioneUtilService {
    private static final Logger LOGGER = Logger.getLogger(AccettazioneUtilService.class);

    @PersistenceUnit(unitName = "sic-model-penale")
    protected EntityManagerFactory emfCaricamento;

    /**
     * Metodo commune per aggiornare lo stato del provvedimento del desk
     *
     * @param repo
     *            repository del cspbackend
     * @param deposito
     *            deposito che si sta aggiornando lo stato
     * @param idUtente
     *            l'identificativo dell'utente che ha fatto l'operazione
     * @param entityManager
     *            enitity manager dello schema del sic
     * @param newState
     *            lo stato nuovo del provvedimento che va settato
     * @param oldState
     *            lo stato vecchio del provvedimento che va settato
     * @throws Throwable
     *             sollevata in casso di errore sul DB
     */
    public void aggiornaStatoDesk(SicPenaleRepository repo, Deposito deposito, Long idUtente, EntityManager entityManager,
            StatoProvvedimento newState, StatoProvvedimento oldState, String messaggio) throws Exception {
        LOGGER.info("Inizio aggiornamento stato per DESK. idCat:" + deposito.getIdCat());
        EntityTransaction transaction = entityManager.getTransaction();
        transaction.begin();
        try {
            ProvvedimentoPenale provvedimentoPenale = repo.findProvvedimentoByIdCat(deposito.getIdCat());

            // Se è stato indicato un messaggio, crea una nuova notifica e una nuova nota da inviare al Presidente
            if (messaggio != null && !messaggio.trim().isEmpty()) {
                SicPenaleRepository sicPenaleRepository = new SicPenaleRepository(entityManager);
                Long idUdienza = provvedimentoPenale.getIdUdienza();
                Long idUtentePresidente = sicPenaleRepository.getIdUtentePresidenteByIdUdien(idUdienza);
                String descrizioneNotifica = "Busta Accettata: " + messaggio;
                NotichePenale notifiche = buildNotifica(deposito.getNrg(), idUdienza, idUtentePresidente, descrizioneNotifica);
                entityManager.persist(notifiche);

                ProvvedimentoNotePenale notePenale = buildNota(provvedimentoPenale.getIdProvv(), messaggio, idUtente,
                        StatoInserimentoNote.MINUTA_ACCETTATA);
                entityManager.persist(notePenale);
            }

            /**
             * Cambio stato per desk penale in tabella PENALE_PROVVEDIMENTI
             */
            if (!StatoProvvedimento.ERRORE_DI_PUBBLICAZIONE.equals(newState)) {
                if (provvedimentoPenale == null) {
                    LOGGER.error("Impossibile rifiutare il deposito. Provvedimento non trovato. idCat:" + deposito.getIdCat());
                    throw new CspCommonRestWarningException("Impossibile rifiutare il deposito. Provvedimento non trovato.",
                            RestErrorCodeEnum.PROVV_NOT_FOUND);
                }

                provvedimentoPenale.setStatoProvvedimento(newState);
                provvedimentoPenale.setDataUltimaModifica(Calendar.getInstance().getTime());
                repo.merge(provvedimentoPenale);
            }

            /**
             * lo aggiungo al tracking dello stato per desk penale in tabella PENALE_PROVV_CHANGE_STATUS
             */
            StoricoProvvedimento storicoProvvedimento = buildStoricoProvvedimento(provvedimentoPenale.getIdProvv(), newState, oldState,
                    idUtente);
            entityManager.persist(storicoProvvedimento);

            transaction.commit();
            LOGGER.info("Aggiornamento stato per DESK completato");

        } catch (Exception ex) {
            if (transaction.isActive()) {
                transaction.rollback();
            }
            LOGGER.error("Errore durante l'aggiornamento dello stato per il DESK. idCat:" + deposito.getIdCat());
            throw new AccettazioneDepositiExcpetion("Errore durante l'aggiornamento dello stato per il DESK",
                    RestErrorCodeEnum.UPDATE_STATO_DESK_ERROR, ex);
        }

    }

    public StoricoProvvedimento buildStoricoProvvedimento(String idProvv, StatoProvvedimento newState, StatoProvvedimento oldState,
            Long idUtente) {
        StoricoProvvedimento storicoProvvedimento = new StoricoProvvedimento();
        storicoProvvedimento.setIdProvv(idProvv);
        storicoProvvedimento.setStato(newState);
        storicoProvvedimento.setPrevStato(oldState);
        storicoProvvedimento.setOggi(Calendar.getInstance().getTime());
        storicoProvvedimento.setIdUtente(idUtente);
        return storicoProvvedimento;
    }

    public NotichePenale buildNotifica(Long nrg, Long idUdienza, Long idUtente, String descrizione) {
        NotichePenale notifiche = new NotichePenale();
        notifiche.setNrg(nrg);
        notifiche.setTipoNotifiche(TipoNotifiche.PROVVEDIMENTO);
        notifiche.setRead(false);
        notifiche.setIdUdienza(idUdienza);
        notifiche.setIdUtenteToSend(idUtente);
        notifiche.setDescrizione(descrizione);
        return notifiche;
    }

    public ProvvedimentoNotePenale buildNota(String idProvv, String motivazione, Long idUtente, StatoInserimentoNote statoInserimento) {
        ProvvedimentoNotePenale note = new ProvvedimentoNotePenale();
        note.setIdProvv(idProvv);
        note.setDataInserimento(Calendar.getInstance().getTime());
        note.setNote(motivazione);
        note.setIdUtente(idUtente);
        note.setStatoInserimento(statoInserimento);
        return note;
    }

}
