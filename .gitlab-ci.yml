image: $DOCKER_REGISTRY/cicd/gradle-image:jdk8-gradle8

stages:
  - publishArchives
  - publishRelease
  - trigImagePusher
  - analysis
  
before_script:
  - "echo CI coordinates: $CI_BUILD_NAME, $CI_BUILD_REF_NAME $CI_BUILD_STAGE"
  - "echo nexus auth: $CI_NEXUS_USERNAME"
  - "export GRADLE_USER_HOME=`pwd`/.gradle"
  - "echo gradle user home: $GRADLE_USER_HOME"

cache:
  paths:
    - .gradle

publishArchives:
  stage: publishArchives
  script:
    - gradle -PnexusUsername=$CI_NEXUS_USERNAME -PnexusPassword=$CI_NEXUS_PASSWORD -PnsPluginVersion=$NS_PLUGIN_VERSION -PpatchPluginVersion=$PATCH_PLUGIN_VERSION --refresh-dependencies --parallel --no-daemon build -PenableSpotless publish
    
publishRelease:
  stage: publishRelease
  script:
    - gradle -PnexusUsername=$CI_NEXUS_USERNAME -PnexusPassword=$CI_NEXUS_PASSWORD -PnsPluginVersion=$NS_PLUGIN_VERSION -PpatchPluginVersion=$PATCH_PLUGIN_VERSION --refresh-dependencies --parallel --no-daemon build publishPatch --stacktrace
    - bash /scripts/copyPortale.sh -p "Servizi Csp" -h $NETSUP_HOST -u $NETSUP_USER -s $SSH_SERVER_PRIVATE_KEY
    - cat /tmp/.nexusPatchPath
    - curl -i -X POST -d 'payload={"text":"@all Patch Servizi CSP in rilascio '"$CI_COMMIT_REF_NAME"' disponibile su Draft --> https://portalegiustiziacivile.netserv.it/web/guest/login  ", "username":"GitLab"}' $MATTERMOST_HOOK_CASSPENALE_HD
  only:
    variables:
      - $CI_COMMIT_TAG =~ /((([0-9]*)(\.))(([0-9]{2})(\.)){2}([0-9]){3}((-RC)([0-9]+))?)|(\w*(\.)([0-9]){3}((-RC)([0-9]+)))/

trigImagePusher:
  stage: trigImagePusher
  script:
    - sh /scripts/trigPusher.sh 678 $TRIGGER_TOKEN $CI_COMMIT_REF_NAME

sonarManual:
  stage: analysis
  when: manual
  script:
    - gradle -PnexusUsername=$CI_NEXUS_USERNAME -PnexusPassword=$CI_NEXUS_PASSWORD -PnsPluginVersion=$NS_PLUGIN_VERSION -PpatchPluginVersion=$PATCH_PLUGIN_VERSION --refresh-dependencies --parallel --no-daemon -Dsonar.host.url=$CI_SONAR_URL -Dsonar.login=$CI_SONAR_TOKEN -Dsonar.verbose=true sonarqube --stacktrace
  except:
    - tags
    
sonar:
  stage: analysis
  script:
    - gradle -PnexusUsername=$CI_NEXUS_USERNAME -PnexusPassword=$CI_NEXUS_PASSWORD -PnsPluginVersion=$NS_PLUGIN_VERSION -PpatchPluginVersion=$PATCH_PLUGIN_VERSION --refresh-dependencies --parallel --no-daemon -Dsonar.host.url=$CI_SONAR_URL -Dsonar.login=$CI_SONAR_TOKEN -Dsonar.verbose=true sonarqube
  only:
    variables:
      - $CI_COMMIT_TAG =~ /(([0-9]*)(\.))(([0-9]{2})(\.)){2}([0-9]){3}((-RC)([0-9]+))?/
    
