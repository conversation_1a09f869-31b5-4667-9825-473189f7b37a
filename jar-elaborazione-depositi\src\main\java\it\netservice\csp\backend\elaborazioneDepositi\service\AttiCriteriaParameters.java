/**
 *
 */
package it.netservice.csp.backend.elaborazioneDepositi.service;

import java.util.Set;

import it.netservice.common.rest.backend.CriteriaParametersBackend;
import it.netservice.penale.model.csp.Atto;

/**
 * Criteri di ricerca per gli atti
 *
 * <AUTHOR>
 */
public class AttiCriteriaParameters extends CriteriaParametersBackend<Atto> {

    @SearchParameter(operator = Operator.EQUAL)
    private String nrg;
    @SearchParameter(operator = Operator.EQUAL)
    private String idCat;
    @SearchParameter(property = "tipo", operator = Operator.NOTIN)
    private Set<String> tipiToExclude;
    @SearchParameter(operator = Operator.EQUAL)
    private Boolean cancellato;
    @SearchParameter(operator = Operator.EQUAL)
    private String idContenitore;

    public AttiCriteriaParameters() {
        cancellato = false;
    }

    public String getNrg() {
        return nrg;
    }

    public void setNrg(String nrg) {
        this.nrg = nrg;
    }

    public String getIdCat() {
        return idCat;
    }

    public void setIdCat(String idCat) {
        this.idCat = idCat;
    }

    public Set<String> getTipiToExclude() {
        return tipiToExclude;
    }

    public void setTipiToExclude(Set<String> tipiToExclude) {
        this.tipiToExclude = tipiToExclude;
    }

    public Boolean getCancellato() {
        return cancellato;
    }

    public void setCancellato(Boolean cancellato) {
        this.cancellato = cancellato;
    }

    public String getIdContenitore() {
        return idContenitore;
    }

    public void setIdContenitore(String idContenitore) {
        this.idContenitore = idContenitore;
    }
}
