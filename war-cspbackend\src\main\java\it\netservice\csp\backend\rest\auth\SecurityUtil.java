package it.netservice.csp.backend.rest.auth;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;

import it.netservice.csp.backend.dto.ErrorTypeEnum;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.excpetion.AuthenticationExcpetion;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.PubblicazioneException;
import it.netservice.csp.backend.excpetion.SignaturedException;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.csp.backend.sic.service.repository.SicRepositoryException;
import it.netservice.penale.model.sic.ElaborazioneDeposito;
import it.netservice.penale.model.sic.Utente;

/**
 * z
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class SecurityUtil {

    @Inject
    private TokenManager tokenManager;

    private static final Logger LOGGER = Logger.getLogger(SecurityUtil.class);

    // public static String decodeJWTIAM(String token) {
    // try {
    // JWTVerifier verifier = JWT.require(algorithmHS).withIssuer(ISSUER).build();
    // verifier.verify(token);
    // } catch (JWTVerificationException jwte) {
    // LOGGER.warn("Token non valido", jwte);
    //
    // }
    // return token;
    // }

    /**
     * Verifica che l'utente può pubblicare e firmare il provvedimento enL'utente di firma non corrisponde all'utente loggato)
     *
     * @param jwt
     *            --> token di autenticazione dell'utente
     * @param otpJWTIAM
     *            --> token di autenticazione del provider di firma
     * @throws CspBackendException
     *             --> eccezione sollevata nel caso in cui l'utente non ha i permessi
     */
    public static void checkUtenzaPubblicaProvvedimento(DecodedJWT jwt, String otpJWTIAM, SicPenaleRepository repo)
            throws CspBackendException, SicRepositoryException {
        // if (!isAdmin(jwt) || !isDepositoSentenza(jwt)) {
        DecodedJWT decodedJWTIAM = JWT.decode(otpJWTIAM);
        String fiscalCodeIAM = decodedJWTIAM.getClaim("fiscalNumber").asString();
        List<Utente> utenteByCodiceFiscale = repo.findUtenteByCodiceFiscale(fiscalCodeIAM);

        if (utenteByCodiceFiscale == null && !utenteByCodiceFiscale.isEmpty()) {
            throw new CspBackendException("Utente non presente in anagrafica", RestErrorCodeEnum.USER_NOT_FOUND, 433,
                    ErrorTypeEnum.WARNING);
        }
        String codiceFiscaleJWT = getCodiceFiscale(jwt);
        if (codiceFiscaleJWT != null && !codiceFiscaleJWT.equals(fiscalCodeIAM)) {
            throw new SignaturedException("Firma non consentita poiché l'utente loggato è diverso dall'utente abilitato alla firma",
                    RestErrorCodeEnum.UNAUTHORIZED_SIGN, ErrorTypeEnum.WARNING);
        }
        if (!isDepositoSentenza(jwt) && !isNumRaccGenerale(jwt)) {
            throw new PubblicazioneException(
                    "Pubblicazione non consentita, l'utente non è abilitato alle funzioni Deposito della Sentenza e Numero di Raccolta Generale",
                    RestErrorCodeEnum.UNAUTHORIZED_PUBLISH, ErrorTypeEnum.WARNING);
        }
    }

    public static void checkUtenzaPagamenti(HttpServletRequest context) throws CspBackendException {
        if ((boolean) context.getSession().getAttribute("admin") || (boolean) context.getSession().getAttribute("gestionePagamenti")) {
        } else {
            throw new CspBackendException("Servizio non disponibile per l'utente autenticato");
        }
    }

    public void checkSessionToValuedInToken(HttpServletRequest context, EntityManager entityManager, Long idDeposito)
            throws CspBackendException {
        /*
         * CriteriaBuilderHelper<DepositoView> criteriaBuilder = new CriteriaBuilderHelper<>(DepositoView.class);
         * DepositiViewCriteriaParameters param = new DepositiViewCriteriaParameters(); param.setIdCat(idDeposito); DepositoView deposito =
         * criteriaBuilder.uniqueResult(entityManager, param);
         */
        checkSessionToValuedInToken(context);
    }

    public void checkSessionToValuedInToken(HttpServletRequest context) throws CspBackendException {
        DecodedJWT jwt = tokenManager.verifyToken(context.getHeader("x-auth-token"));
        // Arrays.asList(jwt.getClaim("idSezioni").asArray(Long.class))
        List<Long> sezioni = jwt.getClaim("idSezioni") != null ? jwt.getClaim("idSezioni").asList(Long.class) : new ArrayList<Long>();
        if (sezioni == null && !sezioni.isEmpty()) {
            throw new CspBackendException("Sezioni nulle in sessione. Ricollegarsi al csp-client", RestErrorCodeEnum.SEZIONI_NULL, 433);
        }
        /*
         * Integer idSezioneInteger = deposito.getIdSezioneRicorso(); if (idSezioneInteger == null) { return; } if (sezioni.contains(new
         * Long(idSezioneInteger))) { return; }
         */
        return;
        // throw new CspBackendException("Servizio non disponibile per l'utente autenticato");
    }

    public static List<Long> getSezioniByToken(DecodedJWT jwt) throws CspBackendException {
        // Arrays.asList(jwt.getClaim("idSezioni").asArray(Long.class))
        List<Long> sezioni = jwt.getClaim("idSezioni") != null ? jwt.getClaim("idSezioni").asList(Long.class) : new ArrayList<Long>();
        return sezioni;
    }

    public static Boolean isIdSezioneRG(DecodedJWT jwt) throws CspBackendException {
        Long sezione = jwt.getClaim("idSezioneRG") != null ? jwt.getClaim("idSezioneRG").asLong() : null;
        return sezione != null;
    }

    public static Long getIdSezione(DecodedJWT jwt, String idSezione) throws CspBackendException {
        Long idSezioneRes = jwt.getClaim(idSezione) != null ? jwt.getClaim(idSezione).asLong() : null;
        return idSezioneRes;
    }

    public static Boolean isAdmin(DecodedJWT jwt) throws CspBackendException {
        Boolean admin = jwt.getClaim("admin") != null ? jwt.getClaim("admin").asBoolean() : null;
        return admin;
    }

    public static Boolean isDepositoSentenza(DecodedJWT jwt) throws CspBackendException {
        Boolean depositoSentenza = jwt.getClaim("depositoSentenza") != null ? jwt.getClaim("depositoSentenza").asBoolean() : null;
        return depositoSentenza;
    }

    public static Boolean isNumRaccGenerale(DecodedJWT jwt) throws CspBackendException {
        Boolean numRaccGenerale = jwt.getClaim("numRaccGenerale") != null ? jwt.getClaim("numRaccGenerale").asBoolean() : null;
        return numRaccGenerale;
    }

    public static void checkUtenzaReadOnly(DecodedJWT jwt) throws CspBackendException {
        Boolean readOnly = jwt.getClaim("readOnly") != null ? jwt.getClaim("readOnly").asBoolean() : false;
        if (readOnly) {
            LOGGER.error("Servizio non disponibile per l'utente autenticato. idUtente:" + jwt.getClaim("idUtente"));
            throw new AuthenticationExcpetion("Servizio non disponibile per l'utente autenticato", RestErrorCodeEnum.USER_READ_ONLY);
        }
    }

    public void logComandoRicorso(HttpServletRequest context, String comando, String nrg) {
        LOGGER.info("USRCMD " + context.getSession().getAttribute("idOperatore") + " comando " + comando + " ricorso " + nrg);
    }

    public String getOperatore(DecodedJWT jwt) {
        return jwt.getSubject() != null && !jwt.getSubject().isEmpty() ? jwt.getSubject() : null;
    }

    public static Long getIdUtente(DecodedJWT jwt) throws CspBackendException {
        return jwt.getClaim("idUtente") != null ? jwt.getClaim("idUtente").asLong() : null;
    }

    public static String getCodiceFiscale(DecodedJWT jwt) throws CspBackendException {
        return jwt.getClaim("codiceFiscale") != null ? jwt.getClaim("codiceFiscale").asString() : null;
    }

    public void logComandoDeposito(String operatore, String comando, Long idDeposito) {
        LOGGER.info("USRCMD " + operatore + " comando " + comando + " deposito " + idDeposito);
    }

    public void logComandoElaborazione(HttpServletRequest context, String comando, String idElaborazione) {
        LOGGER.info(
                "USRCMD " + context.getSession().getAttribute("idOperatore") + " comando " + comando + " elaborazione " + idElaborazione);
    }

    // public void checkUtenzaRicorsoView(HttpServletRequest context, EntityManager entityManager, String nrg) throws CspBackendException {
    // if ((boolean) context.getSession().getAttribute("admin")) {
    // return;
    // }
    // CriteriaBuilderHelper<RicorsoView> criteriaBuilder = new CriteriaBuilderHelper<>(RicorsoView.class);
    // RicorsiViewCriteriaParameters param = new RicorsiViewCriteriaParameters();
    // param.setNrg(nrg);
    // RicorsoView ricorso = criteriaBuilder.uniqueResult(entityManager, param);
    // checkUtenzaRicorsoView(context, ricorso);
    // }
    //
    // public void checkUtenzaRicorsoView(HttpServletRequest context, RicorsoView ricorso) throws CspBackendException {
    // // gli utenti spg possono fare tutto su tutto
    // boolean isSpg = context.getUserPrincipal() != null && "spgclient".equals(context.getUserPrincipal().getName());
    // if (isSpg) {
    // return;
    // }
    // List sezioni = ((List) context.getSession().getAttribute("idSezioni"));
    // if (!sezioni.isEmpty() && (!sezioni.contains(Long.valueOf(ricorso.getIdSezione())) && ricorso.getIdSezioneInc() != null
    // && !sezioni.contains(Long.valueOf(ricorso.getIdSezioneInc())))) {
    // throw new CspBackendException("Servizio non disponibile per l'utente autenticato");
    // }
    // }
    //
    // public void checkUtenzaDepositoView(HttpServletRequest context, EntityManager entityManager, Long idDeposito)
    // throws CspBackendException {
    // CriteriaBuilderHelper<DepositoView> criteriaBuilder = new CriteriaBuilderHelper<>(DepositoView.class);
    // DepositiViewCriteriaParameters param = new DepositiViewCriteriaParameters();
    // param.setId(idDeposito);
    // DepositoView deposito = criteriaBuilder.uniqueResult(entityManager, param);
    // checkUtenzaDepositoView(context, deposito);
    // }
    //
    // public void checkUtenzaDepositoView(HttpServletRequest context, DepositoView deposito) throws CspBackendException {
    // if ((boolean) context.getSession().getAttribute("admin")) {
    // return;
    // }
    // // gli utenti spg possono fare tutto su tutto
    // boolean isSpg = context.getUserPrincipal() != null && "spgclient".equals(context.getUserPrincipal().getName());
    // if (isSpg) {
    // return;
    // }
    // List<Long> sezioni = (List<Long>) context.getSession().getAttribute("idSezioni");
    // if (sezioni == null) {
    // throw new CspBackendException("Sezioni nulle in sessione. Ricollegarsi al csc-client");
    // }
    // Integer idSezioneInteger = deposito.getIdSezioneRicorso();
    // if (idSezioneInteger == null) {
    // return;
    // }
    // if (sezioni.contains(new Long(idSezioneInteger))) {
    // return;
    // }
    // Integer idSezioneIncombenteInteger = deposito.getIdSezioneIncombente();
    // if (idSezioneIncombenteInteger != null && sezioni.contains(new Long(idSezioneIncombenteInteger))) {
    // return;
    // }
    // throw new CspBackendException("Servizio non disponibile per l'utente autenticato");
    // }
    //
    public void checkUtenzaElaborazioneDeposito(HttpServletRequest context, EntityManager entityManager, String id)
            throws CspBackendException {
        ElaborazioneDeposito elabDep = entityManager.find(ElaborazioneDeposito.class, id);
        checkUtenzaElaborazioneDeposito(context, entityManager, elabDep);
    }

    public void checkUtenzaElaborazioneDeposito(HttpServletRequest context, EntityManager entityManager, ElaborazioneDeposito elabDep)
            throws CspBackendException {
        checkSessionToValuedInToken(context, entityManager, elabDep.getDeposito().getIdCat());
    }

    public void initSession(HttpServletRequest httpRequest, DecodedJWT jwt) throws AuthenticationExcpetion {
        LOGGER.info("Inizializzazione sessione per utente:" + jwt.getClaim("idUtente"));
        try {
            // sezione scelta in fase di login
            httpRequest.getSession().setAttribute("idSezione", jwt.getClaim("idSezione").asLong());
            httpRequest.getSession().setAttribute("siglaSezione", jwt.getClaim("siglaSezione").asString());

            // lista di idSezioni per cui l'utente ha visibilità
            httpRequest.getSession().setAttribute("idSezioni", Arrays.asList(jwt.getClaim("idSezioni").asArray(Long.class)));

            // idSezioneRG valorizzato solo se siglaSezione é RG
            httpRequest.getSession().setAttribute("idSezioneRG", jwt.getClaim("idSezioneRG").asLong());

            httpRequest.getSession().setAttribute("nome", jwt.getClaim("nome").asString());
            httpRequest.getSession().setAttribute("cognome", jwt.getClaim("cognome").asString());
            httpRequest.getSession().setAttribute("idOperatore", jwt.getSubject());
            httpRequest.getSession().setAttribute("idProfilo", jwt.getClaim("idProfilo").asString());
            httpRequest.getSession().setAttribute("idUtente", jwt.getClaim("idUtente").asLong());
            httpRequest.getSession().setAttribute("admin", jwt.getClaim("admin").asBoolean());
            httpRequest.getSession().setAttribute("sentenzaMinuta", jwt.getClaim("sentenzaMinuta").asBoolean());
            httpRequest.getSession().setAttribute("depositoSentenza", jwt.getClaim("depositoSentenza").asBoolean());
            httpRequest.getSession().setAttribute("gestionePagamenti", jwt.getClaim("gestionePagamenti").asBoolean());
            httpRequest.getSession().setAttribute("ruoloUdienza", jwt.getClaim("ruoloUdienza").asBoolean());
            httpRequest.getSession().setAttribute("idSezioneSesta", jwt.getClaim("idSezioneSesta").asLong());
            httpRequest.getSession().setAttribute("readOnly", jwt.getClaim("readOnly").asBoolean());

        } catch (Exception ex) {
            LOGGER.error("Errore in inizializzazione sessione", ex);
            throw new AuthenticationExcpetion("Errore in inizializzazione sessione", RestErrorCodeEnum.INIT_SESSION_ERROR, ex);
        }
    }

}
