package it.netservice.csp.backend.elaborazioneDepositi.test;

/**
 *
 * <AUTHOR>
 */
public class Parser2Test {

    // @Test
    // public void testRicorso() throws Exception {
    // ElaborazioneDeposito result = parse("ricprova.xml");
    // assertNotNull(result);
    //// assertEquals(result.getSpeseGiustizia().size(), 4);
    //// for (SpeseGiustiziaElaborazioneDeposito spesa : result.getSpeseGiustizia()) {
    //// if ("ContributoUnificato".equals(spesa.getTipoSpesa())) {
    //// assertEquals(spesa.getImporto().toString(), "127.00");
    //// assertEquals(spesa.getEstremiPagamento().size(), 4); // ?
    //// }
    //// }
    // }
    //
    // @Test
    // public void testAttoGenerico() throws Exception {
    // ElaborazioneDeposito result = parse("attogenerico1.xml");
    // assertNotNull(result);
    // }
    //
    // @Test
    // public void testIntegrazSpese() throws Exception {
    // ElaborazioneDeposito result = parse("integrazionespese.xml");
    // assertNotNull(result);
    // }
    //
    // @Test
    // public void testIntegrazAnagContraddittorio() throws Exception {
    // ElaborazioneDeposito result = parse("intanagContraddittorio.xml");
    // assertNotNull(result);
    // }
    //
    // @Test
    // public void testIntegrazAnagDomicilio() throws Exception {
    // ElaborazioneDeposito result = parse("intanagDomicilio.xml");
    // assertNotNull(result);
    // }
    //
    // @Test
    // public void testRequisitoriaPG() throws Exception {
    // ElaborazioneDeposito result = parse("requisitoriaPG.xml");
    // assertNotNull(result);
    // }
    //
    // @Test
    // public void testIntegrazSostituzione() throws Exception {
    // ElaborazioneDeposito result = parse("intanagSostRevoca.xml");
    // assertNotNull(result);
    // }
    //
    // private ElaborazioneDeposito parse(String inputFile) throws Exception {
    // byte[] content;
    // try (InputStream xmlIs = getClass().getClassLoader().getResourceAsStream(inputFile)) {
    // content = IOUtils.toByteArray(xmlIs);
    // }
    // ElaborazioneDeposito elaborazioneDeposito;
    // String rootElementName;
    // try {
    // try (InputStream contentStream = new ByteArrayInputStream(content)) {
    // rootElementName = DocTypeMapper.getRootElement(contentStream);
    // }
    // try (InputStream contentStream = new ByteArrayInputStream(content)) {
    // elaborazioneDeposito = ElaborazioneDepositoParser.getDatiAttoAsObject(contentStream, rootElementName, null);
    // elaborazioneDeposito.setDeposito(null);
    // }
    // return elaborazioneDeposito;
    // } catch (Exception e) {
    // e.printStackTrace();
    // throw e;
    // }
    // }

}
