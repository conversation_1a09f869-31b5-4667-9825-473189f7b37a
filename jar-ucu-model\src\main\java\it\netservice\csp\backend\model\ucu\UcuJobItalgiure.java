package it.netservice.csp.backend.model.ucu;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.*;

/**
 * The persistent class for the UCU_JOB_ITALGIURE database table.
 *
 */
@Entity
@Table(name = "UCU_JOB_ITALGIURE")
@NamedQuery(name = "UcuJobItalgiure.findAll", query = "SELECT u FROM UcuJobItalgiure u")
public class UcuJobItalgiure implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @SequenceGenerator(name = "UCU_JOB_ITALGIURE_IDJOBITALGIURE_GENERATOR", sequenceName = "SEQ_UCU_JOB_ITALGIURE", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "UCU_JOB_ITALGIURE_IDJOBITALGIURE_GENERATOR")
    @Column(name = "ID_JOB_ITALGIURE")
    private long idJobItalgiure;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "DT_INSERIMENTO")
    private Date dtInserimento;

    @Column(name = "FLAG_INVIATO")
    private String flagInviato;

    @Column(name = "ID_STORIA_DOCUMENTO")
    private BigDecimal storiaDocumento;

    public UcuJobItalgiure() {
    }

    public long getIdJobItalgiure() {
        return this.idJobItalgiure;
    }

    public void setIdJobItalgiure(long idJobItalgiure) {
        this.idJobItalgiure = idJobItalgiure;
    }

    public Date getDtInserimento() {
        return this.dtInserimento;
    }

    public void setDtInserimento(Date dtInserimento) {
        this.dtInserimento = dtInserimento;
    }

    public String getFlagInviato() {
        return this.flagInviato;
    }

    public void setFlagInviato(String flagInviato) {
        this.flagInviato = flagInviato;
    }

    public BigDecimal getStoriaDocumento() {
        return this.storiaDocumento;
    }

    public void setStoriaDocumento(BigDecimal storiaDocumento) {
        this.storiaDocumento = storiaDocumento;
    }

}
