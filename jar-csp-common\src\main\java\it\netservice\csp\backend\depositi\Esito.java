package it.netservice.csp.backend.depositi;

/**
 *
 * <AUTHOR>
 */
public class Esito implements Comparable<Esito> {

    private String tipoContent;
    private String livello;
    private Long data;
    private String codice;
    private String descrizione;

    public Esito() {
    }

    public String getTipoContent() {
        return tipoContent;
    }

    public void setTipoContent(String tipoContent) {
        this.tipoContent = tipoContent;
    }

    public String getLivello() {
        return livello;
    }

    public void setLivello(String livello) {
        this.livello = livello;
    }

    public Long getData() {
        return data;
    }

    public void setData(Long data) {
        this.data = data;
    }

    public String getCodice() {
        return codice;
    }

    public void setCodice(String codice) {
        this.codice = codice;
    }

    public String getDescrizione() {
        return descrizione;
    }

    public void setDescrizione(String descrizione) {
        this.descrizione = descrizione;
    }

    @Override
    public int compareTo(Esito o) {
        return data.compareTo(o.getData());
    }

}
