package it.netservice.csp.backend.sic.service.repository;

import java.math.BigDecimal;
import java.util.*;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;
import javax.persistence.TypedQuery;

import org.apache.log4j.Logger;

import it.netservice.common.rest.IModel;
import it.netservice.csp.backend.common.Utils;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.dto.RicercaPartiParams;
import it.netservice.csp.backend.dto.RicercaRicorsiKeysDto;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.GestioneSicException;
import it.netservice.csp.backend.sic.service.depositi.DateUtils;
import it.netservice.penale.model.common.UtentePenale;
import it.netservice.penale.model.common.dettaglioricorso.*;
import it.netservice.penale.model.csp.RicercaRicorsiBaseFuoriUdienza;
import it.netservice.penale.model.csp.RicercaRicorsiBaseInUdienza;
import it.netservice.penale.model.enums.StatoProvvedimento;
import it.netservice.penale.model.sic.*;
import it.netservice.penale.model.sic.csp.*;

/**
 * <AUTHOR>
 */
public class SicPenaleRepository {

    private static final Logger LOGGER = Logger.getLogger(SicPenaleRepository.class);
    private final EntityManager em;

    public SicPenaleRepository(EntityManager entityManager) {
        this.em = entityManager;
    }

    public EntityManager getEntityManager() {
        return em;
    }

    public <T extends IModel> void persistOrMerge(T entity) {
        LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] persistOrMerge - Inizio persistOrMerge per entita': %s",
                entity.getClass().getName()));
        if (entity instanceof it.netservice.penale.model.common.dettaglioricorso.ProvvedimentoPenale) {
            it.netservice.penale.model.common.dettaglioricorso.ProvvedimentoPenale pp = (it.netservice.penale.model.common.dettaglioricorso.ProvvedimentoPenale) entity;
            LOGGER.info(String.format(
                    "[LOGGING NRG/ID_UDIEN] persistOrMerge - Entity IS ProvvedimentoPenale. ID_PROVV: %s, FK_IDCAT: %d, ID_UDIEN: %d, NRG: %s",
                    pp.getIdProvv(), pp.getIdCat(), pp.getIdUdienza(), pp.getNrg()));
        }
        Object id = em.getEntityManagerFactory().getPersistenceUnitUtil().getIdentifier(entity);
        if (id != null) {
            em.merge(entity);
            LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] persistOrMerge - Eseguito merge per entita': %s, ID: %s",
                    entity.getClass().getName(), id));
        } else {
            em.persist(entity);
            LOGGER.info(
                    String.format("[LOGGING NRG/ID_UDIEN] persistOrMerge - Eseguito persist per entita': %s",
                            entity.getClass().getName()));
        }
        LOGGER.info(
                String.format("[LOGGING NRG/ID_UDIEN] persistOrMerge - Fine persistOrMerge per entita': %s",
                        entity.getClass().getName()));
    }

    public <T extends IModel> void merge(T entity) {
        LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] merge - Inizio merge per entita': %s",
                entity.getClass().getName()));
        if (entity instanceof it.netservice.penale.model.common.dettaglioricorso.ProvvedimentoPenale) {
            it.netservice.penale.model.common.dettaglioricorso.ProvvedimentoPenale pp = (it.netservice.penale.model.common.dettaglioricorso.ProvvedimentoPenale) entity;
            // Assuming getIdCat(), getIdUdienza(), getNrg() are the correct getters. Adjust
            // if necessary.
            LOGGER.info(String.format(
                    "[LOGGING NRG/ID_UDIEN] merge - Entity IS ProvvedimentoPenale. ID_PROVV: %s, FK_IDCAT: %d, ID_UDIEN: %d, NRG: %s",
                    pp.getIdProvv(), pp.getIdCat(), pp.getIdUdienza(), pp.getNrg()));
        }
        em.merge(entity);
        Object id = em.getEntityManagerFactory().getPersistenceUnitUtil().getIdentifier(entity);
        LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] merge - Fine merge per entita': %s, ID: %s",
                entity.getClass().getName(), id));
    }

    // TODO Query che da problemi per gli esiti parziali
    public RicorsoSic getRicorsoByElaborazioneDeposito(ElaborazioneDeposito elabDep) {
        LOGGER.info(String.format(
                "[LOGGING NRG/ID_UDIEN] getRicorsoByElaborazioneDeposito - START - ElabDep.ID: %s, ElabDep.NumeroRicorso: %s, ElabDep.AnnoRicorso: %s",
                elabDep.getId(), elabDep.getNumeroRicorso(), elabDep.getAnnoRicorso()));
        RicorsoSic ricorso = getRicorsoByNumeroAnno(elabDep.getNumeroRicorso(), elabDep.getAnnoRicorso());
        if (ricorso != null) {
            LOGGER.info(String.format(
                    "[LOGGING NRG/ID_UDIEN] getRicorsoByElaborazioneDeposito - RicorsoSic trovato. NRG: %s. Numero UdienzeRicorso: %d",
                    ricorso.getNrg(), ricorso.getUdienzaRicorso() != null ? ricorso.getUdienzaRicorso().size() : 0));
            if (ricorso.getUdienzaRicorso() != null) {
                for (UdienzaRicorso ur : ricorso.getUdienzaRicorso()) {
                    LOGGER.info(String.format(
                            "[LOGGING NRG/ID_UDIEN] getRicorsoByElaborazioneDeposito - UdienzaRicorso details: idUdieRicorso: %d, lastUdie: %b, Udienza ID: %d",
                            ur.getIdUdieRicorso(), ur.isLastUdie(),
                            ur.getUdienza() != null ? ur.getUdienza().getId() : null));
                }
            }
        } else {
            LOGGER.warn(String.format(
                    "[LOGGING NRG/ID_UDIEN] getRicorsoByElaborazioneDeposito - RicorsoSic NON trovato per ElabDep.NumeroRicorso: %s, ElabDep.AnnoRicorso: %s",
                    elabDep.getNumeroRicorso(), elabDep.getAnnoRicorso()));
        }
        return ricorso;
    }

    public PenaleUdienzaRicorso getRicordoUdienzaByIdRicUdienza(Long idRicUdienza) {
        if (idRicUdienza != null && idRicUdienza > 0) {
            PenaleUdienzaRicorso udienzaRicorso = findEntityByQuery(
                    "FROM PenaleUdienzaRicorso where idRicorsoUdienza = :idRicUdienza",
                    PenaleUdienzaRicorso.class, getCompiledMap("idRicUdienza", idRicUdienza));
            if (udienzaRicorso != null) {
                return udienzaRicorso;
            }

        }
        return null;
    }

    public List<PenaleUdienzaRicorso> getRicordoUdienzaRiunitiFigli(Long nrgPadre) {
        if (nrgPadre != null && nrgPadre > 0) {
            List<PenaleUdienzaRicorso> udienzaRicorso = findEntitiesByQuery(
                    "FROM PenaleUdienzaRicorso where principale = :nrgPadre",
                    PenaleUdienzaRicorso.class, getCompiledMap("nrgPadre", nrgPadre));
            if (udienzaRicorso != null) {
                return udienzaRicorso;
            }

        }
        return null;
    }

    public List<PenaleUdienzaRicorso> getRicordoUdienzaRiunitiFigliByIdSent(Long idSentPadre) {
        if (idSentPadre != null && idSentPadre > 0) {
            List<PenaleUdienzaRicorso> udienzaRicorso = findEntitiesByQuery(
                    "FROM PenaleUdienzaRicorso  where vSentenza.id = :idSentPadre and vSentenza.riunito = true ",
                    PenaleUdienzaRicorso.class, getCompiledMap("idSentPadre", idSentPadre));
            if (udienzaRicorso != null) {
                return udienzaRicorso;
            }

        }
        return null;
    }

    public RicorsoSic getRicorsoByNumeroAnno(Long numero, String anno) {
        LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] getRicorsoByNumeroAnno - START - Numero: %d, Anno: %s",
                numero, anno));
        String nrgReale = SicPenaleEntity.formatNrgReale(String.valueOf(numero), anno, null);
        RicorsoSic ricorso = getRicorsoByNrgReale(Long.valueOf(nrgReale));
        // Logging for the returned RicorsoSic will be in getRicorsoByNrgReale or the
        // calling method getRicorsoByElaborazioneDeposito
        LOGGER.info(String.format(
                "[LOGGING NRG/ID_UDIEN] getRicorsoByNumeroAnno - END - Numero: %d, Anno: %s, NRGReale calcolato: %s",
                numero, anno, nrgReale));
        return ricorso;
    }

    public RicorsoSic getRicorsoByNrgReale(Long nrgReale) {
        LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] getRicorsoByNrgReale - START - NRGReale: %d", nrgReale));
        RicorsoSic ricorsoSic = findEntityByQuery("FROM RicorsoSic where nrgreale = :nrgReale", RicorsoSic.class,
                getCompiledMap("nrgReale", nrgReale));
        if (ricorsoSic != null) {
            LOGGER.info(
                    String.format(
                            "[LOGGING NRG/ID_UDIEN] getRicorsoByNrgReale - RicorsoSic trovato per NRGReale: %d. RicorsoSic NRG: %s",
                            nrgReale, ricorsoSic.getNrg()));
        } else {
            LOGGER.warn(String.format(
                    "[LOGGING NRG/ID_UDIEN] getRicorsoByNrgReale - RicorsoSic NON trovato per NRGReale: %d", nrgReale));
        }
        return ricorsoSic;
    }

    public List<Deposito> getDepositiByNrgAndStatoAccettato(Long nrg) {
        return findEntitiesByQuery(
                "FROM Deposito where nrg = :nrg AND STATO IN ('NEW', 'ACCETTATO') ORDER BY DATADEPOSITO DESC",
                Deposito.class, getCompiledMap("nrg", nrg));
    }

    public Sentenza getSetenzaByIdCatAndNrg(Long idCat, Long nrg) throws CspBackendException {
        LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] getSetenzaByIdCatAndNrg - START - IDCAT: %d, NRG: %s", idCat,
                nrg));
        ProvvedimentoPenale provvedimentoPenale = findProvvedimentoByIdCat(idCat);

        if (provvedimentoPenale != null && provvedimentoPenale.getIdUdienza() > 0 && nrg != null && nrg > 0) {
            LOGGER.info(String.format(
                    "[LOGGING NRG/ID_UDIEN] getSetenzaByIdCatAndNrg - ProvvedimentoPenale trovato. ID_UDIEN da ProvvedimentoPenale: %d. Procedo a cercare UdienzaRicorso.",
                    provvedimentoPenale.getIdUdienza()));
            UdienzaRicorso udienzaRicorso = findEntityByQuery(
                    "FROM dettaglioricorso.UdienzaRicorso  where udienza.id = :idUdien and ricorsoSic.nrg= :nrg and lastUdie = 1",
                    UdienzaRicorso.class, getCompiledMap("idUdien", provvedimentoPenale.getIdUdienza(), "nrg", nrg));
            if (udienzaRicorso != null) {
                LOGGER.info(
                        String.format(
                                "[LOGGING NRG/ID_UDIEN] getSetenzaByIdCatAndNrg - UdienzaRicorso trovato. ID_RICUDIEN: %d. IDCAT: %d",
                                udienzaRicorso.getIdUdieRicorso(), idCat));
                Set<Esito> esiti = udienzaRicorso.getEsiti();
                if (esiti != null && !esiti.isEmpty()) {
                    Iterator<Esito> iterator = esiti.iterator();
                    do {

                        Esito esito = iterator.next();
                        if (esito.getEsitosent() != null && !esito.getEsitosent().isEmpty()) {
                            Sentenza sentenza = esito.getEsitosent().iterator().next().getSentenza();
                            LOGGER.info(String.format(
                                    "[LOGGING NRG/ID_UDIEN] getSetenzaByIdCatAndNrg - Sentenza trovata. ID_SENT: %d. IDCAT: %d",
                                    sentenza != null ? sentenza.getId() : null, idCat));
                            return sentenza;
                        }
                    } while (iterator.hasNext());
                    LOGGER.warn(String.format(
                            "[LOGGING NRG/ID_UDIEN] getSetenzaByIdCatAndNrg - UdienzaRicorso trovato, ma nessun Esito con Sentenza. ID_RICUDIEN: %d, IDCAT: %d",
                            udienzaRicorso.getIdUdieRicorso(), idCat));
                } else {
                    LOGGER.warn(String.format(
                            "[LOGGING NRG/ID_UDIEN] getSetenzaByIdCatAndNrg - UdienzaRicorso trovato, ma Set Esiti e' vuoto o null. ID_RICUDIEN: %d, IDCAT: %d",
                            udienzaRicorso.getIdUdieRicorso(), idCat));
                }
            } else {
                LOGGER.warn(String.format(
                        "[LOGGING NRG/ID_UDIEN] getSetenzaByIdCatAndNrg - UdienzaRicorso NON trovato usando ID_UDIEN: %d from ProvvedimentoPenale e NRG: %s. IDCAT: %d",
                        provvedimentoPenale.getIdUdienza(), nrg, idCat));
            }
        } else {
            LOGGER.warn(String.format(
                    "[LOGGING NRG/ID_UDIEN] getSetenzaByIdCatAndNrg - Condizioni non soddisfatte per cercare UdienzaRicorso. ProvvedimentoPenale: %s (ID_UDIEN: %d), NRG: %s. IDCAT: %d",
                    provvedimentoPenale != null ? "Trovato" : "NON Trovato",
                    provvedimentoPenale != null ? provvedimentoPenale.getIdUdienza() : null, nrg, idCat));
        }
        LOGGER.info(String.format(
                "[LOGGING NRG/ID_UDIEN] getSetenzaByIdCatAndNrg - END - Sentenza non trovata. IDCAT: %d, NRG: %s",
                idCat,
                nrg));
        return null;
    }

    public RicercaSentenza getRicercaSentenzaByIdCatAndNrg(Long idCat) throws SicRepositoryException {
        ProvvedimentoPenale provvedimentoPenale = findProvvedimentoByIdCat(idCat);
        if (provvedimentoPenale != null && provvedimentoPenale.getIdUdienza() > 0 && provvedimentoPenale.getNrg() > 0) {
            UdienzaRicorso udienzaRicorso = findEntityByQuery(
                    "FROM dettaglioricorso.UdienzaRicorso  where udienza.id = :idUdien and ricorsoSic.nrg= :nrg and lastUdie = 1",
                    UdienzaRicorso.class,
                    getCompiledMap("idUdien", provvedimentoPenale.getIdUdienza(), "nrg", provvedimentoPenale.getNrg()));
            return findEntityByQuery("FROM RicercaSentenza  where idRicorsoUdienza = :idRicUdien",
                    RicercaSentenza.class,
                    getCompiledMap("idRicUdien", udienzaRicorso.getIdUdieRicorso()));

        }
        return null;
    }

    public ProvvedimentoPenale findProvvedimentoByIdCat(Long idCat) {
        LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] findProvvedimentoByIdCat - START - IDCAT: %d", idCat));
        ProvvedimentoPenale provvedimento = findEntityByQuery(
                "FROM dettaglioricorso.ProvvedimentoPenale p WHERE p.idCat = :idCat",
                ProvvedimentoPenale.class, getCompiledMap("idCat", idCat));
        if (provvedimento != null) {
            LOGGER.info(String.format(
                    "[LOGGING NRG/ID_UDIEN] findProvvedimentoByIdCat - ProvvedimentoPenale Trovato. ID_PROVV: %s, ID_UDIEN: %d, NRG: %s, FK_IDCAT (original param): %d",
                    provvedimento.getIdProvv(), provvedimento.getIdUdienza(), provvedimento.getNrg(), idCat));
        } else {
            LOGGER.warn(String.format(
                    "[LOGGING NRG/ID_UDIEN] findProvvedimentoByIdCat - ProvvedimentoPenale NON Trovato per IDCAT: %d",
                    idCat));
        }
        return provvedimento;
    }

    public UtentePenale findUtenteModificaByIdProvvedimento(String idProvv, StatoProvvedimento statoProvvedimento)
            throws SicRepositoryException {
        return findEntityByQuery(
                "FROM UtentePenale up WHERE up.idUtente = (select pc.idUtente from dettaglioricorso.StoricoProvvedimento pc where pc.idProvv = :idProvv and pc.stato = :stato and pc.oggi = (select max(cs.oggi) from dettaglioricorso.StoricoProvvedimento cs where cs.idProvv = :idProvv and cs.stato = :stato ))",
                UtentePenale.class, getCompiledMap("idProvv", idProvv, "stato", statoProvvedimento));
    }

    public RicorsoSic getRicorsoByNrg(Long nrg) {
        RicorsoSic ricorsoSic = findEntityByQuery("FROM RicorsoSic WHERE nrg = :nrg", RicorsoSic.class,
                getCompiledMap("nrg", nrg));
        return ricorsoSic;
    }

    public Date getDataUdienza(Long nrg) {
        LOGGER.info("getDataUdienza inizio");
        Query query = em.createNativeQuery(
                "select u.DATAUD from PENALE_T_UDIENZA u join PENALE_T_RICUDIEN ru on u.id_udien= ru.id_udien and ru.lastudie=1 and u.dataud>=sysdate and ru.nrg="
                        + nrg);
        LOGGER.info("getDataUdienza fine");
        Date dataFissazione = null;
        try {
            dataFissazione = (Date) query.getSingleResult();
        } catch (NoResultException e) {
            LOGGER.info("Nessuna data udienza trovata");
        }
        return dataFissazione;
    }

    public String getDatiUdienzaPerNotifica(Long nrg) {
        LOGGER.info("getDatiUdienza inizio");
        Query query = em.createNativeQuery("SELECT " + "'. Udienza ' || " + "u.SEZIONE || " + "', ' || "
                + "to_char(u.DATAUD ,'DD/MM/YYYY') || " + "', ' || " + "u.TIPOUD || " + "DECODE(u.AULA,NULL,'',', ') "
                + "|| u.AULA "
                + "FROM " + "PENALE_UDIENZA u " + "JOIN PENALE_T_RICUDIEN ru ON " + "u.id_udien = ru.id_udien "
                + "AND ru.lastudie = 1 "
                + "and ru.lastudie=1 and ru.nrg=" + nrg);
        LOGGER.info("getDatiUdienza fine");
        String datiUdienza = null;
        try {
            datiUdienza = (String) query.getSingleResult();
        } catch (NoResultException e) {
            LOGGER.info("Nessuna data udienza trovata");
        }
        return datiUdienza;
    }

    public Date getDataFissazioneUdienza(Long nrg) {
        LOGGER.info("getDataFissazioneUdienza inizio");
        Query query = em
                .createNativeQuery(
                        "SELECT OGGI FROM PENALE_T_STORIA WHERE ID_FUNZIONE IN (12, 293) AND TIPOREC = 9 AND NRG = "
                                + nrg);
        LOGGER.info("getDataFissazioneUdienza fine");
        Date dataFissazione = null;
        try {
            dataFissazione = (Date) query.getSingleResult();
        } catch (NoResultException notFound) {
            LOGGER.info("Nessuna data fissazione udienza trovata");
        } catch (NonUniqueResultException moreThanOneResult) {
            List<Date> listaDateUdienza = (List<Date>) query.getResultList();
            Collections.sort(listaDateUdienza, Collections.reverseOrder());
            dataFissazione = listaDateUdienza.get(0);
        }
        return dataFissazione;
    }

    // public Avvocato getAnagraficaAvvocato(String codiceFiscale) {
    // // Devo fare questo schifo perchè l'anagrafica dovrebbe essere solo una ma in
    // realtà non è così
    // // se c'è dataCassazionista almeno una vota nel db, imposto il tipo come
    // Cassazionista, altrimenti prendo il primo record che trovo
    // // "cassazionista" nell'xml
    // Avvocato avvocato = findEntityByQueryList(
    // "FROM sic.Avvocato where codiceFiscale = :codiceFiscale and dataCassazionista
    // is not null", Avvocato.class,
    // getCompiledMap("codiceFiscale", codiceFiscale));
    // if (avvocato == null) {
    // avvocato = findEntityByQueryList("FROM sic.Avvocato where codiceFiscale =
    // :codiceFiscale", Avvocato.class,
    // getCompiledMap("codiceFiscale", codiceFiscale));
    // }
    // return avvocato;
    // }
    //
    // public AnagraficaParte getAnagraficaParte(String codiceFiscale) {
    // // Devo fare questo schifo perchè l'anagrafica dovrebbe essere solo una ma in
    // realtà non è così
    // return findEntityByQueryList("FROM AnagraficaParte where codiceFiscale =
    // :codiceFiscale", AnagraficaParte.class,
    // getCompiledMap("codiceFiscale", codiceFiscale));
    // }
    //
    public Utente findUtenteByUsername(String username) throws SicRepositoryException {
        return findEntityByQuery("FROM Utente WHERE identificativo = :username", Utente.class,
                getCompiledMap("username", username));
    }

    public List<PenaleRicercaRiunitiView> getRicorsoRiunitoViewByIdRicUdienPadre(Long idRicUdienPadre)
            throws SicRepositoryException {
        return findEntitiesByQuery("FROM PenaleRicercaRiunitiView WHERE  idRicUdienPadre = :idRicUdienPadre",
                PenaleRicercaRiunitiView.class, getCompiledMap("idRicUdienPadre", idRicUdienPadre));
    }

    /**
     * PERFORMANCE OPTIMIZATION: Batch method to get ricorso riuniti for multiple
     * parent IDs
     * This replaces multiple individual queries with a single batch query
     */
    public List<PenaleRicercaRiunitiView> getRicorsoRiunitoViewByIdRicUdienPadreBatch(List<Long> idRicUdienPadreList)
            throws SicRepositoryException {
        if (idRicUdienPadreList == null || idRicUdienPadreList.isEmpty()) {
            return new ArrayList<>();
        }

        // Process in batches to avoid Oracle IN clause limit (1000 items)
        List<PenaleRicercaRiunitiView> result = new ArrayList<>();
        int batchSize = 500;

        for (int i = 0; i < idRicUdienPadreList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, idRicUdienPadreList.size());
            List<Long> batch = idRicUdienPadreList.subList(i, endIndex);

            List<PenaleRicercaRiunitiView> batchResult = findEntitiesByQuery(
                    "FROM PenaleRicercaRiunitiView WHERE idRicUdienPadre IN (:idRicUdienPadreList)",
                    PenaleRicercaRiunitiView.class,
                    getCompiledMap("idRicUdienPadreList", batch));

            result.addAll(batchResult);
        }

        return result;
    }

    public List<Utente> findUtenteByCodiceFiscale(String codiceFiscale) {
        return findEntitiesByQuery("FROM Utente WHERE codiceFiscale = :codiceFiscale", Utente.class,
                getCompiledMap("codiceFiscale", codiceFiscale));
    }

    //
    public PenaleFunzioni findFunzione(String nome) throws SicRepositoryException {
        return findEntityByQuery("FROM PenaleFunzioni WHERE nome = :nome", PenaleFunzioni.class,
                getCompiledMap("nome", nome));
    }

    // public PenaleAnagAtti getAnagAtti(String sigla, PenaleParam natura) {
    // return findEntityByQuery("from PenaleAnagAtti where sigla = :sigla and natura
    // = :natura", PenaleAnagAtti.class,
    // getCompiledMap("sigla", sigla, "natura", natura.getIdParam()));
    // }
    //
    // public PenaleAnagAtti getAnagAttiByTagXml(String tag, PenaleParam natura) {
    // return findEntityByQuery("from PenaleAnagAtti where Tag_Xml = :tag and natura
    // = :natura", PenaleAnagAtti.class,
    // getCompiledMap("tag", tag, "natura", natura.getIdParam()));
    // }
    //
    // public PenaleAnagAtti getAnagAttiByLikeTagXml(String tag, PenaleParam natura)
    // {
    // return findEntityByQuery("from PenaleAnagAtti where Tag_Xml like :tag and
    // natura = :natura", PenaleAnagAtti.class,
    // getCompiledMap("tag", '%' + tag + '%', "natura", natura.getIdParam()));
    // }
    //
    public List<PenaleParam> getParamList(String tipoTab) {
        return findEntitiesByQuery("FROM PenaleParam sp WHERE sp.tipoTab = :tipoTab", PenaleParam.class,
                getCompiledMap("tipoTab", tipoTab));
    }

    //
    // /**
    // * Recupera la conclusione requisitoria dalla tabella tipologica del SIC
    // *
    // * @param descConclusione
    // * Descrizone testuale della conclusione
    // * @return Record della tipologica
    // */
    // public ConclusioneRequisitoriaSic findConclusioneRequisitoria(String
    // descConclusione) {
    // return findEntityByQuery("FROM ConclusioneRequisitoriaSic crs WHERE
    // crs.descrizione = :descConclusione",
    // ConclusioneRequisitoriaSic.class, getCompiledMap("descConclusione",
    // descConclusione));
    // }
    //
    // public Long getIdParam(String tipoTab, String sigla) {
    // PenaleParam param = getParam(tipoTab, sigla);
    // Long result = null;
    // if (param != null) {
    // result = param.getIdParam();
    // }
    // return result;
    // }
    //
    public PenaleParam getParam(String tipoTab, String sigla) {
        return findEntityByQuery("FROM PenaleParam sp WHERE sp.tipoTab = :tipoTab AND sp.sigla = :sigla",
                PenaleParam.class,
                getCompiledMap("tipoTab", tipoTab, "sigla", sigla));
    }

    public PenaleParam decodeParam(Long idParam) {
        return em.find(PenaleParam.class, idParam);
    }

    //
    // // Questa cosa è molto molesta
    // public Long generateNrgReale(Date dataIscrizione) throws
    // SicRepositoryException {
    // try {
    // Calendar cal = Calendar.getInstance();
    // cal.setTime(dataIscrizione);
    // int anno = cal.get(Calendar.YEAR);
    // String seqName = String.format("seq_%snrg", anno);
    // BigDecimal numero = (BigDecimal) em.createNativeQuery("SELECT " + seqName +
    // ".nextval FROM dual").getSingleResult();
    // return numero.longValue();
    // } catch (Throwable e) {
    // throw new SicRepositoryException("Errore nella generazione del numero di
    // ruolo generale", e);
    // }
    // }
    //
    public <T> T findEntity(Class<T> clazz, Object key) {
        return em.find(clazz, key);
    }

    protected <T> T findEntityByQueryList(String query, Class<T> clazz, Map<String, Object> params) {
        T result = null;
        List<T> list = findEntitiesByQuery(query, clazz, params);
        if (list != null && !list.isEmpty()) {
            result = list.get(0);
        }
        return result;
    }

    protected <T> T findEntityByQuery(String query, Class<T> clazz, Map<String, Object> params) {
        T result = null;
        try {
            result = fillQuery(query, clazz, params).getSingleResult();
        } catch (NoResultException ex) {
            LOGGER.warn("Oggetto " + clazz.getName() + " non trovato");
        }
        return result;
    }

    protected <T> List<T> findEntitiesByQuery(String query, Class<T> clazz, Map<String, Object> params) {
        List<T> result = null;
        try {
            result = fillQuery(query, clazz, params).getResultList();
        } catch (NoResultException ex) {
            LOGGER.warn("Oggetti " + clazz.getName() + " non trovato");
        }
        return result;
    }

    protected <T> TypedQuery<T> fillQuery(String query, Class<T> clazz, Map<String, Object> params) {
        TypedQuery<T> q = em.createQuery(query, clazz);
        for (String key : params.keySet()) {
            q.setParameter(key, params.get(key));
        }
        return q;
    }

    //
    // public Parte findParteRicorsoByCf(RicorsoSic ricorso, String cfParte) {
    // Set<Parte> partiRic = ricorso.getParti();
    // for (Parte p : partiRic) {
    // if (cfParte.equalsIgnoreCase(p.getAnagraficaParte().getCodiceFiscale())) {
    // return p;
    // }
    // }
    // return null;
    // }
    //
    // public AvvocatoRicorso findDifensoreRicorsoByCf(RicorsoSic ricorso, String
    // cfDifensore) {
    // Set<AvvocatoRicorso> difensori = ricorso.getAvvocatiRicorso();
    // for (AvvocatoRicorso d : difensori) {
    // if (cfDifensore.equalsIgnoreCase(d.getAvvocato().getCodiceFiscale())) {
    // return d;
    // }
    // }
    // return null;
    // }

    public DispositivoUdienzaSic findDispositivoUdienza(Long idSentenza, Long sezione, Long tipoSent) {
        return findEntityByQuery("FROM DispositivoUdienzaSic WHERE idSentenza = :sentenza AND sezione = :sezione",
                DispositivoUdienzaSic.class, getCompiledMap("sentenza", idSentenza, "sezione", sezione));
    }

    public List<SentenzaDettaglio> findFascicoliRiuniti(Long idSentenza, Long sezione, Long tipoSent) {
        return findEntitiesByQuery(
                "FROM dettaglioricorso.Sentenza WHERE sentenza = :sentenza AND sezione = :sezione AND tiposent = :tiposent",
                SentenzaDettaglio.class,
                getCompiledMap("sentenza", idSentenza, "sezione", sezione, "tiposent", tipoSent));
    }

    public DispositivoUdienzaSic upsertDispositivoUdienzaSic(Long idSentenza, Boolean contributoUnificatoStabilita,
            Long idDispositivo,
            Long sezione, Long tipoSent, Long oscuramento, Utente utente) throws GestioneSicException {

        DispositivoUdienzaSic dispositivoUdienzaSic = null;
        try {
            dispositivoUdienzaSic = findDispositivoUdienza(idSentenza, sezione, tipoSent);
        } catch (Exception e) {
            LOGGER.info("Dispositivo udienza non trovato...verrà creato");
        }
        try {
            if (dispositivoUdienzaSic != null) {
                mergeDispositivoUdienzaSicPubblica(dispositivoUdienzaSic, idSentenza, idDispositivo,
                        contributoUnificatoStabilita, sezione,
                        tipoSent, oscuramento, utente);
                em.merge(dispositivoUdienzaSic);
                LOGGER.info("AGGIORNATO DISPOSITIVO UDIENZA SIC ID:" + dispositivoUdienzaSic.getIdDispositivoUdienza()
                        + " IDDISPOSITIVO:"
                        + idDispositivo + " IDSENTENZA:" + idSentenza);
            } else {
                dispositivoUdienzaSic = new DispositivoUdienzaSic();
                mergeDispositivoUdienzaSicPubblica(dispositivoUdienzaSic, idSentenza, idDispositivo,
                        contributoUnificatoStabilita, sezione,
                        tipoSent, oscuramento, utente);
                em.persist(dispositivoUdienzaSic);
                LOGGER.info(
                        "VERSATO DISPOSITIVO UDIENZA SIC IDDISPOSITIVO:" + idDispositivo + " IDSENTENZA:" + idSentenza);
            }
            return dispositivoUdienzaSic;
        } catch (Exception ex) {
            LOGGER.error("Errore nel salvataggio dei dati del ricorso. idElabDep" + idDispositivo + ",  idSentenza:"
                    + idSentenza);
            throw new GestioneSicException("Errore nel salvataggio dei dati del ricorso",
                    RestErrorCodeEnum.RICORSO_SAVE_ERROR, ex);
        }
    }

    private void mergeDispositivoUdienzaSicPubblica(DispositivoUdienzaSic dispositivoUdienzaSic, Long idSentenza,
            Long idDispositivo,
            Boolean contributoUnificatoStabilita, Long sezione, Long tipoSent, Long oscuramento, Utente operatore) {
        dispositivoUdienzaSic.setIdSentenza(idSentenza);
        dispositivoUdienzaSic.setOggi(new Date());
        dispositivoUdienzaSic.setIdFunzione(new Long(705));
        dispositivoUdienzaSic.setSezione(sezione);
        dispositivoUdienzaSic.setTesto("Vedi Provvedimento Allegato.");
        dispositivoUdienzaSic.setOperatore(operatore.getIdUtente());
    }

    public void updateSentenzaPubblica(Long id, Long idProvvedimentoSic, Long nRaccg, Boolean motivazioneSemplificata,
            Date dataDeposito,
            Utente utente) throws Exception {
        Date oggi = new Date();
        SentenzaDettaglio sentenza = findEntity(SentenzaDettaglio.class, id.toString());
        sentenza.setnRaccg(nRaccg);
        sentenza.setDataPubbl(Utils.convertDateTimeToDate(oggi));
        sentenza.setOggi(oggi);
        sentenza.setOperatore(utente.getIdUtente());
        em.merge(sentenza);
    }

    // public DispositivoUdienzaSic getDispositivoUdienzaSic(Long idSentenza, Long
    // sezione, Long tipoSent)
    // throws GestioneSicException, CommonRestWarning {
    // try {
    // return findDispositivoUdienza(idSentenza, sezione, tipoSent);
    // } catch (NoResultException ex) {
    // LOGGER.info("Dispositivo udienza non trovato");
    // return null;
    // }
    // }
    //
    public List<PenaleReatiRicorso> getPenaleReatiRicorsoByNrg(Long nrg) throws CspBackendException {
        if (nrg == null) {
            throw new CspBackendException("Campo nrg obbligatorio non fornito. nrg null", RestErrorCodeEnum.NRG_NULL,
                    433);
        }
        LOGGER.info("Avvio ricerca reati ricorso per nrg:" + nrg);
        List<PenaleReatiRicorso> penaleReatiRicorso = findEntitiesByQuery("FROM PenaleReatiRicorso WHERE nrg = :nrg",
                PenaleReatiRicorso.class, getCompiledMap("nrg", nrg));
        for (PenaleReatiRicorso penale : penaleReatiRicorso) {
            PenaleReati penaleReato = findEntityByQuery("FROM PenaleReati WHERE idReato = :idReato", PenaleReati.class,
                    getCompiledMap("idReato", penale.getIdReato()));
            penale.setPenaleReati(penaleReato);
            if (penale.getPenaleReati() != null) {
                LOGGER.info("Inizio il calcolo della visualizzazione del reato per idReato:"
                        + penale.getPenaleReati().getIdReato());
                String displayReato = penale.getPenaleReati().getFonteNorm() + " " + penale.getPenaleReati().getArt()
                        + " "
                        + penale.getPenaleReati().getNumleg() + " " + penale.getPenaleReati().getAnno() + " "
                        + penale.getPenaleReati().getLettera() + " " + penale.getPenaleReati().getComma() + " "
                        + penale.getPenaleReati().getAltroIdentificativo() + " ";
                PenaleParam penaleParam = penale.getTipoD() != null ? decodeParam(penale.getTipoD()) : null;
                penale.setPenaleParam(penaleParam);

                if (penaleParam != null && !penaleParam.getDescrizione().equalsIgnoreCase("")) {
                    if (penaleParam.getDescrizione().contains("GG/MM/AAAA")) {
                        if (penaleParam.getDescrizione().startsWith("IL")) {
                            displayReato = displayReato + penaleParam.getDescrizione().replace("GG/MM/AAAA",
                                    DateUtils.fromDateToString("MM/dd/yyyy", penale.getDataDa()));
                        } else if (penaleParam.getDescrizione().startsWith("DAL")) {
                            displayReato = displayReato + penaleParam.getDescrizione().replace("DAL GG/MM/AAAA",
                                    "DAL " + DateUtils.fromDateToString("MM/dd/yyyy", penale.getDataDa()));
                            displayReato = displayReato + penaleParam.getDescrizione().replace("DAL GG/MM/AAAA",
                                    "AL " + DateUtils.fromDateToString("MM/dd/yyyy", penale.getDataA()));
                        } else if (penaleParam.getDescrizione().startsWith("FINO")) {
                            displayReato = displayReato + penaleParam.getDescrizione().replace("GG/MM/AAAA",
                                    DateUtils.fromDateToString("MM/dd/yyyy", penale.getDataA()));
                        }
                    } else if (penaleParam.getDescrizione().contains(" AAAA")) {
                        if (penaleParam.getDescrizione().startsWith("DA")) {
                            displayReato = displayReato + penaleParam.getDescrizione().replace("DA AAAA",
                                    "DA  " + DateUtils.fromDateToString("yyyy", penale.getDataDa()));
                            displayReato = displayReato + penaleParam.getDescrizione().replace("AD AAAA",
                                    "AD " + DateUtils.fromDateToString("yyyy", penale.getDataA()));
                        } else {
                            displayReato = displayReato
                                    + penaleParam.getDescrizione().replace("AAAA",
                                            DateUtils.fromDateToString("yyyy", penale.getDataDa()));

                        }
                    }
                }
                if (penale.getPenaleReati().getEppo() != null
                        && !penale.getPenaleReati().getEppo().equalsIgnoreCase("0")) {
                    displayReato = displayReato + " EPPO";
                }
                LOGGER.info("Fine calcolo della visualizzazione del reato per idReato:"
                        + penale.getPenaleReati().getIdReato()
                        + "; DisplayReato:" + displayReato);
                penale.setDisplayReato(displayReato);
            }

        }
        LOGGER.info("Ricerca reati ricorso per nrg:" + nrg + " completata. Totale reati trovati:"
                + penaleReatiRicorso.size());
        return penaleReatiRicorso;
    }

    public List<PenaleParti> getPenalePartiRicorsoByNrg(Long nrg) throws CspBackendException {
        if (nrg == null) {
            throw new CspBackendException("Campo nrg obbligatorio non fornito. nrg null", RestErrorCodeEnum.NRG_NULL,
                    433);
        }
        LOGGER.info("Avvio ricerca parti ricorso per nrg:" + nrg);
        List<PenaleParti> penalePartiRicorso = findEntitiesByQuery("FROM PenaleParti WHERE nrg = :nrg",
                PenaleParti.class,
                getCompiledMap("nrg", nrg));
        for (PenaleParti penale : penalePartiRicorso) {
            List<PenaleDifensoreParti> penaleDifensoreParties = findEntitiesByQuery(
                    "FROM PenaleDifensoreParti WHERE idParti = :idParti",
                    PenaleDifensoreParti.class, getCompiledMap("idParti", penale.getIdParte()));
            if (penaleDifensoreParties != null && !penaleDifensoreParties.isEmpty()) {
                for (PenaleDifensoreParti penaleDifensoreParti : penaleDifensoreParties) {
                    PenaleParam penaleParam = decodeParam(penaleDifensoreParti.getTipodifen());
                    penaleDifensoreParti.setPenaleParam(penaleParam);
                    // questa query restituisce sempre un solo risultato
                    Query query = em.createNativeQuery(
                            "select COGNDIFEN, NOMEDIFEN from CIVILE_ANAGDIFEN@CIVILE_DBLINK.CASSAZIONE.SIC cad WHERE  cad.ID_ANAGDIFEN = "
                                    + penaleDifensoreParti.getIdAnagdifen());
                    List<Object> result = (List<Object>) query.getResultList();
                    if (result.size() > 1) {
                        LOGGER.error(
                                "Risultato della query nella anagrafica difensore civile è maggiore di uno. nrg:" + nrg
                                        + ", resultSize:" + result.size());
                    }
                    LOGGER.info("Risultato della query nella anagrafica difensore civile. nrg:" + nrg + ", resultSize:"
                            + result.size());
                    Iterator itr = result.iterator();
                    while (itr.hasNext()) {
                        Object[] obj = (Object[]) itr.next();
                        String cognome = String.valueOf(obj[0]);
                        String nome = String.valueOf(String.valueOf(obj[1]));
                        penaleDifensoreParti.setNome(nome);
                        penaleDifensoreParti.setCognome(cognome);
                    }

                }
                penale.setPenaleDifensoreParti(penaleDifensoreParties);
            }
            PenaleAnagParti penaleParti = findEntityByQuery("FROM PenaleAnagParti WHERE idAnagParte = :idAnagParte",
                    PenaleAnagParti.class,
                    getCompiledMap("idAnagParte", penale.getIdAnagParte()));
            penale.setPenaleAnagParti(penaleParti);
            if (penale.getPenaleAnagParti() != null) {
                PenaleParam penaleParam = decodeParam(penale.getIdTipofig());
                penale.setPenaleParam(penaleParam);
                String displayParte = "";
                if (penaleParam != null) {
                    displayParte = displayParte + penaleParam.getSigla() + " - ";
                    if (penale.getRicorrente() != null && penale.getRicorrente().equalsIgnoreCase("1")) {
                        displayParte = displayParte + "R";
                    } else {
                        displayParte = displayParte + "N";
                    }
                    String cognome = penaleParti.getCognome() != null ? penaleParti.getCognome() : "";
                    String nome = penaleParti.getNome() != null ? penaleParti.getNome() : "";

                    penale.setDisplayParte(displayParte + " - " + cognome + " " + nome);
                    if (penaleParam.getTipoTab().equalsIgnoreCase("CONTROPARTE")
                            || penaleParam.getTipoTab().equalsIgnoreCase("FIGURA")) {
                        Query query = em.createNativeQuery(
                                "SELECT AP.COGNOME AS COGNOME, AP.NOME AS NOME, TP.ID_TIPOFIG AS TIPOFIGURA, TP.RICORRENTE AS RICORRENTE FROM "
                                        + "PENALE_ANAGPARTI AP LEFT JOIN PENALE_T_PARTI TP ON TP.ID_ANAGPARTE = AP.ID_ANAGPARTE WHERE TP.ID_PARTE IN "
                                        + "(SELECT ID_PARTE_2 FROM MIGRA.PENALE_PARTI_CONTRO WHERE ID_PARTE_1 = "
                                        + penale.getIdParte()
                                        + ")");

                        List<Object> result = (List<Object>) query.getResultList();
                        if (result != null && !result.isEmpty()) {
                            penale.setPenaleControParti(new ArrayList<PenaleControParti>());
                            Iterator itr = result.iterator();
                            while (itr.hasNext()) {
                                Object[] obj = (Object[]) itr.next();
                                PenaleParam ruoloControparte = decodeParam(((BigDecimal) obj[2]).longValue());
                                penale.setPenaleParam(ruoloControparte);
                                PenaleControParti penaleControParti = new PenaleControParti();
                                if (obj[3] != null && obj[3].toString().equalsIgnoreCase("1")) {
                                    penaleControParti.setDisplayParte(
                                            ruoloControparte.getSigla() + " - R - " + obj[0] + " "
                                                    + (obj[1] != null ? obj[1] : ""));
                                } else {
                                    penaleControParti.setDisplayParte(
                                            ruoloControparte.getSigla() + " - N - " + obj[0] + " "
                                                    + (obj[1] != null ? obj[1] : ""));
                                }
                                penale.getPenaleControParti().add(penaleControParti);
                            }
                        }
                    }
                }
            }
        }
        LOGGER.info("Ricerca parti ricorso per nrg:" + nrg + " completata. Totale parti trovate:"
                + penalePartiRicorso.size());
        return penalePartiRicorso;
    }

    public int countPartiByNrgReale(String nrgreale) throws CspBackendException {
        if (nrgreale == null) {
            throw new CspBackendException("Campo nrgreale obbligatorio non fornito. nrgreale null",
                    RestErrorCodeEnum.NRG_NULL, 433);
        }
        Query query = em.createNativeQuery(
                "SELECT p.ID_PARTE FROM PENALE_PARTI p JOIN PENALE_RICORSO r ON r.NRG = p.NRG "
                        + "WHERE r.NRGREALE = :nrgreale");
        query.setParameter("nrgreale", nrgreale);
        List<Long> result = (List<Long>) query.getResultList();
        return result.size();
    }

    /**
     * Cerca l'elenco degli NRG dei ricorsi associati alla parte (in particolare al
     * cognome della parte) Dalla ricerca sono esclusi i
     * cognomi dei Procuratori e dei difensori. Oltre a filtrare per cognome e
     * ruolo, se indicati vengono applicati anche gli altri filtri
     * di ricerca
     *
     * @param params
     *                    lista dei parametri di ricerca
     * @param firstResult
     *                    primo elemento della lista da restituire
     * @param maxResult
     *                    numero massimo di elementi da restituire
     * @return lista degli NRG e ID_RICUDIEN dei ricorsi trovati
     */
    public List<RicercaRicorsiKeysDto> getRicorsiByCognomeParte(RicercaPartiParams params, int firstResult,
            int maxResult) {
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT p.NRG, ru.ID_RICUDIEN ");
        Query query = buildQueryRicercaByCognomeParte(params, sb, true);
        query.setFirstResult(firstResult);
        query.setMaxResults(maxResult);

        List<Object[]> queryResult = query.getResultList();
        List<RicercaRicorsiKeysDto> result = new ArrayList<>();
        for (Object[] singleResult : queryResult) {
            Long nrg = ((Number) singleResult[0]).longValue();
            Long idRicUdien = (singleResult[1] != null) ? ((Number) singleResult[1]).longValue() : null;
            result.add(new RicercaRicorsiKeysDto(nrg, idRicUdien));
        }

        return result;
    }

    /**
     * Conta il numero totale dei ricorsi associati alla parte (in particolare al
     * cognome della parte) Dalla ricerca sono esclusi i cognomi
     * dei Procuratori e dei difensori. Oltre a filtrare per cognome e ruolo, se
     * indicati vengono applicati anche gli altri filtri di
     * ricerca
     *
     * @param params
     *               lista dei parametri di ricerca
     * @return numero totale di record trovati
     */
    public long calculateTotRecordsRicorsiByParte(RicercaPartiParams params) {
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT count(*) ");
        Query query = buildQueryRicercaByCognomeParte(params, sb, false);

        return ((Number) query.getSingleResult()).longValue();
    }

    private Query buildQueryRicercaByCognomeParte(RicercaPartiParams params, StringBuilder sb, boolean isOrdered) {
        sb.append("FROM PENALE_PARTI p " + "JOIN PENALE_ANAGPARTI a ON p.ID_ANAGPARTE = a.ID_ANAGPARTE "
                + "JOIN PENALE_RICORSO r ON r.NRG = p.NRG ");

        if (params.getSezione() != null || params.getDataUdDa() != null || params.getDataUdA() != null
                || params.getTipoUd() != null
                || params.getCollegio() != null || params.getCfPresidente() != null) {
            sb.append("JOIN PENALE_RICUDIEN ru ON ru.NRG = r.NRG "
                    + "JOIN PENALE_UDIENZA u ON u.ID_UDIEN = ru.ID_UDIEN ");
        } else {
            sb.append("LEFT JOIN PENALE_RICUDIEN ru ON ru.NRG = r.NRG ");
            if (params.getOrderBy().equals("dataUd")) {
                sb.append("LEFT JOIN PENALE_UDIENZA u ON u.ID_UDIEN = ru.ID_UDIEN ");
            }
        }

        if (params.getCfPresidente() != null) {
            sb.append("JOIN V_RICERCA_RICUDIEN_WITH_REL_PRE vru ON vru.ID_UDIEN = u.ID_UDIEN AND vru.NRG = r.NRG ");
        }

        if (params.getCfEstensore() != null || params.getDataMinutaDa() != null || params.getDataMinutaA() != null
                || params.getDataPubblicazioneDa() != null || params.getDataPubblicazioneA() != null
                || params.getRemoveRiuniti() != null
                || params.getOrderBy().equals("nraccg")) {
            sb.append("JOIN V_RICERCA_PENALE_SENTENZA vrps ON vrps.ID_RICUDIEN = ru.ID_RICUDIEN ");
        } else {
            sb.append("LEFT JOIN V_RICERCA_PENALE_SENTENZA vrps ON vrps.ID_RICUDIEN = ru.ID_RICUDIEN ");
        }

        if (params.getOrderBy().equals("oscuramentoSic")) {
            sb.append("LEFT JOIN PENALE_PARAM pp ON pp.ID_PARAM = VRPS.PRIVACY ");
        } else if (params.getOrderBy().equals("oscuramentoDeskCsp")) {
            sb.append("LEFT JOIN PENALE_PROVVEDIMENTI pr ON pr.NRG = r.NRG " + "AND pr.ID_UDIEN = ru.ID_UDIEN "
                    + "AND pr.LAST_MODIFIED = 1 " + "AND pr.TIPO IS NOT NULL " + "LEFT JOIN (SELECT IDPROVV, OSCURATO, "
                    + "ROW_NUMBER() OVER (PARTITION BY IDPROVV ORDER BY IDPROVV) as rn "
                    + "FROM PROVV_LAV_FILE WHERE OSCURATO = 1) plf "
                    + "ON plf.IDPROVV = pr.ID_PROVV AND plf.rn = 1 ");
        }

        if (Boolean.TRUE.equals(params.getRemoveRiuniti())) {
            sb.append("LEFT JOIN CSPBACKEND_RICERCA_RIUNITI crr ON crr.ID_RICUDIEN = ru.ID_RICUDIEN ");
        }

        sb.append("WHERE LOWER(TRIM(a.cognome)) = LOWER(TRIM(:cognomeParte)) "
                + "AND p.TIPOFIG IN ('AL', 'CA', 'CG', 'CO', 'FC', 'IA', 'ID', 'IL', 'IN', "
                + "'PC', 'RC', 'TI', 'NN', 'PO', 'LA', 'DA', 'RR', 'DD', 'RD', 'AA', 'IC', 'UC', 'DI', 'DO') ");

        if (params.getSezione() != null) {
            sb.append("AND u.SEZIONE = :sezione ");
        }

        if (params.getDataUdDa() != null) {
            sb.append("AND u.DATAUD >= :dataUdDa ");
        }

        if (params.getDataUdA() != null) {
            sb.append("AND u.DATAUD <= :dataUdA ");
        }

        if (params.getTipoUd() != null) {
            sb.append("AND u.TIPOUD = :tipoUd ");
        }

        if (params.getCollegio() != null) {
            sb.append("AND u.AULA = :collegio ");
        }

        if (params.getNrgrealeDa() != null) {
            sb.append("AND r.NRGREALE >= :nrgrealeDa ");
        }

        if (params.getNrgrealeA() != null) {
            sb.append("AND r.NRGREALE <= :nrgrealeA ");
        }

        if (params.getCfPresidente() != null) {
            sb.append("AND vru.CF_PRESIDENTE = :presidente ");
        }

        if (params.getCfEstensore() != null) {
            sb.append("AND vrps.CF_ESTENSORE = :estensore ");
        }

        if (params.getDataMinutaDa() != null) {
            sb.append("AND vrps.DATAMINUTA >= :dataMinutaDa ");
        }

        if (params.getDataMinutaA() != null) {
            sb.append("AND vrps.DATAMINUTA <= :dataMinutaA ");
        }

        if (params.getDataPubblicazioneDa() != null) {
            sb.append("AND vrps.DATAPUBBL >= :dataPubblDa ");
        }

        if (params.getDataPubblicazioneA() != null) {
            sb.append("AND vrps.DATAPUBBL <= :dataPubblA ");
        }

        // Condizioni usate nella ricerca in monitoraggio: se indicato rimuove i riuniti
        // e filtra per tipo udienza
        if (Boolean.TRUE.equals(params.getRemoveRiuniti())) {
            sb.append("AND (crr.RIUNITO IS NULL OR crr.RIUNITO = 0) ").append(
                    "AND vrps.ESITO_PARAM IN (SELECT ID_PARAM FROM PENALE_PARAM ppesito WHERE ppesito.TIPOTAB = 'ESITOUD' AND ppesito.SIGLA IN ('P', 'D'))");
        }

        if (isOrdered) {
            String order = params.getOrder() != null ? " " + params.getOrder() : "";
            // Caso per ordinamento del numero del sezionale, occorre estrarre anno e numero
            // e ordinarli separatamente
            if (params.getOrderBy().equals("numeroSezionale")) {
                sb.append("ORDER BY TO_NUMBER(SUBSTR(vrps.SENTENZA, 0, 4)) ").append(order)
                        .append(", TO_NUMBER(LTRIM(SUBSTR(vrps.SENTENZA, 5, 9), '0')) ").append(order);
            } else {
                String orderProp;
                switch (params.getOrderBy()) {
                    case "numOrd":
                        orderProp = "ru.NUMORD";
                        break;
                    case "dataUd":
                    case "dataUdienza":
                        orderProp = "u.DATAUD";
                        break;
                    case "tipoUdienza":
                        orderProp = "u.TIPOUD";
                        break;
                    case "semplificata":
                        orderProp = "vrps.SEMPLIFICATA";
                        break;
                    case "nraccg":
                    case "numRaccGenerale":
                        orderProp = "vrps.NRACCG";
                        break;
                    case "oscuramentoSic":
                        orderProp = "NVL(pp.SIGLA, 'NO')";
                        break;
                    case "oscuramentoDeskCsp":
                        orderProp = "NVL(plf.OSCURATO, 0)";
                        break;
                    case "numeroSezionale":
                        orderProp = "vrps.SENTENZA";
                        break;
                    default:
                        orderProp = "r.NRGREALE";
                }
                sb.append("ORDER BY ").append(orderProp).append(" ").append(order);
            }
        }

        Query query = em.createNativeQuery(sb.toString());
        query.setParameter("cognomeParte", params.getCognomeParte());

        if (params.getSezione() != null) {
            query.setParameter("sezione", params.getSezione());
        }

        if (params.getDataUdDa() != null) {
            query.setParameter("dataUdDa", params.getDataUdDa());
        }

        if (params.getDataUdA() != null) {
            query.setParameter("dataUdA", params.getDataUdA());
        }

        if (params.getTipoUd() != null) {
            query.setParameter("tipoUd", params.getTipoUd());
        }

        if (params.getCollegio() != null) {
            query.setParameter("collegio", params.getCollegio());
        }

        if (params.getNrgrealeDa() != null) {
            query.setParameter("nrgrealeDa", params.getNrgrealeDa());
        }

        if (params.getNrgrealeA() != null) {
            query.setParameter("nrgrealeA", params.getNrgrealeA());
        }

        if (params.getCfPresidente() != null) {
            query.setParameter("presidente", params.getCfPresidente());
        }

        if (params.getCfEstensore() != null) {
            query.setParameter("estensore", params.getCfEstensore());
        }

        if (params.getDataMinutaDa() != null) {
            query.setParameter("dataMinutaDa", params.getDataMinutaDa());
        }

        if (params.getDataMinutaA() != null) {
            query.setParameter("dataMinutaA", params.getDataMinutaA());
        }

        if (params.getDataPubblicazioneDa() != null) {
            query.setParameter("dataPubblDa", params.getDataPubblicazioneDa());
        }

        if (params.getDataPubblicazioneA() != null) {
            query.setParameter("dataPubblA", params.getDataPubblicazioneA());
        }
        return query;
    }

    public Map<Long, RicercaRicorsiBaseInUdienza> getRicorsiInUdienzaByIdRicudienList(List<Long> idRicudienList) {
        Map<Long, RicercaRicorsiBaseInUdienza> ricorsiInUdienza = new HashMap<>();
        if (idRicudienList == null || idRicudienList.isEmpty())
            return ricorsiInUdienza;
        List<RicercaRicorsiBaseInUdienza> ricorsi = findEntitiesByQuery(
                "FROM RicercaRicorsiBaseInUdienza WHERE ID_RICUDIEN IN (:params)",
                RicercaRicorsiBaseInUdienza.class, getCompiledMap("params", idRicudienList));
        for (RicercaRicorsiBaseInUdienza ricorso : ricorsi) {
            ricorsiInUdienza.put(ricorso.getRicercaRicorsiBaseInUdienzaPK().getIdRicUdien(), ricorso);
        }
        return ricorsiInUdienza;
    }

    public Map<Long, RicercaRicorsiBaseFuoriUdienza> getRicorsiInUdienzaByIdNrgList(List<Long> nrgFuoriUdienzaList) {
        Map<Long, RicercaRicorsiBaseFuoriUdienza> ricorsiFuoriUdienza = new HashMap<>();
        if (nrgFuoriUdienzaList == null || nrgFuoriUdienzaList.isEmpty())
            return ricorsiFuoriUdienza;
        List<RicercaRicorsiBaseFuoriUdienza> ricorsi = findEntitiesByQuery(
                "FROM RicercaRicorsiBaseFuoriUdienza WHERE NRG IN (:params)",
                RicercaRicorsiBaseFuoriUdienza.class, getCompiledMap("params", nrgFuoriUdienzaList));
        for (RicercaRicorsiBaseFuoriUdienza ricorso : ricorsi) {
            ricorsiFuoriUdienza.put(ricorso.getNrg(), ricorso);
        }
        return ricorsiFuoriUdienza;
    }

    public List<PenaleUdienzeView> getUdienzeByNrgList(List<Long> nrgList) {
        List<PenaleUdienzeView> udienzeList = findEntitiesByQuery("FROM PenaleUdienzeView WHERE NRG IN (:params)",
                PenaleUdienzeView.class,
                getCompiledMap("params", nrgList));
        // Occorre aggiornamento manuale per garantire l'ordinamento della lista passata
        // come parametro
        Map<Long, PenaleUdienzeView> udienzeMap = new HashMap<>();
        for (PenaleUdienzeView udienza : udienzeList) {
            udienzeMap.put(udienza.getPenaleUdienzeViewPK().getNrg(), udienza);
        }
        List<PenaleUdienzeView> orderedUdienze = new ArrayList<>();
        for (long nrg : nrgList) {
            PenaleUdienzeView udienzaView = udienzeMap.get(nrg);
            if (udienzaView != null)
                orderedUdienze.add(udienzaView);
        }
        return orderedUdienze;
    }

    public List<String> getCognomiParte(String cognomeParte) {

        // Nella query sono esclusi i Procuratori e i difensori
        Query query = em.createNativeQuery("SELECT DISTINCT a.COGNOME FROM PENALE_PARTI p "
                + "JOIN PENALE_ANAGPARTI a ON p.ID_ANAGPARTE = a.ID_ANAGPARTE "
                + "WHERE lower(a.COGNOME) LIKE lower(:cognomeParte) "
                + "AND p.TIPOFIG IN ('AL', 'CA', 'CG', 'CO', 'FC', 'IA', 'ID', 'IL', 'IN', "
                + "'PC', 'RC', 'TI', 'NN', 'PO', 'LA', 'DA', 'RR', 'DD', 'RD', 'AA', 'IC', 'UC', 'DI', 'DO') "
                + "ORDER BY a.COGNOME");

        query.setParameter("cognomeParte", cognomeParte + "%");
        query.setMaxResults(30);

        return (List<String>) query.getResultList();
    }

    public Long getIdUtentePresidenteByIdUdien(Long idUdien) {
        Query query = em.createNativeQuery(
                "SELECT ptu.ID_UTENTE FROM PENALE_COLLEGIO pc "
                        + "JOIN PENALE_T_MAGIS ptm ON ptm.ID_MAGIS = pc.ID_MAGIS "
                        + "JOIN PENALE_ANAGMAGIS pa ON pa.ID_ANAGMAGIS = ptm.ID_ANAGMAGIS "
                        + "JOIN PENALE_T_UTENTI ptu ON ptu.CODICE_FISCALE = pa.CODICE_FISCALE "
                        + "WHERE pc.ID_UDIEN = :idUdien AND pc.TIPOMAG = 'PRE'");

        query.setParameter("idUdien", idUdien);
        query.setMaxResults(1);
        return ((Number) query.getSingleResult()).longValue();
    }

    public List<String> getReatiRicorsoByNrg(Long nrg) {
        Query query = em.createNativeQuery(
                "SELECT reati.FONTENORM || ' ' || reati.ART || ' ' || reati.NUMLEG || ' ' || reati.ANNO || ' ' || reati.LETTERA || ' ' || reati.COMMA || ' ' || reati.ALTRO_IDENTIFICATIVO reato FROM PENALE_T_REATI reati WHERE ID_REATO IN (SELECT ID_REATO FROM PENALE_REATIRICORSO WHERE NRG = "
                        + nrg + ")");
        return (List<String>) query.getResultList();
    }

    public List<String> getPartiRicorsoByNrg(Long nrg) {
        Query query = em.createNativeQuery(
                "SELECT cognome || ' ' || nome FROM PENALE_ANAGPARTI WHERE ID_ANAGPARTE IN (SELECT ID_ANAGPARTE FROM PENALE_T_PARTI WHERE NRG = "
                        + nrg + ")");
        return (List<String>) query.getResultList();
    }

    public BigDecimal getIdSezione(String sezione) {
        Query query = em.createNativeQuery(
                "select a.id_param from penale_param a where a.sigla ='" + sezione + "' and tipotab='SEZIONI'");
        return (BigDecimal) query.getSingleResult();
    }

    public BigDecimal getIdSentMaster() {
        /* TODO */
        Query query = em.createNativeQuery("SELECT SEQ_P_SENT.NEXTVAL FROM DUAL");
        return (BigDecimal) query.getSingleResult();
    }

    public BigDecimal getnRaccg(String anno) {
        Query query = em
                .createNativeQuery("SELECT (MAX(NRACCG) + 100) " + "FROM PENALE_SENTENZA "
                        + "WHERE TO_CHAR(DATAPUBBL,'YYYY') = " + anno);
        BigDecimal result = (BigDecimal) query.getSingleResult();
        if (result == null) {
            result = new BigDecimal(anno + "00000100");
        }
        return result;
    }

    public static <K> Map<String, K> getCompiledMap(Object... params) {
        Map<String, K> res = new HashMap<>();
        for (int i = 0; i < params.length - 1; i += 2) {
            res.put(params[i].toString(), (K) params[i + 1]);
        }
        return res;
    }

    public void createStoriaNraccg(Utente operatore, Long nrg, Long idRecord, String note, Long idSezione) {
        Date adesso = new Date();
        StoriaSic storiaSic = new StoriaSic();
        storiaSic.setOperatore(operatore.getIdUtente());
        storiaSic.setNrg(nrg);
        storiaSic.setIdFunzione(42L);
        storiaSic.setOggi(adesso);
        storiaSic.setDataInizio(adesso);
        storiaSic.setTipoRec(26);
        storiaSic.setUffInvio(idSezione);
        storiaSic.setUffArrivo(idSezione);
        storiaSic.setIdRecord(idRecord);
        storiaSic.setNote(note);
        em.persist(storiaSic);
    }

    // public void createStoriaEliminaDocumento(Utente operatore, Long nrg, String
    // note, Long idRecord, Long idSezione) {
    // Date adesso = new Date();
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(705L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(adesso);
    // storiaSic.setTipoRec(122);
    // storiaSic.setUffInvio(idSezione);
    // storiaSic.setUffArrivo(idSezione);
    // storiaSic.setIdRecord(idRecord);
    // storiaSic.setNote(note);
    // em.persist(storiaSic);
    // }
    //
    // public void createStoriaAttovis(Utente operatore, Long nrg, Long idRecord,
    // String note, Long idSezione) {
    // Date adesso = new Date();
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(new Long(42));
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(adesso);
    // storiaSic.setTipoRec(119);
    // storiaSic.setUffInvio(idSezione);
    // storiaSic.setUffArrivo(idSezione);
    // storiaSic.setIdRecord(idRecord);
    // storiaSic.setNote(note);
    // em.persist(storiaSic);
    // }
    //
    // public void createStoriaIstanzaOpposizione380bis(Utente operatore, Long nrg,
    // Long idCat, Long idSezione, Date dataDeposito) {
    // Date adesso = new Date();
    // class SearchParams extends CriteriaParametersBackend<ProposteAccelerate> {
    //
    // @CriteriaParametersBackend.SearchParameter(property = "nrg", operator =
    // CriteriaParametersBackend.Operator.EQUAL)
    // String nrg;
    //
    // public SearchParams(String nrg) {
    // this.nrg = nrg;
    // }
    // }
    // CriteriaBuilderHelper<ProposteAccelerate> criteriaBuilder = new
    // CriteriaBuilderHelper<>(ProposteAccelerate.class);
    // List<ProposteAccelerate> proposteAccelerateList =
    // criteriaBuilder.resultList(em, new SearchParams(String.valueOf(nrg)));
    // if (proposteAccelerateList.size() > 0) {
    // ProposteAccelerate proposteAccelerate = proposteAccelerateList.get(0);
    // if (idCat != null) {
    // proposteAccelerate.setIdCatOpposizione(String.valueOf(idCat));
    // } else {
    // proposteAccelerate.setIdCatOpposizione("n.d.");
    // }
    // proposteAccelerate.setDataDepOpposizione(dataDeposito);
    //
    // em.merge(proposteAccelerate);
    // }
    //
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(new Long(42));
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(adesso);
    // storiaSic.setTipoRec(121);
    // storiaSic.setUffInvio(idSezione.longValue());
    // storiaSic.setUffArrivo(idSezione.longValue());
    // em.persist(storiaSic);
    // }
    //
    // public void visiblePropostaAccelerata(Long nrg) {
    // Date adesso = new Date();
    // class SearchParams extends CriteriaParametersBackend<ProposteAccelerate> {
    //
    // @CriteriaParametersBackend.SearchParameter(property = "nrg", operator =
    // CriteriaParametersBackend.Operator.EQUAL)
    // String nrg;
    //
    // public SearchParams(String nrg) {
    // this.nrg = nrg;
    // }
    // }
    // CriteriaBuilderHelper<ProposteAccelerate> criteriaBuilder = new
    // CriteriaBuilderHelper<>(ProposteAccelerate.class);
    // List<ProposteAccelerate> proposteAccelerateList =
    // criteriaBuilder.resultList(em, new SearchParams(String.valueOf(nrg)));
    // if (proposteAccelerateList.size() > 0) {
    // ProposteAccelerate proposteAccelerate = proposteAccelerateList.get(0);
    // proposteAccelerate.setVisibleDeposito(true);
    // em.merge(proposteAccelerate);
    // }
    // }
    //
    // public boolean hasPropostaAccelerataAccettata(Long nrg) {
    // class SearchParams extends CriteriaParametersBackend<ProposteAccelerate> {
    //
    // @CriteriaParametersBackend.SearchParameter(property = "nrg", operator =
    // CriteriaParametersBackend.Operator.EQUAL)
    // String nrg;
    //
    // @CriteriaParametersBackend.SearchParameter(property = "dataAccettazione",
    // operator = Operator.NOTNULL)
    // Boolean dataAccettazione;
    //
    // public SearchParams(String nrg) {
    // this.nrg = nrg;
    // this.dataAccettazione = true;
    // }
    // }
    // CriteriaBuilderHelper<ProposteAccelerate> criteriaBuilder = new
    // CriteriaBuilderHelper<>(ProposteAccelerate.class);
    // List<ProposteAccelerate> proposteAccelerateList =
    // criteriaBuilder.resultList(em, new SearchParams(String.valueOf(nrg)));
    // return proposteAccelerateList.size() > 0;
    // }
    //
    // public void rinunciaPropostaAccelerata(Long nrg) {
    // class SearchParams extends CriteriaParametersBackend<ProposteAccelerate> {
    //
    // @CriteriaParametersBackend.SearchParameter(property = "nrg", operator =
    // CriteriaParametersBackend.Operator.EQUAL)
    // String nrg;
    //
    // public SearchParams(String nrg) {
    // this.nrg = nrg;
    // }
    // }
    // CriteriaBuilderHelper<ProposteAccelerate> criteriaBuilder = new
    // CriteriaBuilderHelper<>(ProposteAccelerate.class);
    // List<ProposteAccelerate> proposteAccelerate = criteriaBuilder.resultList(em,
    // new SearchParams(String.valueOf(nrg)));
    // if (proposteAccelerate.size() > 0) {
    // ProposteAccelerate propostaAccelerata = proposteAccelerate.get(0);
    // // propostaAccelerata.setRinuncia(true);
    // em.merge(propostaAccelerata);
    // }
    // }
    //
    // public void accettaPropostaAccelerata(Long nrg, boolean telematica, String
    // dataDeposito, String depositanteId, Boolean sospensione) {
    // Date adesso = new Date();
    // class SearchParams extends CriteriaParametersBackend<ProposteAccelerate> {
    //
    // @CriteriaParametersBackend.SearchParameter(property = "nrg", operator =
    // CriteriaParametersBackend.Operator.EQUAL)
    // String nrg;
    //
    // public SearchParams(String nrg) {
    // this.nrg = nrg;
    // }
    // }
    // CriteriaBuilderHelper<ProposteAccelerate> criteriaBuilder = new
    // CriteriaBuilderHelper<>(ProposteAccelerate.class);
    // List<ProposteAccelerate> proposteAccelerateList =
    // criteriaBuilder.resultList(em, new SearchParams(String.valueOf(nrg)));
    // if (proposteAccelerateList.size() > 0) {
    // ProposteAccelerate proposteAccelerate = proposteAccelerateList.get(0);
    // proposteAccelerate.setDataAccettazione(new Date());
    // if (!telematica) {
    // proposteAccelerate.setDataDeposito(dataDeposito != null ? new
    // Date(Long.parseLong(dataDeposito)) : new Date());
    // proposteAccelerate.setSospensione(sospensione);
    // proposteAccelerate.setIdDepositante(depositanteId);
    // }
    // em.merge(proposteAccelerate);
    // } else if (!telematica) {
    // ProposteAccelerate proposteAccelerate = new ProposteAccelerate();
    // proposteAccelerate.setNrg(nrg);
    // proposteAccelerate.setDataAccettazione(dataDeposito != null ? new
    // Date(Long.parseLong(dataDeposito)) : new Date());
    // proposteAccelerate.setVisibleDeposito(true);
    // proposteAccelerate.setDataDeposito(dataDeposito != null ? new
    // Date(Long.parseLong(dataDeposito)) : new Date());
    // proposteAccelerate.setSospensione(sospensione);
    // proposteAccelerate.setIdDepositante(depositanteId);
    // em.persist(proposteAccelerate);
    // }
    // }
    //
    // public void createStoriaPropostaAccelerata(Utente operatore, Long nrg, String
    // note, BigDecimal idSezione) {
    // Date adesso = new Date();
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(new Long(705));
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(adesso);
    // storiaSic.setTipoRec(120);
    // storiaSic.setUffInvio(idSezione.longValue());
    // storiaSic.setUffArrivo(idSezione.longValue());
    // storiaSic.setNote(note);
    // em.persist(storiaSic);
    // }
    //
    // public void createStoriaNotificaPropostaDefinizioneAnticipata(Utente
    // operatore, Long nrg, String note, BigDecimal idSezione,
    // Date dataInvio) {
    // Date adesso = new Date();
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(new Long(705));
    // storiaSic.setOggi(dataInvio);
    // storiaSic.setDataInizio(adesso);
    // storiaSic.setTipoRec(124);
    // storiaSic.setUffInvio(idSezione.longValue());
    // storiaSic.setUffArrivo(idSezione.longValue());
    // storiaSic.setNote(note);
    // em.persist(storiaSic);
    // }
    //
    // public void createNotificaCsc(String nrg, String oggetto, String testo, Date
    // dataInvio, String mittente,
    // Comunicazione.TipoComunicazione tipo, String idAtto, String destinatario) {
    // NotificaCsc notificaCsc = new NotificaCsc();
    // notificaCsc.setNrg(nrg);
    // notificaCsc.setOggetto(oggetto);
    // notificaCsc.setTesto(testo);
    // notificaCsc.setDataInvio(dataInvio);
    // notificaCsc.setMittente(mittente);
    // notificaCsc.setTipo(tipo);
    // notificaCsc.setIdAllegato(idAtto);
    // notificaCsc.setDestinatario(destinatario);
    // em.persist(notificaCsc);
    // }
    //
    // public void createStoriaVerbaleUdienza(Utente operatore, Long nrg, String
    // note, BigDecimal idSezione) {
    // Date adesso = new Date();
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(new Long(705));
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(adesso);
    // storiaSic.setTipoRec(123);
    // storiaSic.setUffInvio(idSezione.longValue());
    // storiaSic.setUffArrivo(idSezione.longValue());
    // storiaSic.setNote(note);
    // em.persist(storiaSic);
    // }
    //
    // public void createStoriaDecreto(Utente operatore, Long nrg, Long idRecord,
    // String note, Long idSezione) {
    // Date adesso = new Date();
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(32L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(adesso);
    // storiaSic.setTipoRec(13);
    // storiaSic.setUffInvio(idSezione);
    // storiaSic.setUffArrivo(idSezione);
    // storiaSic.setIdRecord(idRecord);
    // storiaSic.setNote(note);
    // em.persist(storiaSic);
    // }
    //
    // public void createStoriaDecretoEstinzione(Utente operatore, Long nrg, Long
    // idRecord, String note, Long idSezione) {
    // Date adesso = new Date();
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(32L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(adesso);
    // storiaSic.setTipoRec(13);
    // storiaSic.setUffInvio(idSezione);
    // storiaSic.setUffArrivo(idSezione);
    // storiaSic.setIdRecord(idRecord);
    // storiaSic.setNote(note);
    // em.persist(storiaSic);
    // }
    //
    // public void createStoriaDecretoVariazioneMateria(Utente operatore, Long nrg,
    // Long idRecord, Long idSezione) {
    // Date adesso = new Date();
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(32L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(adesso);
    // storiaSic.setTipoRec(103);
    // storiaSic.setUffInvio(idSezione);
    // storiaSic.setUffArrivo(idSezione);
    // storiaSic.setIdRecord(idRecord);
    // em.persist(storiaSic);
    // }
    //
    // public void accettaNotificaProposta380(Long nrg, String dataInvio, String
    // dataConsegna, Long ricorrenteId)
    // throws ParseException, ParseException {
    // Date adesso = new Date();
    // class SearchParams extends CriteriaParametersBackend<ProposteAccelerate> {
    //
    // @CriteriaParametersBackend.SearchParameter(property = "nrg", operator =
    // CriteriaParametersBackend.Operator.EQUAL)
    // String nrg;
    //
    // public SearchParams(String nrg) {
    // this.nrg = nrg;
    // }
    // }
    // CriteriaBuilderHelper<ProposteAccelerate> criteriaBuilder = new
    // CriteriaBuilderHelper<>(ProposteAccelerate.class);
    // List<ProposteAccelerate> proposteAccelerateList =
    // criteriaBuilder.resultList(em, new SearchParams(String.valueOf(nrg)));
    // if (proposteAccelerateList.size() > 0) {
    // SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    // long timeDataInvio = dateFormat.parse(dataInvio).getTime();
    // long timeDataConsegna = dateFormat.parse(dataConsegna).getTime();
    // ProposteAccelerate proposteAccelerate = proposteAccelerateList.get(0);
    // proposteAccelerate.setDataInviaNot(dataInvio != null ? new
    // Date(timeDataInvio) : new Date());
    // proposteAccelerate.setVisibleDeposito(true);
    // proposteAccelerate.setDataRdac(dataConsegna != null ? new
    // Date(timeDataConsegna) : new Date());
    // proposteAccelerate.setIdParteNotifica(ricorrenteId);
    // em.merge(proposteAccelerate);
    // }
    // }
    //
    // public void
    // createStoriaRichiestaConclusioniScritteAlProcuratoreGenerale(Utente
    // operatore, Long nrg, Long idRecord, Long idSezione) {
    // Date adesso = new Date();
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(32L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(adesso);
    // storiaSic.setTipoRec(107);
    // storiaSic.setUffInvio(idSezione);
    // storiaSic.setUffArrivo(idSezione);
    // storiaSic.setIdRecord(idRecord);
    // em.persist(storiaSic);
    // }
    //
    // public void createStoriaDecretoFissazioneUdienzaRelatore(Utente operatore,
    // Long nrg, Long idRecord, Long idMagis, Date dataUdienza,
    // Long idSezione, Date dataDeposito) {
    // Date adesso = new Date();
    //
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(293L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(dataDeposito);
    // storiaSic.setTipoRec(101);
    // storiaSic.setUffInvio(idSezione);
    // storiaSic.setUffArrivo(idSezione);
    // storiaSic.setIdRecord(idRecord);
    // storiaSic.setIdMagis(idMagis);
    // em.persist(storiaSic);
    //
    // StoriaSic storiaSic2 = new StoriaSic();
    // storiaSic2.setOperatore(operatore.getIdUtente());
    // storiaSic2.setNrg(nrg);
    // storiaSic2.setIdFunzione(293L);
    // storiaSic2.setOggi(adesso);
    // storiaSic2.setDataInizio(dataUdienza);
    // storiaSic2.setTipoRec(100);
    // storiaSic2.setUffInvio(idSezione);
    // storiaSic2.setUffArrivo(idSezione);
    // storiaSic2.setIdRecord(idRecord);
    // storiaSic2.setIdMagis(idMagis);
    // em.persist(storiaSic2);
    //
    // StoriaSic storiaSic3 = new StoriaSic();
    // storiaSic3.setOperatore(operatore.getIdUtente());
    // storiaSic3.setNrg(nrg);
    // storiaSic3.setIdFunzione(293L);
    // storiaSic3.setOggi(adesso);
    // storiaSic3.setDataInizio(dataUdienza);
    // storiaSic3.setTipoRec(9);
    // storiaSic3.setUffInvio(idSezione);
    // storiaSic3.setUffArrivo(idSezione);
    // storiaSic3.setIdRecord(idRecord);
    // storiaSic3.setIdMagis(idMagis);
    // em.persist(storiaSic3);
    // }
    //
    // public String getCfAutoreProvvedimentoRicorso(Long idCat) {
    // try {
    // Query query = em.createNativeQuery("select t.cf_utente from
    // DESK_PROVVLAVORAZIONE t where t.fk_idcat = " + idCat);
    //
    // return (String) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public void createStoriaAssegnazioneRelatore(Utente operatore, Long nrg, Long
    // idMagis, Long idSezione) {
    // Date adesso = new Date();
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(291L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(adesso);
    // storiaSic.setTipoRec(4);
    // storiaSic.setUffInvio(idSezione);
    // storiaSic.setUffArrivo(idSezione);
    // storiaSic.setIdMagis(idMagis);
    // em.persist(storiaSic);
    // }
    //
    // public void createStoriaDecretoAssegnazioneAConsigliere(Utente operatore,
    // Long nrg, Long idMagis, Long idSezione, Date dataDeposito)
    // {
    // Date adesso = new Date();
    //
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(291L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(adesso);
    // storiaSic.setTipoRec(112);
    // storiaSic.setUffInvio(idSezione);
    // storiaSic.setUffArrivo(idSezione);
    // em.persist(storiaSic);
    //
    // StoriaSic storiaSic2 = new StoriaSic();
    // storiaSic2.setOperatore(operatore.getIdUtente());
    // storiaSic2.setNrg(nrg);
    // storiaSic2.setIdFunzione(291L);
    // storiaSic2.setOggi(adesso);
    // storiaSic2.setDataInizio(adesso);
    // storiaSic2.setTipoRec(4);
    // storiaSic2.setUffInvio(idSezione);
    // storiaSic2.setUffArrivo(idSezione);
    // storiaSic2.setIdMagis(idMagis);
    // em.persist(storiaSic2);
    // }
    //
    // public void createStoriaDecretoAssegnazioneCoordinatoreConsigliere(Utente
    // operatore, Long nrg, Long idMagis, Long idSezione,
    // Date dataDeposito) {
    // Date adesso = new Date();
    //
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(291L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(adesso);
    // storiaSic.setTipoRec(116);
    // storiaSic.setUffInvio(idSezione);
    // storiaSic.setUffArrivo(idSezione);
    // em.persist(storiaSic);
    //
    // StoriaSic storiaSic2 = new StoriaSic();
    // storiaSic2.setOperatore(operatore.getIdUtente());
    // storiaSic2.setNrg(nrg);
    // storiaSic2.setIdFunzione(291L);
    // storiaSic2.setOggi(adesso);
    // storiaSic2.setDataInizio(adesso);
    // storiaSic2.setTipoRec(4);
    // storiaSic2.setUffInvio(idSezione);
    // storiaSic2.setUffArrivo(idSezione);
    // storiaSic2.setIdMagis(idMagis);
    // em.persist(storiaSic2);
    // }
    //
    // public void
    // createStoriaDecretoRiassegnazioneInUdienzaAdAltroConsigliere(Utente
    // operatore, Long nrg, Long idRecord, Long idMagis,
    // Date dataUdienza, Long idSezione, Date dataDeposito) {
    // Date adesso = new Date();
    //
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(291L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(dataDeposito);
    // storiaSic.setTipoRec(115);
    // storiaSic.setUffInvio(idSezione);
    // storiaSic.setUffArrivo(idSezione);
    // storiaSic.setIdRecord(idRecord);
    // em.persist(storiaSic);
    //
    // StoriaSic storiaSic2 = new StoriaSic();
    // storiaSic2.setOperatore(operatore.getIdUtente());
    // storiaSic2.setNrg(nrg);
    // storiaSic2.setIdFunzione(291L);
    // storiaSic2.setOggi(adesso);
    // storiaSic2.setDataInizio(dataUdienza);
    // storiaSic2.setTipoRec(114);
    // storiaSic2.setUffInvio(idSezione);
    // storiaSic2.setUffArrivo(idSezione);
    // storiaSic2.setIdRecord(idRecord);
    // storiaSic2.setIdMagis(idMagis);
    // em.persist(storiaSic2);
    // }
    //
    // public void
    // createStoriaDecretoRiassegnazioneAdAltroConsigliereFuoriUdienza(Utente
    // operatore, Long nrg, Long idMagis, Long
    // idMagisOld,
    // Long idSezione, Date dataDeposito) {
    // Date adesso = new Date();
    // StoriaSic storiaSicToend = getRelatoreFuoriUdienzaNonTrasmesso(nrg,
    // idMagisOld);
    // if (storiaSicToend != null) {
    //
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(291L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(dataDeposito);
    // storiaSic.setTipoRec(115);
    // storiaSic.setUffInvio(idSezione);
    // storiaSic.setUffArrivo(idSezione);
    // em.persist(storiaSic);
    //
    // storiaSicToend.setDataFine(adesso);
    // em.persist(storiaSicToend);
    //
    // StoriaSic storiaSic3 = new StoriaSic();
    // storiaSic3.setOperatore(operatore.getIdUtente());
    // storiaSic3.setNrg(nrg);
    // storiaSic3.setIdFunzione(291L);
    // storiaSic3.setOggi(adesso);
    // storiaSic3.setDataInizio(adesso);
    // storiaSic3.setTipoRec(114);
    // storiaSic3.setUffInvio(idSezione);
    // storiaSic3.setUffArrivo(idSezione);
    // em.persist(storiaSic3);
    //
    // StoriaSic storiaSic2 = new StoriaSic();
    // storiaSic2.setOperatore(operatore.getIdUtente());
    // storiaSic2.setNrg(nrg);
    // storiaSic2.setIdFunzione(291L);
    // storiaSic2.setOggi(adesso);
    // storiaSic2.setDataInizio(adesso);
    // storiaSic2.setTipoRec(17);
    // storiaSic2.setUffInvio(idSezione);
    // storiaSic2.setUffArrivo(idSezione);
    // storiaSic2.setIdMagis(storiaSicToend.getIdMagis());
    // em.persist(storiaSic2);
    //
    // StoriaSic storiaSic4 = new StoriaSic();
    // storiaSic4.setOperatore(operatore.getIdUtente());
    // storiaSic4.setNrg(nrg);
    // storiaSic4.setIdFunzione(291L);
    // storiaSic4.setOggi(adesso);
    // storiaSic4.setDataInizio(adesso);
    // storiaSic4.setTipoRec(4);
    // storiaSic4.setUffInvio(idSezione);
    // storiaSic4.setUffArrivo(idSezione);
    // storiaSic4.setIdMagis(idMagis);
    // em.persist(storiaSic4);
    // } else {
    // storiaSicToend = getRelatoreFuoriUdienzaTrasmesso(nrg, idMagisOld);
    // if (storiaSicToend != null) {
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(292L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(dataDeposito);
    // storiaSic.setTipoRec(115);
    // storiaSic.setUffInvio(idSezione);
    // storiaSic.setUffArrivo(idSezione);
    // em.persist(storiaSic);
    //
    // storiaSicToend.setDataFine(adesso);
    // em.persist(storiaSicToend);
    //
    // StoriaSic storiaSic2 = new StoriaSic();
    // storiaSic2.setOperatore(operatore.getIdUtente());
    // storiaSic2.setNrg(nrg);
    // storiaSic2.setIdFunzione(292L);
    // storiaSic2.setOggi(adesso);
    // storiaSic2.setDataInizio(adesso);
    // storiaSic2.setTipoRec(18);
    // storiaSic2.setUffInvio(idSezione);
    // storiaSic2.setUffArrivo(idSezione);
    // storiaSic2.setIdMagis(storiaSicToend.getIdMagis());
    // em.persist(storiaSic2);
    //
    // StoriaSic storiaSic3 = new StoriaSic();
    // storiaSic3.setOperatore(operatore.getIdUtente());
    // storiaSic3.setNrg(nrg);
    // storiaSic3.setIdFunzione(292L);
    // storiaSic3.setOggi(adesso);
    // storiaSic3.setDataInizio(adesso);
    // storiaSic3.setTipoRec(114);
    // storiaSic3.setUffInvio(idSezione);
    // storiaSic3.setUffArrivo(idSezione);
    // em.persist(storiaSic3);
    //
    // StoriaSic storiaSic4 = new StoriaSic();
    // storiaSic4.setOperatore(operatore.getIdUtente());
    // storiaSic4.setNrg(nrg);
    // storiaSic4.setIdFunzione(292L);
    // storiaSic4.setOggi(adesso);
    // storiaSic4.setDataInizio(adesso);
    // storiaSic4.setTipoRec(4);
    // storiaSic4.setUffInvio(idSezione);
    // storiaSic4.setUffArrivo(idSezione);
    // storiaSic4.setIdMagis(idMagis);
    // em.persist(storiaSic4);
    // }
    // }
    //
    // }
    //
    // public void createStoriaDecretoFissazioneAdunanza(Utente operatore, Long nrg,
    // Long idRecord, Long idMagis, Long idRelatore,
    // Date dataUdienza, Long idSezione, Date dataDeposito) {
    // Date adesso = new Date();
    //
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(293L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(dataDeposito);
    // storiaSic.setTipoRec(29);
    // storiaSic.setUffInvio(idSezione);
    // storiaSic.setUffArrivo(idSezione);
    // storiaSic.setIdRecord(idRecord);
    // storiaSic.setIdMagis(idMagis);
    // em.persist(storiaSic);
    //
    // adesso = new Date();
    // StoriaSic storiaSic2 = new StoriaSic();
    // storiaSic2.setOperatore(operatore.getIdUtente());
    // storiaSic2.setNrg(nrg);
    // storiaSic2.setIdFunzione(293L);
    // storiaSic2.setOggi(adesso);
    // storiaSic2.setDataInizio(new Date());
    // storiaSic2.setTipoRec(22);
    // storiaSic2.setUffInvio(idSezione);
    // storiaSic2.setUffArrivo(idSezione);
    // storiaSic2.setIdRecord(idRecord);
    // storiaSic2.setIdMagis(idRelatore);
    // em.persist(storiaSic2);
    //
    // adesso = new Date();
    // StoriaSic storiaSic3 = new StoriaSic();
    // storiaSic3.setOperatore(operatore.getIdUtente());
    // storiaSic3.setNrg(nrg);
    // storiaSic3.setIdFunzione(293L);
    // storiaSic3.setOggi(adesso);
    // storiaSic3.setDataInizio(dataUdienza);
    // storiaSic3.setTipoRec(9);
    // storiaSic3.setUffInvio(idSezione);
    // storiaSic3.setUffArrivo(idSezione);
    // storiaSic3.setIdRecord(idRecord);
    // storiaSic3.setIdMagis(idRelatore);
    // em.persist(storiaSic3);
    // }
    //
    // public void statoAccettatoProposta(Long nrg) throws Exception {
    // Query query = em.createNativeQuery("update cscbackend_proposte set
    // stato='ADUNANZA_ACCETTATA' where nrg=" + nrg);
    // query.executeUpdate();
    // }
    //
    public void statoRifiutatoProposta(Long nrg, String tipo) throws Exception {
        // Query query = em.createNativeQuery("update cscbackend_proposte set stato='" +
        // tipo + "' where nrg=" + nrg);
        // query.executeUpdate();
    }
    //
    // public void createStoriaDecretoIntegrazioneContraddittorio(Utente operatore,
    // Long nrg, Long idRecord, Long idMagis, Date dataTermine,
    // Long idSezione, Date dataDeposito) {
    // Date adesso = new Date();
    //
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(32L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(dataDeposito);
    // storiaSic.setTipoRec(105);
    // storiaSic.setUffInvio(idSezione);
    // storiaSic.setUffArrivo(idSezione);
    // storiaSic.setIdRecord(idRecord);
    // storiaSic.setIdMagis(idMagis);
    // em.persist(storiaSic);
    //
    // adesso = new Date();
    // StoriaSic storiaSic2 = new StoriaSic();
    // storiaSic2.setOperatore(operatore.getIdUtente());
    // storiaSic2.setNrg(nrg);
    // storiaSic2.setIdFunzione(32L);
    // storiaSic2.setOggi(adesso);
    // storiaSic2.setDataInizio(dataTermine);
    // storiaSic2.setTipoRec(104);
    // storiaSic2.setUffInvio(idSezione);
    // storiaSic2.setUffArrivo(idSezione);
    // storiaSic2.setIdRecord(idRecord);
    // storiaSic2.setIdMagis(idMagis);
    // em.persist(storiaSic2);
    // }
    //
    // public void createStoriaDecretoInvioAttiAdAltraSottosezione(Utente operatore,
    // Long nrg, long idSottosezioneOrigine,
    // long idSottosezioneDestinazione, Date dataDeposito) {
    // Date adesso = new Date();
    //
    // insertDatafine(nrg, adesso);
    //
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(32L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(dataDeposito);
    // storiaSic.setTipoRec(109);
    // storiaSic.setUffInvio(idSottosezioneOrigine);
    // storiaSic.setUffArrivo(idSottosezioneDestinazione);
    // em.persist(storiaSic);
    //
    // adesso = new Date();
    // StoriaSic storiaSic2 = new StoriaSic();
    // storiaSic2.setOperatore(operatore.getIdUtente());
    // storiaSic2.setNrg(nrg);
    // storiaSic2.setIdFunzione(28L);
    // storiaSic2.setOggi(adesso);
    // storiaSic2.setDataInizio(adesso);
    // storiaSic2.setTipoRec(2);
    // storiaSic2.setUffInvio(idSottosezioneOrigine);
    // storiaSic2.setUffArrivo(idSottosezioneDestinazione);
    // em.persist(storiaSic2);
    // }
    //
    // public void createStoriaDecretoInvioAttiASezioneOrdinaria(Utente operatore,
    // Long nrg, long idSezioneOrigine, long
    // idSezioneDestinazione,
    // Date dataDeposito) {
    // Date adesso = new Date();
    //
    // insertDatafine(nrg, adesso);
    //
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(32L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(dataDeposito);
    // storiaSic.setTipoRec(111);
    // storiaSic.setUffInvio(idSezioneOrigine);
    // storiaSic.setUffArrivo(idSezioneDestinazione);
    // em.persist(storiaSic);
    //
    // adesso = new Date();
    // StoriaSic storiaSic2 = new StoriaSic();
    // storiaSic2.setOperatore(operatore.getIdUtente());
    // storiaSic2.setNrg(nrg);
    // storiaSic2.setIdFunzione(28L);
    // storiaSic2.setOggi(adesso);
    // storiaSic2.setDataInizio(adesso);
    // storiaSic2.setTipoRec(2);
    // storiaSic2.setUffInvio(idSezioneOrigine);
    // storiaSic2.setUffArrivo(idSezioneDestinazione);
    // em.persist(storiaSic2);
    // }
    //
    // public void createStoriaDecretoInvioAttiAdAltraSezioneOrdinaria(Utente
    // operatore, Long nrg, long idSezioneOrigine,
    // long idSezioneDestinazione, Date dataDeposito) {
    // Date adesso = new Date();
    //
    // insertDatafine(nrg, adesso);
    //
    // StoriaSic storiaSic = new StoriaSic();
    // storiaSic.setOperatore(operatore.getIdUtente());
    // storiaSic.setNrg(nrg);
    // storiaSic.setIdFunzione(32L);
    // storiaSic.setOggi(adesso);
    // storiaSic.setDataInizio(dataDeposito);
    // storiaSic.setTipoRec(111);
    // storiaSic.setUffInvio(idSezioneOrigine);
    // storiaSic.setUffArrivo(idSezioneDestinazione);
    // em.persist(storiaSic);
    //
    // adesso = new Date();
    // StoriaSic storiaSic2 = new StoriaSic();
    // storiaSic2.setOperatore(operatore.getIdUtente());
    // storiaSic2.setNrg(nrg);
    // storiaSic2.setIdFunzione(28L);
    // storiaSic2.setOggi(adesso);
    // storiaSic2.setDataInizio(adesso);
    // storiaSic2.setTipoRec(2);
    // storiaSic2.setUffInvio(idSezioneOrigine);
    // storiaSic2.setUffArrivo(idSezioneDestinazione);
    // em.persist(storiaSic2);
    // }
    //
    // private void insertDatafine(Long nrg, Date adesso) {
    // List<StoriaSic> ultimiPassaggi = em
    // .createNativeQuery("select * from civile_t_storia s where s.nrg ='" + nrg +
    // "' and s.id_funzione=28 and s.datafine is null",
    // StoriaSic.class)
    // .getResultList();
    // if (!ultimiPassaggi.isEmpty()) {
    // StoriaSic ultimoPassaggio = ultimiPassaggi.get(0);
    // ultimoPassaggio.setDataFine(adesso);
    // em.merge(ultimoPassaggio);
    // }
    // }
    //
    // public BigDecimal getIdUdienza(Date dataUdienza, String tipoUdienza, String
    // aula, String sezione) {
    // Calendar cal = Calendar.getInstance();
    // cal.setTime(dataUdienza);
    // int year = cal.get(Calendar.YEAR);
    // int month = cal.get(Calendar.MONTH) + 1;
    // int day = cal.get(Calendar.DAY_OF_MONTH);
    // String aulaIsNull = aula != null ? ("='" + aula + "'") : "is null";
    // Query query = em
    // .createNativeQuery("select MAX(id_udien) from PENALE_T_UDIENZA t " + "join
    // PENALE_PARAM ctu on t.tipoud=ctu.id_param "
    // + "left join PENALE_PARAM cta on t.aula=cta.id_param " + "join PENALE_PARAM
    // cts on t.sezione=cts.id_param "
    // + "where " + "t.dataud=TO_DATE('" + year + "-" + month + "-" + day + "',
    // 'yyyy-mm-dd') " + "and ctu.sigla='"
    // + tipoUdienza + "' and cta.sigla " + aulaIsNull + " and cts.sigla='" +
    // sezione + "'");
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    //
    // return result;
    // }
    //
    // public BigDecimal getIdTipoUdienza(String tipoUdienza) {
    // Query query = em
    // .createNativeQuery("select a.id_param from civile_param a where a.sigla ='" +
    // tipoUdienza + "' and tipotab='TIPOUDIENZA'");
    // return (BigDecimal) query.getSingleResult();
    // }
    //
    // public BigDecimal getIdAula(String aula) {
    // Query query = em.createNativeQuery("select a.id_param from civile_param a
    // where a.sigla ='" + aula + "' and tipotab='AULA'");
    // return (BigDecimal) query.getSingleResult();
    // }
    //
    // public BigDecimal createUdienza(Utente operatore, Date dataUdienza, Long
    // idTipoUdienza, Long idAula, Long idSezione) {
    // Date adesso = new Date();
    // Udienza udienza = new Udienza();
    // udienza.setDataud(dataUdienza);
    // udienza.setTipoud(idTipoUdienza);
    // udienza.setSezione(idSezione);
    // udienza.setAula(idAula);
    // udienza.setId_funzione(42L);
    // udienza.setOperatore(operatore.getIdUtente());
    // udienza.setOggi(adesso);
    // em.persist(udienza);
    // em.flush();
    // return new BigDecimal(udienza.getId_udien());
    //
    // }
    //
    // public BigDecimal getIdUdienzaRicorso(BigDecimal idUdienza, Long nrg) {
    // try {
    // Query query = em.createNativeQuery("select ID_RICUDIEN from PENALE_T_RICUDIEN
    // where ID_UDIEN=" + idUdienza + " AND NRG=" + nrg);
    // return (BigDecimal) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public Long newMaxOrdUdienzaRicorso(Long nrg) {
    // Query query = em.createNativeQuery("select max (NUMORD) from
    // PENALE_T_RICUDIEN where nrg=" + nrg);
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // if (result == null) {
    // return 0L;
    // } else {
    // return ((BigDecimal) query.getSingleResult()).longValue() + 1;
    // }
    // }
    //
    // public BigDecimal upsertConsigliereCollegio(Utente operatore, Long idUdienza,
    // Long idRelatore) {
    // Query query = getEntityManager().createNativeQuery("select max(id_magiscolle)
    // from civile_collegio where id_magis=" + idRelatore
    // + " and id_udien=" + idUdienza + " and tipomag='CON'");
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // if (result != null) {
    // return result;
    // } else {
    // return createConsigliereCollegio(operatore, idUdienza, idRelatore);
    // }
    // }
    //
    // private List<BigDecimal> getPmCollegio(Long idUdienza) {
    // try {
    // Query query = em
    // .createNativeQuery("select ID_MAGISCOLLE from PENALE_COLLEGIO where
    // ID_UDIEN=" + idUdienza + " and TIPOMAG='PM'");
    // return query.getResultList();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // private void deletePmCollegio(Long idMagisColle) {
    // Query query = getEntityManager().createNativeQuery("delete from
    // civile_collegio where id_magiscolle=" + idMagisColle);
    // query.executeUpdate();
    // }
    //
    // public void deletePropostaAccelerata(Long nrg) {
    // Query query = getEntityManager().createNativeQuery("delete from
    // DESK_PROPOSTE_ACCELERATE where nrg=" + nrg);
    // query.executeUpdate();
    // }
    //
    // public BigDecimal createPMCollegio(Long idOperatore, Long idUdienza, Long
    // idPM) {
    // // controllo prima che non ci sia già il pm, se c'è già lo rimuovo e tengo
    // solo quello nuovo
    // for (BigDecimal idMagisColle : getPmCollegio(idUdienza)) {
    // deletePmCollegio(idMagisColle.longValue());
    // }
    // Date adesso = new Date();
    // Collegio collegio = new Collegio();
    // collegio.setId_udien(idUdienza);
    // collegio.setId_magis(idPM);
    // collegio.setGradoMag(1L);
    // collegio.setIn_udienza(Boolean.TRUE);
    // collegio.setTipomag("PM");
    // collegio.setRiserva("0");
    // collegio.setId_funzione(82L);
    // collegio.setOperatore(idOperatore);
    // collegio.setOggi(adesso);
    // em.persist(collegio);
    // em.flush();
    // return new BigDecimal(collegio.getId_magiscolle());
    // }
    //
    // private BigDecimal createConsigliereCollegio(Utente operatore, Long
    // idUdienza, Long idRelatore) {
    // Date adesso = new Date();
    // Collegio collegio = new Collegio();
    // collegio.setId_udien(idUdienza);
    // collegio.setId_magis(idRelatore);
    // collegio.setGradoMag(1L);
    // collegio.setIn_udienza(Boolean.TRUE);
    // collegio.setTipomag("CON");
    // collegio.setRiserva("0");
    // collegio.setId_funzione(293L);
    // collegio.setOperatore(operatore.getIdUtente());
    // collegio.setOggi(adesso);
    // em.persist(collegio);
    // em.flush();
    // return new BigDecimal(collegio.getId_magiscolle());
    // }
    //
    // public BigDecimal getIdConsigliereDecretoFissazione(Long idUdienza, Long
    // idRelatore) {
    // try {
    // Query query = em.createNativeQuery("select ID_MAGISCOLLE from PENALE_COLLEGIO
    // where ID_UDIEN=" + idUdienza + " and ID_MAGIS="
    // + idRelatore + " and TIPOMAG='CON' and IN_UDIENZA=1 and ID_FUNZIONE=293");
    // return (BigDecimal) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public BigDecimal createUdienzaRicorsoDecretoFissazioneUdienzaRelatore(Utente
    // operatore, Long idUdienza, Long nrg, Long idRelatore,
    // Long numord, Long idMagisColle) {
    // Date adesso = new Date();
    // UdienzaRicorso udienzaRicorso = new UdienzaRicorso();
    // udienzaRicorso.setId_udien(idUdienza);
    // udienzaRicorso.setNrg(nrg);
    // udienzaRicorso.setId_relatore(idRelatore);
    // udienzaRicorso.setNumord(numord);
    // udienzaRicorso.setId_magiscolle(idMagisColle);
    // udienzaRicorso.setId_funzione(293L);
    // udienzaRicorso.setDecreto(2L);
    // udienzaRicorso.setOperatore(operatore.getIdUtente());
    // udienzaRicorso.setOggi(adesso);
    // em.persist(udienzaRicorso);
    // em.flush();
    // return new BigDecimal(udienzaRicorso.getId_ricudien());
    //
    // }
    //
    // public BigDecimal createUdienzaRicorsoDecretoFissazioneAdunanza(Utente
    // operatore, Long idUdienza, Long nrg, Long idRelatore,
    // Long numord, Long idMagisColle) {
    // Date adesso = new Date();
    // UdienzaRicorso udienzaRicorso = new UdienzaRicorso();
    // udienzaRicorso.setId_udien(idUdienza);
    // udienzaRicorso.setNrg(nrg);
    // udienzaRicorso.setId_relatore(idRelatore);
    // udienzaRicorso.setNumord(numord);
    // udienzaRicorso.setId_magiscolle(idMagisColle);
    // udienzaRicorso.setId_funzione(293L);
    // udienzaRicorso.setDecreto(2L);
    // udienzaRicorso.setOperatore(operatore.getIdUtente());
    // udienzaRicorso.setOggi(adesso);
    // em.persist(udienzaRicorso);
    // em.flush();
    // return new BigDecimal(udienzaRicorso.getId_ricudien());
    //
    // }
    //
    // public BigDecimal getIdMagis(String codicefiscale, BigDecimal idSezione) {
    // try {
    // Query query = em.createNativeQuery(
    // "select t.id_magis from PENALE_T_MAGIS t where t.id_anagmagis in(select
    // ID_ANAGMAGIS from PENALE_ANAGMAGIS where
    // upper(CODICE_FISCALE) = '"
    // + codicefiscale.toUpperCase() + "' and (datafine is null or sysdate<datafine)
    // ) and t.ufficio="
    // + idSezione.toString());
    //
    // return (BigDecimal) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public BigDecimal getIdRelatoreRicorsoFuoriUdienza(Long nrg) {
    // try {
    // Query query = em.createNativeQuery("select s.id_magis from PENALE_T_STORIA s
    // " + "join PENALE_MAGIS m on m.ID_MAGIS=s.id_magis "
    // + "where s.id_funzione=291 and s.datafine is null and s.id_magis is not null
    // and s.tiporec=4 and s.nrg=" + nrg
    // + " and (m.datafine is null or sysdate<m.datafine)");
    //
    // return (BigDecimal) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public BigDecimal getIdRelatoreRicorsoFuoriUdienzaTrasmesso(Long nrg) {
    // try {
    // Query query = em.createNativeQuery("select s.id_magis from PENALE_T_STORIA s
    // " + "join PENALE_MAGIS m on m.ID_MAGIS=s.id_magis "
    // + "where s.id_funzione=292 and s.datafine is null and s.id_magis is not null
    // and s.tiporec=18 and s.nrg=" + nrg
    // + " and (m.datafine is null or sysdate<m.datafine)");
    //
    // return (BigDecimal) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public BigDecimal getIdCoordinatoreRelatore(String codicefiscale) {
    // try {
    // Query query = em.createNativeQuery(
    // "select t.id_magis from PENALE_T_MAGIS t where t.id_anagmagis in(select
    // ID_ANAGMAGIS from PENALE_ANAGMAGIS where
    // upper(CODICE_FISCALE) = '"
    // + codicefiscale.toUpperCase()
    // + "' and (datafine is null or sysdate<datafine) ) and t.ufficio = (select
    // id_param from PENALE_PARAM cp where sigla='S6' and tipotab
    // = 'SEZIONI')");
    //
    // return (BigDecimal) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public BigDecimal getIdMagisPGFromCF(String codicefiscale) {
    // try {
    // Query query = em.createNativeQuery(
    // "select t.id_magis from PENALE_T_MAGIS t where t.id_anagmagis in(select
    // ID_ANAGMAGIS from PENALE_ANAGMAGIS where
    // upper(CODICE_FISCALE) = '"
    // + codicefiscale.toUpperCase() + "' and (datafine is null or
    // sysdate<datafine)) and t.UFFICIO = 85");
    //
    // return (BigDecimal) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public void updateStatoSesta(Long nrg, StatiFlussoSesta statiFlussoSesta)
    // throws Exception {
    // Query query = em.createNativeQuery("update cscbackend_ricorsi set stato='" +
    // statiFlussoSesta.name() + "' where nrg=" + nrg);
    // query.executeUpdate();
    // }
    //
    // public boolean isRicorsoFlussoSesta(Long nrg) {
    // Query query = getEntityManager()
    // .createNativeQuery("select count (*) from Civile_t_Ricorso r join
    // cscbackend_ricorsi c on c.nrg=r.nrg"
    // + " where r.dovesta in (select ID_PARAM from PENALE_PARAM where tipotab
    // ='SEZIONI' AND ATTO='S6')" + " and r.nrg="
    // + nrg);
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // return result.intValue() == 1;
    // }
    //
    // public boolean isRicorsoInSesta(Long nrg) {
    // Query query = getEntityManager()
    // .createNativeQuery("select count (*) from Civile_t_Ricorso r join
    // cscbackend_ricorsi c on c.nrg=r.nrg"
    // + " where r.dovesta in (select ID_PARAM from PENALE_PARAM where tipotab
    // ='SEZIONI' AND ATTO='S6' AND ATTO=SIGLA)"
    // + " and c.stato is not null and r.nrg=" + nrg);
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // return result.intValue() == 1;
    // }
    //
    // public boolean isRicorsoInSottosezioniSesta(Long nrg) {
    // Query query = getEntityManager()
    // .createNativeQuery("select count (*) from Civile_t_Ricorso r join
    // cscbackend_ricorsi c on c.nrg=r.nrg"
    // + " where r.dovesta in (select ID_PARAM from PENALE_PARAM where tipotab
    // ='SEZIONI' AND ATTO='S6' AND ATTO<>SIGLA)"
    // + " and c.stato is not null and r.nrg=" + nrg);
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // return result.intValue() == 1;
    // }
    //
    // public BigDecimal getIdMateria(String siglaMateria) {
    // try {
    // Query query = em
    // .createNativeQuery("select ID_PARAM from PENALE_PARAM where tipotab='MATERIA'
    // and SIGLA='" + siglaMateria + "'");
    //
    // return (BigDecimal) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public String getDescrizioneMateria(Long idMateria) {
    // try {
    // Query query = em.createNativeQuery(
    // "select SIGLA || ' - ' || DESCRIZIONE || ' - ' || SEZMAT from PENALE_PARAM
    // where tipotab='MATERIA' and ID_PARAM="
    // + idMateria);
    //
    // return (String) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public String getMateriaSpoglio(Long nrg) {
    // try {
    // Query query = em.createNativeQuery("SELECT C.DESCRIZIONE FROM PENALE_RICORSO
    // A,PENALE_CLASS B, PENALE_PARAM C WHERE \n"
    // + "A.NRG = " + nrg + " AND B.NRG(+)=A.NRG AND B.MATERIA=C.ID_PARAM(+)");
    //
    // return (String) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public String getSezioneMateria(String sigla) {
    // try {
    // Query query = em.createNativeQuery("select DESCRIZIONE from PENALE_PARAM
    // where tipotab='SEZIONI' and SIGLA='" + sigla + "'");
    //
    // return (String) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public String getSiglaTipoRicorso(Long idTipoRic) {
    // try {
    // Query query = em.createNativeQuery("select SIGLA from PENALE_PARAM where
    // tipotab='TIPOLOGIA' and ID_PARAM=" + idTipoRic);
    //
    // return (String) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public BigDecimal getIdPresSezione(String sezione) throws CommonRestWarning {
    // try {
    // Query query = em.createNativeQuery("select id_magis from PENALE_MAGIS where
    // ufficio ='" + sezione
    // + "' and FUNZMAG='PT' and (datafine is null or sysdate<datafine)");
    //
    // return (BigDecimal) query.getSingleResult();
    // } catch (Exception e) {
    // throw new CommonRestWarning("Errore nel recupero del presidente della sezione
    // " + sezione, e);
    // }
    // }
    //
    // public BigDecimal getIdCoordinatoreSezione(String sezione) throws
    // CommonRestWarning {
    // try {
    // Query query = em.createNativeQuery("select id_magis from PENALE_MAGIS where
    // ufficio ='" + sezione
    // + "' and FUNZMAG IN ('PD', 'CD') and (datafine is null or
    // sysdate<datafine)");
    //
    // return (BigDecimal) query.getSingleResult();
    // } catch (Exception e) {
    // throw new CommonRestWarning("Errore nel recupero del presidente della sezione
    // " + sezione, e);
    // }
    // }
    //
    // public boolean isPresidenteSesta(String codicefiscale) {
    // Query query = getEntityManager().createNativeQuery(
    // "select count(*) from PENALE_MAGIS m join PENALE_ANAGMAGIS a on
    // a.id_anagmagis=m.ID_ANAGMAGIS where m.ufficio ='S6' "
    // + " and m.FUNZMAG='PT' and (m.datafine is null or sysdate<m.datafine) and
    // upper(a.codice_fiscale)='"
    // + codicefiscale.toUpperCase() + "'");
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // return result.intValue() == 1;
    // }
    //
    // public boolean isCoordinatoreAssegnatarioSesta(String codicefiscale) {
    // Query query = getEntityManager()
    // .createNativeQuery("select count(*) from PENALE_MAGIS m join PENALE_ANAGMAGIS
    // a on a.id_anagmagis=m.ID_ANAGMAGIS "
    // + "where m.FUNZMAG IN ('PD','CD') and m.ufficio in (select p.sigla from
    // PENALE_PARAM p where p.tipotab='SEZIONI' "
    // + "and (m.datafine is null or sysdate<m.datafine) and p.atto='S6') and
    // a.codice_fiscale='"
    // + codicefiscale.toUpperCase() + "'");
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // return result.intValue() == 1;
    // }
    //
    // public boolean isSestaConSottosezioni(String sezioneRicorso) {
    // Query query = getEntityManager().createNativeQuery(
    // "select count(*) from PENALE_PARAM t where tipotab='SEZIONI' and ATTO='S6'
    // and sigla='" + sezioneRicorso + "'");
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // return result.intValue() == 1;
    // }
    //
    // public boolean isSottoSezioneSesta(String sezioneRicorso) {
    // Query query = getEntityManager()
    // .createNativeQuery("select count(*) from PENALE_PARAM t where
    // tipotab='SEZIONI' and ATTO='S6' and sigla<>atto and sigla='"
    // + sezioneRicorso + "'");
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // return result.intValue() == 1;
    // }
    //
    // public void modificaUbicazioneRicorso(Long nrg, Long sezionedestinazione) {
    // Query query = em.createNativeQuery("update civile_t_ricorso set dovesta=" +
    // sezionedestinazione + " where nrg=" + nrg);
    // query.executeUpdate();
    // }
    //
    // public boolean isSezioneOrdinaria(String sezioneRicorso) {
    // Query query = getEntityManager().createNativeQuery(
    // "select count(*) from PENALE_PARAM t where tipotab='SEZIONI' and sigla=atto
    // and sigla='" + sezioneRicorso + "'");
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // return result.intValue() == 1;
    // }
    //
    // public boolean esisteUdienzaRicorso(Long nrg) {
    // Query query = getEntityManager().createNativeQuery(
    // "select count(*) from PENALE_T_UDIENZA u join PENALE_T_RICUDIEN ru on
    // u.id_udien= ru.id_udien and ru.lastudie=1 and u.dataud>=sysdate
    // and ru.nrg="
    // + nrg);
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // return result.intValue() == 1;
    // }
    //
    // public boolean esisteRelatoreUdienzaRicorso(Long nrg) {
    // Query query = getEntityManager().createNativeQuery(
    // "select count(*) from PENALE_T_UDIENZA u join PENALE_T_RICUDIEN ru on
    // u.id_udien= ru.id_udien and ru.lastudie=1 and u.dataud>=sysdate
    // and ru.id_relatore is not null and ru.nrg="
    // + nrg);
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // return result.intValue() == 1;
    // }
    //
    // public boolean esisteRelatoreRicorsoFuoriUdienza(Long nrg) {
    // Query query = getEntityManager()
    // .createNativeQuery("select count(s.id_magis) from PENALE_T_STORIA s " + "join
    // PENALE_MAGIS m on m.ID_MAGIS=s.id_magis "
    // + "where s.id_funzione=291 and s.datafine is null and s.id_magis is not null
    // and s.tiporec=4 and s.nrg=" + nrg
    // + " and (m.datafine is null or sysdate<m.datafine)");
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // return result.intValue() >= 1;
    // }
    //
    // public boolean esisteRelatoreRicorsoFuoriUdienzaTrasmesso(Long nrg) {
    // Query query = getEntityManager()
    // .createNativeQuery("select count(s.id_magis) from PENALE_T_STORIA s " + "join
    // PENALE_MAGIS m on m.ID_MAGIS=s.id_magis "
    // + "where s.id_funzione=292 and s.datafine is null and s.id_magis is not null
    // and s.tiporec=18 and s.nrg=" + nrg
    // + " and (m.datafine is null or sysdate<m.datafine)");
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // return result.intValue() >= 1;
    // }
    //
    // public boolean esisteSpogliatore(Long nrg) {
    // Query query = getEntityManager()
    // .createNativeQuery("select count(spogliatore) from DESK_SPOGLIO d " + "where
    // nrg=" + nrg + " and spogliatore is not null");
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // return result.intValue() == 1;
    // }
    //
    // public boolean esisteRelatoreSezione(String sezione, String codicefiscale) {
    // Query query = getEntityManager()
    // .createNativeQuery("select count(*) from PENALE_T_MAGIS t join PENALE_PARAM p
    // on p.id_param=t.ufficio and p.sigla='"
    // + sezione + "' where t.id_anagmagis in(select ID_ANAGMAGIS from
    // PENALE_ANAGMAGIS where upper(CODICE_FISCALE) ='"
    // + codicefiscale.toUpperCase() + "' and (datafine is null or sysdate<datafine)
    // ) ");
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // return result.intValue() == 1;
    // }
    //
    // public boolean esisteCoordinatoreConsigliere(String codicefiscale) {
    // Query query = getEntityManager().createNativeQuery("select count(*) from
    // PENALE_T_MAGIS m"
    // + " join PENALE_PARAM p1 on p1.id_param = m.tipomag" + " join
    // PENALE_ANAGMAGIS a on m.id_anagmagis = a.id_anagmagis"
    // + " where (m.datafine is null or sysdate < m.datafine)"
    // + " and m.ufficio = (select id_param from PENALE_PARAM cp where sigla = 'S6'
    // and tipotab = 'SEZIONI')"
    // + " and p1.sigla = 'CON'" + " and upper(a.codice_fiscale) = '" +
    // codicefiscale.toUpperCase() + "'");
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // return result.intValue() == 1;
    // }
    //
    // public BigDecimal getIdUdienzaAttiva(Long nrg) {
    // try {
    // Query query = em.createNativeQuery(
    // "select u.ID_UDIEN from PENALE_T_UDIENZA u join PENALE_T_RICUDIEN ru on
    // u.id_udien= ru.id_udien and ru.lastudie=1 and
    // trunc(u.dataud)>=trunc(sysdate) and ru.nrg="
    // + nrg);
    //
    // return (BigDecimal) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public String getTipoUdienzaAttiva(Long nrg) {
    // try {
    // Query query = em.createNativeQuery(
    // "select TIPOUD from PENALE_UDIENZA u join PENALE_T_RICUDIEN ru on u.id_udien=
    // ru.id_udien and ru.lastudie=1 and u.dataud>=sysdate and
    // ru.nrg="
    // + nrg);
    //
    // return (String) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public BigDecimal getIdUdienzaRicorsoAttiva(Long nrg) {
    // try {
    // Query query = em.createNativeQuery(
    // "select ru.ID_RICUDIEN from PENALE_T_UDIENZA u join PENALE_T_RICUDIEN ru on
    // u.id_udien= ru.id_udien and ru.lastudie=1 and
    // u.dataud>=sysdate and ru.nrg="
    // + nrg);
    //
    // return (BigDecimal) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public BigDecimal getLastIdUdienzaRicorso(Long nrg) {
    // try {
    // Query query = em.createNativeQuery(
    // "select ru.ID_RICUDIEN from PENALE_T_UDIENZA u join PENALE_T_RICUDIEN ru on
    // u.id_udien= ru.id_udien and ru.lastudie=1 and ru.nrg="
    // + nrg);
    //
    // return (BigDecimal) query.getSingleResult();
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public void updateConsigliereUdienzaRicorso(Utente operatore, Long
    // idUdienzaRicorso, Long idRelatore, Long idMagisColle,
    // Long idFunzione) {
    // UdienzaRicorso udienzaRicorso = findEntity(UdienzaRicorso.class,
    // idUdienzaRicorso);
    // udienzaRicorso.setId_relatore(idRelatore);
    // udienzaRicorso.setId_magiscolle(idMagisColle);
    // udienzaRicorso.setOperatore(operatore.getIdUtente());
    // udienzaRicorso.setOggi(new Date());
    // udienzaRicorso.setId_funzione(idFunzione);
    // }
    //
    // public void rimuoviConsigliereNonReferenziatoDaCollegio(Utente operatore,
    // Long idUdienza, Long idRelatore) {
    // Query query = getEntityManager().createNativeQuery(
    // "select count(*) from PENALE_T_RICUDIEN ru join civile_collegio c on
    // ru.id_udien=c.id_udien and c.tipomag='CON' and
    // ru.id_relatore=c.id_magis and c.id_magis="
    // + idRelatore + " and c.id_udien=" + idUdienza);
    // BigDecimal result = (BigDecimal) query.getSingleResult();
    // if (result.intValue() == 1) {
    // // unica referenza rimasta...si può togliere il consigliere dal collegio
    // query = getEntityManager().createNativeQuery(
    // "delete from civile_collegio where tipomag='CON' and id_magis=" + idRelatore
    // + " and id_udien=" + idUdienza);
    // query.executeUpdate();
    // }
    // }
    //
    // public String getCfPresidenteSezioneTitolare(Long nrg) {
    // try {
    // Query query = em
    // .createNativeQuery(" select a.codice_fiscale from PENALE_T_RICORSO r " +
    // "join PENALE_T_MAGIS m on r.dovesta=m.ufficio "
    // + "join civile_param p2 on m.funzmag=p2.id_param join civile_anagmagis a on
    // a.id_anagmagis=m.id_anagmagis "
    // + "where p2.SIGLA='PT' and (m.datafine is null or m.datafine>sysdate) and
    // r.nrg=" + +nrg);
    //
    // return (String) query.getSingleResult();
    // } catch (Exception e) {
    // return "UNKNOWN";
    // }
    // }
    //
    // public String getCodiceFiscaleCoordinatoreRicorsoSesta(Long nrg) {
    // Query query = em.createNativeQuery("select a.codice_fiscale from
    // PENALE_T_RICORSO t "
    // + "join PENALE_PARAM p on t.dovesta=p.id_param " + "join Civile_t_Magis m on
    // m.UFFICIO=p.id_param "
    // + "join PENALE_PARAM p2 on p2.id_param=m.funzmag " + "join civile_anagmagis a
    // on a.id_anagmagis=m.id_anagmagis "
    // + "where p.atto='S6' and p.atto<>p.sigla and (m.datafine is null or
    // sysdate<m.datafine) and p2.sigla in('PD','CD') "
    // + "and a.codice_fiscale is not null and t.nrg=" + nrg);
    // try {
    // return (String) query.getSingleResult();
    // } catch (Exception t) {
    // return null;
    // }
    // }
    //
    // public String getCodiceFiscaleSpogliatoreByNrg(Long nrg) {
    // Query query = em.createNativeQuery(
    // "select ca.CODICE_FISCALE from PENALE_ANAGMAGIS ca where ca.ID_ANAGMAGIS in (
    // select d.spogliatore from DESK_SPOGLIO d where d.nrg='"
    // + nrg + "')");
    // try {
    // return (String) query.getSingleResult();
    // } catch (Exception t) {
    // return null;
    // }
    // }
    //
    // public BigDecimal getIdMagisCoordinatoreRicorsoSesta(Long nrg) {
    // Query query = em.createNativeQuery("select m.id_magis from PENALE_T_RICORSO t
    // " + "join PENALE_PARAM p on t.dovesta=p.id_param "
    // + "join Civile_t_Magis m on m.UFFICIO=p.id_param " + "join PENALE_PARAM p2 on
    // p2.id_param=m.funzmag "
    // + "join civile_anagmagis a on a.id_anagmagis=m.id_anagmagis "
    // + "where p.atto='S6' and p.atto<>p.sigla and (m.datafine is null or
    // sysdate<m.datafine) and p2.sigla in('PD','CD') "
    // + "and a.codice_fiscale is not null and t.nrg=" + nrg);
    // try {
    // return (BigDecimal) query.getSingleResult();
    // } catch (Exception t) {
    // return null;
    // }
    // }
    //
    // public BigDecimal getIdRelazione(String tipoRelazione) {
    // Query query = em.createNativeQuery(
    // "select a.id_param from civile_param a where a.sigla ='" + tipoRelazione + "'
    // and tipotab='TIPORELAZIONE'");
    // return (BigDecimal) query.getSingleResult();
    // }
    //
    // public String getCfRelatoreRicorso(Long nrg) {
    // Query query = em.createNativeQuery("select a.CFRELATORE from
    // civile_t_ricorso_cache a where a.nrg =" + nrg);
    // return (String) query.getSingleResult();
    // }
    //
    // public String getNomeRelatoreRicorso(BigDecimal idMagis) {
    // Query query = em.createNativeQuery(
    // "select nome || ' ' || cognome from civile_anagmagis a join civile_t_magis m
    // on m.id_anagmagis = a.id_anagmagis where m.id_magis ="
    // + idMagis);
    // return (String) query.getSingleResult();
    // }
    //
    // public StoriaSic getRelatoreFuoriUdienzaNonTrasmesso(Long nrg, Long
    // idMagisOld) {
    // return findEntitiesByQuery(
    // "FROM StoriaSic WHERE nrg = :nrg and idMagis = :idMagis and idFunzione
    // =:idFunzione and tipoRec=:tipoRec and dataFine is null",
    // StoriaSic.class, getCompiledMap("nrg", nrg, "idMagis", idMagisOld,
    // "idFunzione", 291L, "tipoRec", 4)).get(0);
    // }
    //
    // public boolean existRelatoreFuoriUdienzaNonTrasmesso(Long nrg, Long
    // idMagisOld) {
    // return findEntitiesByQuery(
    // "FROM StoriaSic WHERE nrg = :nrg and idMagis = :idMagis and idFunzione
    // =:idFunzione and tipoRec=:tipoRec and dataFine is null",
    // StoriaSic.class, getCompiledMap("nrg", nrg, "idMagis", idMagisOld,
    // "idFunzione", 291L, "tipoRec", 4)).size() > 0;
    // }
    //
    // public StoriaSic getRelatoreFuoriUdienzaTrasmesso(Long nrg, Long idMagisOld)
    // {
    // return findEntityByQuery(
    // "FROM StoriaSic WHERE nrg = :nrg and idMagis = :idMagis and idFunzione
    // =:idFunzione and tipoRec=:tipoRec and dataFine is null",
    // StoriaSic.class, getCompiledMap("nrg", nrg, "idMagis", idMagisOld,
    // "idFunzione", 292L, "tipoRec", 18));
    // }
    //
    // public RelazioniMagis getRelazioniMagis(Long nrg) {
    // return findEntityByQuery("FROM RelazioniMagis WHERE nrg = :nrg AND
    // id_ricudien IS NULL", RelazioniMagis.class,
    // getCompiledMap("nrg", nrg));
    // }
    //
    // public String getDenominazionePG(String cf) {
    // try {
    // Query query = em.createNativeQuery("select COGNOME || ' ' || NOME from
    // PENALE_ANAGMAGIS where CODICE_FISCALE ='" + cf + "'");
    //
    // return (String) query.getSingleResult();
    // } catch (Exception e) {
    // return "";
    // }
    // }
    //
    // public BigDecimal getIdAttoPadre(Long nrg) {
    // try {
    // Query query = em.createNativeQuery("select MIN(ID_ATTO) FROM PENALE_ATTI
    // WHERE NRG = " + nrg + " AND ID_ANAGATTI = 0");
    // BigDecimal idAttoPadre = (BigDecimal) query.getSingleResult();
    // return idAttoPadre;
    // } catch (Exception e) {
    // return null;
    // }
    // }
    //
    // public Parte getParteRicorso(ParteElaborazioneDeposito parteElabDep, Long
    // nrg) {
    // String codiceFiscale = parteElabDep.getCodiceFiscale();
    // String cognome = parteElabDep.getCognome();
    // String nome = parteElabDep.getNome();
    // Query query = em.createNativeQuery("SELECT a.id_parte"
    // + " FROM civile_parti a, civile_anagparti c, civile_attipartifig apf,
    // civile_param pa,civile_anagatti aa,civile_atti at"
    // + " WHERE a.id_parte=apf.id_parte and pa.id_param=apf.tipofigura AND
    // a.id_anagparte=c.id_anagparte AND a.nrg="
    // + nrg
    // + " AND apf.id_atto=AT.ID_ATTO AND AA.ID_ANAGATTI=At.ID_aNAGATTI AND AA.SIGLA
    // IN ('IR','CI','IS') AND c.CODFISC='"
    // + codiceFiscale + "'");
    // BigDecimal idParte = null;
    // List<BigDecimal> listIdParteCf = (List<BigDecimal>) query.getResultList();
    // if (listIdParteCf == null || listIdParteCf.isEmpty()) {
    // // se c'è un apostrofo usiamo la somiglianza altrimenti va in pappa la query
    // if (nome != null && !"".equals(nome) && !nome.contains("'") &&
    // !cognome.contains("'")) {
    // LOGGER.info("Parte non trovata con il codice fiscale, cerco per nome e
    // cognome");
    // query = em.createNativeQuery("SELECT a.id_parte"
    // + " FROM civile_parti a, civile_anagparti c, civile_attipartifig apf,
    // civile_param pa,civile_anagatti aa,civile_atti at"
    // + " WHERE a.id_parte=apf.id_parte and pa.id_param=apf.tipofigura AND
    // a.id_anagparte=c.id_anagparte AND a.nrg="
    // + nrg
    // + " AND apf.id_atto=AT.ID_ATTO AND AA.ID_ANAGATTI=At.ID_aNAGATTI AND AA.SIGLA
    // IN ('IR','CI','IS') AND c.COGNOME='"
    // + cognome + "'" + " AND c.NOME='" + nome + "'");
    // List<BigDecimal> listIdParte = (List<BigDecimal>) query.getResultList();
    // if (listIdParte == null || listIdParte.isEmpty()) {
    // idParte = cercaSomiglianzaParti(parteElabDep, nrg);
    // } else {
    // for (BigDecimal idP : listIdParte) {
    // if (idParte == null) {
    // idParte = idP;
    // } else {
    // if (idP.longValue() > idParte.longValue()) {
    // idParte = idP;
    // }
    // }
    // }
    // }
    // } else {
    // idParte = cercaSomiglianzaParti(parteElabDep, nrg);
    // }
    // } else {
    // for (BigDecimal idP : listIdParteCf) {
    // if (idParte == null) {
    // idParte = idP;
    // } else {
    // if (idP.longValue() > idParte.longValue()) {
    // idParte = idP;
    // }
    // }
    // }
    // }
    // if (idParte != null) {
    // return em.find(Parte.class, idParte.longValue());
    // }
    // return null;
    // }
    //
    // private BigDecimal cercaSomiglianzaParti(ParteElaborazioneDeposito
    // parteElabDep, Long nrg) {
    // class SearchParams extends
    // CriteriaParametersBackend<it.netservice.sic.model.common.dettaglioricorso.Parte>
    // {
    //
    // @CriteriaParametersBackend.SearchParameter(property = "nrg", operator =
    // CriteriaParametersBackend.Operator.EQUAL)
    // String nrg;
    //
    // public SearchParams(String nrg) {
    // this.nrg = nrg;
    // }
    // }
    //
    // String cognome = parteElabDep.getCognome();
    // String nome = parteElabDep.getNome();
    // BigDecimal idParte = null;
    // LOGGER.info("Parte sporca");
    // CriteriaBuilderHelper<it.netservice.sic.model.common.dettaglioricorso.Parte>
    // criteriaBuilder = new CriteriaBuilderHelper<>(
    // it.netservice.sic.model.common.dettaglioricorso.Parte.class);
    // List<it.netservice.sic.model.common.dettaglioricorso.Parte> parteList =
    // criteriaBuilder.resultList(em,
    // new SearchParams(String.valueOf(nrg)));
    //
    // while (cognome.contains(" ")) {
    // cognome = cognome.replace(" ", " ");
    // }
    // cognome = cognome.trim().replace(".", "");
    //
    // if (nome != null) {
    // while (nome.contains(" ")) {
    // nome = nome.replace(" ", " ");
    // }
    // nome = nome.trim().replace(".", "");
    // }
    //
    // Double similar = 0.0;
    // for (it.netservice.sic.model.common.dettaglioricorso.Parte p : parteList) {
    // if (p.getTipo() != null && !Arrays.asList("RI", "IN",
    // "RE").contains(p.getTipo().getSigla())) {
    // continue;
    // }
    // if (p.getAnagraficaParte().getCognome() != null) {
    // // while (p.getAnagraficaParte().getCognome().contains(" ")) {
    // //
    // p.getAnagraficaParte().setCognome(p.getAnagraficaParte().getCognome().replace("
    // ", " "));
    // // }
    // p.getAnagraficaParte().setCognome(p.getAnagraficaParte().getCognome().trim().replace(".",
    // ""));
    // }
    //
    // if (p.getAnagraficaParte().getNome() != null) {
    // // while (p.getAnagraficaParte().getNome().contains(" ")) {
    // // p.getAnagraficaParte().setNome(p.getAnagraficaParte().getNome().replace("
    // ", " "));
    // // }
    // p.getAnagraficaParte().setNome(p.getAnagraficaParte().getNome().trim().replace(".",
    // ""));
    // }
    // JaroWinklerDistance jaroWinklerDistance = new JaroWinklerDistance();
    // Double newSimilar =
    // jaroWinklerDistance.apply(p.getAnagraficaParte().getCognome(), cognome);
    // // controlliamo che sia parecchio simile il nome (0.65), altrimenti vuol dire
    // che è una parte nuova
    // if (newSimilar > 0.65 && newSimilar > similar) {
    // similar = newSimilar;
    // idParte = BigDecimal.valueOf(p.getId());
    // }
    // String denom = nome != null ? (cognome + " " + nome) : cognome;
    // Double newSimilar2;
    // if ("NA".equals(parteElabDep.getNaturaGiuridica()) && (nome == null ||
    // "".equals(nome))) {
    // newSimilar2 = jaroWinklerDistance.apply(p.getAnagraficaParte().getCognome() +
    // " " + p.getAnagraficaParte().getNome(),
    // cognome);
    // } else {
    // newSimilar2 = jaroWinklerDistance.apply(p.getAnagraficaParte().getCognome(),
    // denom);
    // }
    // if (newSimilar > 0.65 && newSimilar2 > similar) {
    // similar = newSimilar;
    // idParte = BigDecimal.valueOf(p.getId());
    // }
    // }
    // return idParte;
    // }
    //
    // public void updateIdAttoPadre(Long idAtto, Long idAttoPadre) {
    // Query query = em.createNativeQuery("update PENALE_ATTIPARTIFIG set
    // id_attopadre=" + idAttoPadre + " where id_atto=" + idAtto);
    // query.executeUpdate();
    // }
    //
    // public void updateCachePropostaAccelerata(Long nrg) {
    // Query query = em.createNativeQuery("update PENALE_T_RICORSO_CACHE set
    // provvesitospoglio=" + "'PROPOSTA_380_BIS'"
    // + ", bozzaesitospoglio = null " + " where nrg=" + nrg);
    // query.executeUpdate();
    // Query queryEsito = em.createNativeQuery("update DESK_ESITO_SPOGLIO_RIC set
    // depositoeffettuato=1 where nrg=" + nrg);
    // queryEsito.executeUpdate();
    // }
    //
    /*
     * public void updateCacheMinutaRiuniti(Long nrg) { Query query =
     * em.createNativeQuery("update CSCBACKEND_RICORSI set statominuta=" +
     * "'INFIRMAPRESIDENTE'" + " where nrg=" + nrg); query.executeUpdate(); }
     */
    //
    // public void updateCacheProvvDefRiuniti(Long nrg) {
    // Query query = em.createNativeQuery("update CSCBACKEND_RICORSI set
    // statominuta=" + "'PUBBLICATO'" + " where nrg=" + nrg);
    // query.executeUpdate();
    // }
}
