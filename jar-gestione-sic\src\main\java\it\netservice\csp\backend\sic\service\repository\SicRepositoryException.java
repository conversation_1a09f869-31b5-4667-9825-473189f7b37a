package it.netservice.csp.backend.sic.service.repository;

/**
 *
 * Indica un errore nella persistenza dei dati del SIC
 *
 * <AUTHOR>
 */
public class SicRepositoryException extends Exception {

    public SicRepositoryException() {
    }

    public SicRepositoryException(String message) {
        super(message);
    }

    public SicRepositoryException(String message, Throwable cause) {
        super(message, cause);
    }

}
