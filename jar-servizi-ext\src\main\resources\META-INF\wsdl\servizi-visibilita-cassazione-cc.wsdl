<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:xs="http://www.w3.org/2001/XMLSchema"
             xmlns:tns="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"
             xmlns:apachesoap="http://xml.apache.org/xml-soap"
             xmlns:tp="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione/types"
             xmlns:qb="http://www.netserv.it/QBuilder"
             xmlns:yt="http://www.giustizia.it/gl/common/types"
             targetNamespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione">
    <import namespace="http://www.netserv.it/QBuilder" location="qbuilder.wsdl"/>
    <types>
        <xs:schema xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/"
                   xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                   targetNamespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione/types">
            <xs:import namespace="http://schemas.xmlsoap.org/soap/encoding/" schemaLocation="soap-encoding.xsd"/>
            <xs:import
                    namespace="http://www.giustizia.it/gl/common/types"
                    schemaLocation="common-types.xsd"/>
            <xs:complexType name="VistaVisibilitaCassazione">
                <xs:all>
                    <xs:element name="idrichiesta" type="xs:string"/>
                    <xs:element name="idfasc" type="xs:string"/>
                    <xs:element name="idparte" type="xs:string" nillable="true"/>
                    <xs:element name="richiedente" type="xs:string"/>
                    <xs:element name="ruolo" type="xs:string" nillable="true"/>
                    <xs:element name="codiceFiscale" type="xs:string"/>
                    <xs:element name="stato" type="xs:string"/>
                    <xs:element name="numProvv" type="xs:string"/>
                    <xs:element name="naturaProvv" type="xs:string"/>
                    <xs:element name="dataEmissProvv" type="xs:date"/>
                    <xs:element name="dataRichiesta" type="xs:date"/>
                    <xs:element name="dominio" type="xs:string"/>
                    <xs:element name="idrito" type="xs:string"/>
                </xs:all>
            </xs:complexType>

            <xs:complexType name="RichiestaVisibilitaCassazione">
                <xs:all>
                    <xs:element name="idrichiesta" type="xs:string"/>
                    <xs:element name="numpro" type="xs:string"/>
                </xs:all>
            </xs:complexType>

            <xs:complexType name="VisibilitaCassazione">
                <xs:all>
                    <xs:element name="idrichiesta" type="xs:string"/>
                    <xs:element name="nrg" type="xs:long"/>
                    <xs:element name="cartacei" type="xs:boolean"/>
                    <xs:element name="numPro" type="xs:string"/>
                    <xs:element name="materiaCass" type="xs:string"/>
                    <xs:element name="nrgFormatted" type="xs:string"/>
                    <xs:element name="motivoRifiuto" type="xs:string"/>
                    <xs:element name="codiceFiscale" type="xs:string"/>
                    <xs:element name="numProvv" type="xs:string"/>
                    <xs:element name="naturaProvv" type="xs:string"/>
                    <xs:element name="dataEmissProvv" type="xs:date"/>
                    <xs:element name="dataAccettazione" type="xs:date"/>
                    <xs:element name="dataUltimaModifica" type="xs:date"/>
                    <xs:element name="statoRichiesta" type="xs:string"/>
                </xs:all>
            </xs:complexType>

            <xs:complexType name="RicercaRichiesteVisibilitaParameter">
                <xs:all>
                    <xs:element name="dataDa" type="xs:date" nillable="true"/>
                    <xs:element name="dataA" type="xs:date" nillable="true"/>
                    <xs:element name="nrg" type="xs:string" nillable="true"/>
                    <xs:element name="stato" type="xs:string" nillable="true"/>
                </xs:all>
            </xs:complexType>

            <xs:complexType name="VistaVisibilitaCassazioneArray">
                <xs:complexContent>
                    <xs:restriction base="soapenc:Array">
                        <xs:attribute ref="soapenc:arrayType" wsdl:arrayType="tp:VistaVisibilitaCassazione[]"/>
                    </xs:restriction>
                </xs:complexContent>
            </xs:complexType>
        </xs:schema>
    </types>
    <message name="searchRichiesteVisibilitaReq">
        <part name="parameters" type="tp:RicercaRichiesteVisibilitaParameter"/>
    </message>
    <message name="searchRichiesteVisibilitaRes">
        <part name="vistaVisibilitaCassazione" type="tp:VistaVisibilitaCassazioneArray"/>
    </message>
    <message name="richiediVisibilitaReq">
        <part name="numero" type="xs:string"/>
        <part name="anno" type="xs:string"/>
        <part name="sub" type="xs:string"/>
        <part name="subSocio" type="xs:string"/>
        <part name="numeroCCI" type="xs:string"/>
        <part name="registro" type="xs:string"/>
        <part name="ufficio" type="xs:string"/>
        <part name="nrg" type="xs:string"/>
        <part name="nrgFormatted" type="xs:string"/>
        <part name="materiaCass" type="xs:string"/>
        <part name="numProvvImp" type="xs:string"/>
        <part name="naturaProvvImp" type="xs:string"/>
        <part name="dataProvvImp" type="xs:date"/>
        <part name="codiceFiscale" type="xs:string"/>
    </message>
    <message name="richiediVisibilitaRes">
        <part name="result" type="tp:RichiestaVisibilitaCassazione"/>
    </message>
    <message name="statoRichiestaVisibilitaReq">
        <part name="idRichiesta" type="xs:string"/>
        <part name="registro" type="xs:string"/>
        <part name="ufficio" type="xs:string"/>
    </message>
    <message name="statoRichiestaVisibilitaRes">
        <part name="statoRichiesta" type="tp:VisibilitaCassazione"/>
    </message>
    <message name="updateStatoRichiestaVisibilitaReq">
        <part name="idRichiesta" type="xs:string"/>
        <part name="nuovoStato" type="xs:string"/>
        <part name="ufficio" type="xs:string"/>
        <part name="registro" type="xs:string"/>
    </message>
    <message name="updateStatoRichiestaVisibilitaRes">
        <part name="statoRichiesta" type="xs:string"/>
    </message>
    <message name="downloadAttoReq">
        <part name="idAtto" type="xs:string"/>
        <part name="registro" type="xs:string"/>
        <part name="ufficio" type="xs:string"/>
    </message>
    <message name="downloadAttoRes">
        <part name="atto" type="apachesoap:DataHandler"/>
    </message>
    <message name="listaAllegatiReq">
        <part name="idAtto" type="xs:string"/>
        <part name="registro" type="xs:string"/>
        <part name="ufficio" type="xs:string"/>
    </message>
    <message name="listaAllegatiRes">
        <part name="atto" type="yt:ContentInfo"/>
    </message>
    <message name="countRichiesteVisibilitaReq">
        <part name="parameters" type="tp:RicercaRichiesteVisibilitaParameter"/>
    </message>
    <message name="countRichiesteVisibilitaRes">
        <part name="count" type="xs:int"/>
    </message>
    <message name="accettaRichiestaVisibilitaReq">
        <part name="idrichiesta" type="xs:string"/>
        <part name="idufficio" type="xs:string"/>
    </message>
    <message name="accettaRichiestaVisibilitaRes"/>
    <message name="rifiutaRichiestaVisibilitaReq">
        <part name="idrichiesta" type="xs:string"/>
        <part name="motivazione" type="xs:string"/>
        <part name="idufficio" type="xs:string"/>
    </message>
    <message name="rifiutaRichiestaVisibilitaRes"/>
    <portType name="ServiziVisibilitaCassazione">
        <operation name="searchRichiesteVisibilita">
            <input message="tns:searchRichiesteVisibilitaReq"/>
            <output message="tns:searchRichiesteVisibilitaRes"/>
        </operation>
        <operation name="richiediVisibilita">
            <input message="tns:richiediVisibilitaReq"/>
            <output message="tns:richiediVisibilitaRes"/>
        </operation>
        <operation name="updateStatoRichiesta">
            <input message="tns:updateStatoRichiestaVisibilitaReq"/>
            <output message="tns:updateStatoRichiestaVisibilitaRes"/>
        </operation>
        <operation name="statoRichiestaVisibilita">
            <input message="tns:statoRichiestaVisibilitaReq"/>
            <output message="tns:statoRichiestaVisibilitaRes"/>
        </operation>
        <operation name="downloadAtto">
            <input message="tns:downloadAttoReq"/>
            <output message="tns:downloadAttoRes"/>
        </operation>
        <operation name="listaAllegati">
            <input message="tns:listaAllegatiReq"/>
            <output message="tns:listaAllegatiRes"/>
        </operation>
        <operation name="countRichiesteVisibilita">
            <input message="tns:countRichiesteVisibilitaReq"/>
            <output message="tns:countRichiesteVisibilitaRes"/>
        </operation>
        <operation name="accettaRichiestaVisibilita">
            <input message="tns:accettaRichiestaVisibilitaReq"/>
            <output message="tns:accettaRichiestaVisibilitaRes"/>
        </operation>
        <operation name="rifiutaRichiestaVisibilita">
            <input message="tns:rifiutaRichiestaVisibilitaReq"/>
            <output message="tns:rifiutaRichiestaVisibilitaRes"/>
        </operation>
    </portType>
    <binding name="ServiziVisibilitaCassazioneSOAPBinding" type="tns:ServiziVisibilitaCassazione">
        <soap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="richiediVisibilita">
            <input>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </input>
            <output>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </output>
        </operation>
        <operation name="searchRichiesteVisibilita">
            <input>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </input>
            <output>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </output>
        </operation>
        <operation name="statoRichiestaVisibilita">
            <input>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </input>
            <output>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </output>
        </operation>
        <operation name="updateStatoRichiesta">
            <input>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </input>
            <output>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </output>
        </operation>

        <operation name="downloadAtto">
            <input>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </input>
            <output>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </output>
        </operation>
        <operation name="listaAllegati">
            <input>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </input>
            <output>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </output>
        </operation>
        <operation name="accettaRichiestaVisibilita">
            <input>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </input>
            <output>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </output>
        </operation>
        <operation name="rifiutaRichiestaVisibilita">
            <input>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </input>
            <output>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </output>
        </operation>
        <operation name="countRichiesteVisibilita">
            <input>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </input>
            <output>
                <soap:body use="encoded" namespace="http://www.netservice.it/pt/vpnc/ServiziVisibilitaCassazione"/>
            </output>
        </operation>
    </binding>


    <binding name="DetailFascSICCSOAPBinding" type="qb:ServiziQueryBuilder">
        <soap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="execute">
            <soap:operation soapAction="execute"/>
            <input>
                <soap:body use="literal" namespace="urn:CONS-SICC-BE-CASS"/>
            </input>
            <output>
                <soap:body use="literal" namespace="urn:CONS-SICC-BE-CASS"/>
            </output>
        </operation>
        <operation name="getRowClassDescriptor">
            <soap:operation soapAction="getRowClassDescriptor"/>
            <input>
                <soap:body use="literal" namespace="urn:CONS-SICC-BE-CASS"/>
            </input>
            <output>
                <soap:body use="literal" namespace="urn:CONS-SICC-BE-CASS"/>
            </output>
        </operation>
        <operation name="getServiceDescriptor">
            <soap:operation soapAction="getServiceDescriptor"/>
            <input>
                <soap:body use="literal" namespace="urn:CONS-SICC-BE-CASS"/>
            </input>
            <output>
                <soap:body use="literal" namespace="urn:CONS-SICC-BE-CASS"/>
            </output>
        </operation>
        <operation name="getServiceNames">
            <soap:operation soapAction="getServiceNames"/>
            <input>
                <soap:body use="literal" namespace="urn:CONS-SICC-BE-CASS"/>
            </input>
            <output>
                <soap:body use="literal" namespace="urn:CONS-SICC-BE-CASS"/>
            </output>
        </operation>
    </binding>

    <service name="WsServiziDettaglioSICC">
        <port name="DetailFascSICCSOAPPort" binding="tns:DetailFascSICCSOAPBinding">
            <soap:address location="http://localhost:8080/wasp/rpcrouter"/>
        </port>
    </service>

    <service name="WsServiziVisibilitaCassazioneSICC">
        <port name="ServiziVisibilitaCassazioneSOAPPort" binding="tns:ServiziVisibilitaCassazioneSOAPBinding">
            <soap:address location="http://localhost:8080/wasp/rpcrouter"/>
        </port>
    </service>
</definitions>
