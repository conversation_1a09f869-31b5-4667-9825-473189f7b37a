apply plugin: 'ear'
apply plugin: 'netservice'
apply plugin: 'java'

dependencies {
    deploy project(path: ':jar-gestione-sic', configuration: 'archives')
    deploy project(path: ':war-cspbackend', configuration: 'archives')
    deploy project(path: ':jar-elaborazione-depositi', configuration: 'archives')
    deploy project(path: ':jar-csp-common', configuration: 'archives')
    deploy project(path: ':jar-servizi-ext', configuration: 'archives')
    deploy project(path: ':jar-ucu-model', configuration: 'archives')
    deploy project(path: ':jar-gestione-pdf', configuration: 'archives')
    deploy project(path: ':jar-pubblicazione-depositi', configuration: 'archives')

    //earlib group: 'org.springframework', name: 'spring-context', version: '3.0.6.RELEASE'
    earlib group: 'libraries.cassazione.sic-common', name: 'sic-common', version: '2.1.8'
    earlib("libraries.cassazione.sic-model-penale:sic-model-penale:${sicPenaleModelVersion}") {
        exclude group: 'org.hibernate', module: 'hibernate-core'
    }


    /*
     * queste dipendenze ora TUTTE incluse in cassazione.pubblicazione (jboss module)
     * QUANDO SI AGGIUNGE UNA DIPENDENZA :
     * produrre ear decommentando questo blocco
     * prendere tutte le lib e ficcarle in modules (a parte i moduli spring)
     * compilare in modo consistente modules.xml
     * ricommentare questo blocco
     * */

    /*
       earlib group: 'com.google.guava', name: 'guava', version: '28.2-android' // x.x-android è la versione compatibile con java7
       earlib group: 'libraries.adn-client', name: 'adn-client', version: '1.0.0'
       earlib group: 'libraries.common-rest', name: 'common-rest', version: '1.5.4'
       earlib group: 'org.apache.axis', name: 'axis', version: '1.4'
       earlib group: 'org.apache.axis', name: 'axis-jaxrpc-no-qname', version: '1.4'
       earlib group: 'commons-discovery', name: 'commons-discovery', version: '0.2'
       earlib group: 'wsdl4j', name: 'wsdl4j', version: '1.6.2'
       earlib group: 'commons-beanutils', name: 'commons-beanutils', version: '1.9.4'
       earlib group: 'org.apache.commons', name: 'commons-digester3', version: '3.2'
       earlib group: 'libraries.nscommon', name: 'ns-common', version: '1.5.0', transitive: false
       earlib group: 'com.auth0', name: 'java-jwt', version: '3.9.0'  // !!! questa dipende da jackson-core che potrebbe andare in conflito con quello deploiato su JBoss
       earlib group: 'com.squareup.okhttp3', name: 'okhttp', version: '3.12.10'
       earlib group: 'com.google.code.gson', name: 'gson', version: '2.8.6'
       earlib group: 'com.itextpdf', name: 'itextpdf', version: '5.4.5'

       earlib group: 'libraries.firmaremota-client', name: 'firmaremota-client-detached', version: '1.2.1'
       earlib group: 'libraries.firmaremota-client', name: 'firmaremota-client-common', version: '1.2.1'

       earlib group: 'it.netservice.csc.documentale', name: 'csc-documentale', version: '1.0.0'
       earlib group: 'org.apache.poi', name: 'poi', version: '3.14'
       earlib group: 'org.apache.poi', name: 'poi-ooxml', version: '3.14'
       //dipendenze di documentale (che alza asticella wsdl4j a 1.6.2)
       earlib group: 'org.apache.ws.commons.schema', name: 'XmlSchema', version: '1.4.2'
       earlib group: 'org.apache.ws.commons.axiom', name: 'axiom-api', version: '1.2.13'
       earlib group: 'org.apache.ws.commons.axiom', name: 'axiom-dom', version: '1.2.13'
       earlib group: 'org.apache.ws.commons.axiom', name: 'axiom-impl', version: '1.2.13'
       earlib group: 'org.apache.axis2', name: 'axis2', version: '1.6.2'
       earlib group: 'org.apache.axis2', name: 'axis2-transport-http', version: '1.6.2'
       earlib group: 'org.apache.axis2', name: 'axis2-transport-local', version: '1.6.2'
       earlib group: 'org.codehaus.castor', name: 'castor-core', version: '1.3.3'
       earlib group: 'org.codehaus.castor', name: 'castor-xml', version: '1.3.3'
       earlib group: 'it.sauronsoftware', name: 'ftp4j', version: '1.5.1'
       earlib group: 'log4j', name: 'log4j', version: '1.2.15'
       earlib group: 'org.apache.neethi', name: 'neethi', version: '3.0.2'
       earlib group: 'com.sun.xml.parsers', name: 'jaxp-ri', version: '1.4.5'
       earlib group: 'org.apache.poi', name: 'poi', version: '3.14'
       earlib group: 'org.apache.poi', name: 'poi-ooxml', version: '3.14'
   */
}

ear {
    deploymentDescriptor {
        version = "1.4"
        applicationName = "ear-cspbackend"
        displayName = "CSP-Backend"
        webModule("war-cspbackend.war", "csp-backend")
    }
}
