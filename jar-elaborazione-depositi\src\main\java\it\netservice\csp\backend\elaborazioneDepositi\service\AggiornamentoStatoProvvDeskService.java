/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package it.netservice.csp.backend.elaborazioneDepositi.service;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.EntityManager;

import okhttp3.Credentials;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;

import org.apache.log4j.Logger;

/**
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class AggiornamentoStatoProvvDeskService {

    private static final Logger LOGGER = Logger.getLogger(AggiornamentoStatoProvvDeskService.class);
    private final OkHttpClient client = new OkHttpClient();

    public void aggiornaStatoProvvedimento(EntityManager entityManager, Long idCat) {
        if (idCat != null) {
            try {
                String url = String.format("/provvedimentiLavorazione/accetta?idCat=%s", idCat);
                Request req = getRequestBuilder(url).post(RequestBody.create(MediaType.get("application/json; charset=utf-8"), "")).build();
                try (okhttp3.Response response = client.newCall(req).execute()) {
                }
                LOGGER.info("Aggiornamento stato provvedimento idCat = " + idCat + " con successo");
            } catch (Exception ex) {
                LOGGER.error("", ex);
            }
        }
    }

    private Request.Builder getRequestBuilder(String endpoint) {
        String cred = Credentials.basic("desk", "servizi-desk");
        String url = System.getProperty("servizi-desk.url") + "/servizi-desk/jsonapi" + endpoint;
        return new Request.Builder().url(url).addHeader("UserCode", "SGR").addHeader("Authorization", cred);
    }

}
