/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package it.netservice.csp.backend.pubblicazioneDepositi;

import org.apache.log4j.Logger;

import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.excpetion.CspCommonRestWarningException;

/**
 *
 * <AUTHOR>
 */
public class PubblicazioneSemaforo {
    private static volatile PubblicazioneSemaforo pubblicazioneSemaforo;

    private static final Logger LOGGER = Logger.getLogger(PubblicazioneSemaforo.class);
    private long lockTime = System.currentTimeMillis();
    private static final long lockMaxDuration = 5 * 60 * 1000;// 5 minutes
    private static final String messaggioOccupato = "Pubblicazione precedente in corso.Provare piu' tardi";
    private static final String messaggioNullo = "Richiesto semaforo per deposito nullo";
    private static final String messaggioErrore = "Errore rilascio semaforo pubblicazione";
    private Long idCat;

    private Boolean isSic;

    private PubblicazioneSemaforo() {
    }

    public static PubblicazioneSemaforo getInstance() {
        PubblicazioneSemaforo localPubblicazioneSemaforo = pubblicazioneSemaforo;
        if (localPubblicazioneSemaforo == null) {
            synchronized (PubblicazioneSemaforo.class) {
                localPubblicazioneSemaforo = pubblicazioneSemaforo;
                if (localPubblicazioneSemaforo == null) {
                    pubblicazioneSemaforo = localPubblicazioneSemaforo = new PubblicazioneSemaforo();
                }
            }
        }
        return localPubblicazioneSemaforo;
    }

    public synchronized void registraPubblicazione(Long idCat, boolean isSic) throws CspCommonRestWarningException {
        if (idCat == null) {
            LOGGER.info(messaggioNullo);
            throw new CspCommonRestWarningException(messaggioNullo, RestErrorCodeEnum.IDCAT_DEPOSITO_NULL);
        }
        if (this.idCat == null) {
            this.idCat = idCat;
            this.isSic = isSic;
            lockTime = System.currentTimeMillis();
            if (isSic) {
                LOGGER.info("Attivato semaforo di pubblicazione per il SIC " + idCat);
            } else {
                LOGGER.info("Attivato semaforo di pubblicazione per il deposito " + idCat);
            }

        } else {
            if (System.currentTimeMillis() > lockTime + lockMaxDuration) {
                if (isSic) {
                    LOGGER.info("Timeout semaforo SIC " + this.idCat + " attivato nuovo semaforo per il SIC " + idCat);
                } else {
                    LOGGER.info("Timeout semaforo deposito " + this.idCat + " attivato nuovo semaforo per il deposito " + idCat);
                }

                this.idCat = idCat;
                this.isSic = isSic;
                lockTime = System.currentTimeMillis();
            } else {
                LOGGER.info(messaggioOccupato + " Richiesto idCat:" + idCat + " in corso " + this.idCat);
                throw new CspCommonRestWarningException(messaggioOccupato, RestErrorCodeEnum.PUBBLICAZIONE_IN_CORSO);
            }
        }

    }

    public synchronized void sbloccaPubblicazione(Long idCat, boolean isSic) {
        if (idCat != null && this.idCat != null && idCat.longValue() == this.idCat.longValue()) {
            this.idCat = null;
            this.isSic = null;
            if (isSic) {
                LOGGER.info("Disattivato semaforo di pubblicazione per il SIC " + idCat);
            } else {
                LOGGER.info("Disattivato semaforo di pubblicazione per il deposito " + idCat);
            }

        } else {
            LOGGER.info(messaggioErrore);
        }
    }

}
