<?xml version="1.0" encoding="UTF-8"?>

<persistence version="2.0"
         xmlns="http://java.sun.com/xml/ns/persistence"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/persistence http://java.sun.com/xml/ns/persistence/persistence_2_0.xsd">
    <persistence-unit name="DBUCU" transaction-type="RESOURCE_LOCAL">
    <non-jta-data-source>java:jboss/datasources/DBUCU</non-jta-data-source>

    <properties>
        <property name="jboss.entity.manager.jndi.name" value="java:/jboss/persistence/DBUCU" />
        <property name="jboss.entity.manager.factory.jndi.name" value="java:/jboss/persistence/DBUCUFactory" />
        <property name="hibernate.dialect" value="org.hibernate.dialect.Oracle10gDialect" />
        <property name="hibernate.show_sql" value="${show_sql:false}"/>
        <property name="hibernate.type" value="${type:false}"/>
    </properties>
    </persistence-unit>
</persistence>

