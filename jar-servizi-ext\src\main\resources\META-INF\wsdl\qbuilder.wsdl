<?xml version="1.0" encoding="UTF-8"?>
<!--
  Author: nicola  
  Version: $Id: qbuilder.wsdl,v 1.1 2009/01/12 17:07:31 nicola Exp $
  Description:
    WSDL Web Service esposto da query builder.
    Ogni WSDL deve essere adattato con il namespace opportuno per puntare al servizio (istanza di servizio)
    configurata.
-->
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:qbt="http://www.netserv.it/QBuilder/types" xmlns:tns="http://www.netserv.it/QBuilder" targetNamespace="http://www.netserv.it/QBuilder">
  <import namespace="http://www.netserv.it/QBuilder/types" location="./qbuilder-types.xsd"/>
  <message name="executeRequest">
    <part name="name" type="xsd:string"/>
    <part name="valueSet" type="qbt:valueSetType"/>
    <part name="orderBy" type="qbt:orderByType"/>
  </message>
  <message name="executeResponse">
    <part name="rowList" type="qbt:rowListType"/>
  </message>
  <message name="getRowClassDescriptorRequest">
    <part name="name" type="xsd:string"/>
  </message>
  <message name="getRowClassDescriptorResponse">
    <part name="return" type="qbt:rowClassDescriptorType"/>
  </message>
  <message name="getServiceDescriptorRequest">
    <part name="name" type="xsd:string"/>
  </message>
  <message name="getServiceDescriptorResponse">
    <part name="return" type="qbt:serviceDescriptorType"/>
  </message>
  <message name="getServiceNamesRequest"/>
  <message name="getServiceNamesResponse">
    <part name="return" type="qbt:stringArrayType"/>
  </message>
  <portType name="ServiziQueryBuilder">
    <operation name="execute">
      <input message="tns:executeRequest"/>
      <output message="tns:executeResponse"/>
    </operation>
    <operation name="getRowClassDescriptor">
      <input message="tns:getRowClassDescriptorRequest"/>
      <output message="tns:getRowClassDescriptorResponse"/>
    </operation>
    <operation name="getServiceDescriptor">
      <input message="tns:getServiceDescriptorRequest"/>
      <output message="tns:getServiceDescriptorResponse"/>
    </operation>
    <operation name="getServiceNames">
      <input message="tns:getServiceNamesRequest"/>
      <output message="tns:getServiceNamesResponse"/>
    </operation>
  </portType>
</definitions>
