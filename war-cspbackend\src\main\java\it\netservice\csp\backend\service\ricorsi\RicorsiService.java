package it.netservice.csp.backend.service.ricorsi;

import java.util.*;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.PersistenceUnit;
import javax.persistence.Query;

import org.apache.log4j.Logger;

import it.netservice.common.rest.PageInfo;
import it.netservice.common.rest.PagedResponse;
import it.netservice.common.rest.backend.CriteriaBuilderHelper;
import it.netservice.common.rest.backend.InputPayloadBackend;
import it.netservice.csp.backend.common.Constats;
import it.netservice.csp.backend.common.Utils;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.dto.RicercaPartiParams;
import it.netservice.csp.backend.dto.RicercaRicorsiBaseDto;
import it.netservice.csp.backend.dto.RicercaRicorsiKeysDto;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.RicorsiExcpetion;
import it.netservice.csp.backend.pubblicazioneDepositi.PubblicazioneService;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.csp.backend.sic.service.repository.SicRepositoryException;
import it.netservice.penale.model.common.dettaglioricorso.Timbro;
import it.netservice.penale.model.csp.RicercaRicorsiBaseFuoriUdienza;
import it.netservice.penale.model.csp.RicercaRicorsiBaseInUdienza;
import it.netservice.penale.model.csp.RicercaRicorsiBaseInUdienzaPK;
import it.netservice.penale.model.sic.PenaleRicercaRiunitiView;
import it.netservice.penale.model.sic.SicPenaleEntity;
import it.netservice.penale.model.sic.criteria.csp.PenaleEsitoCriteriaParameters;
import it.netservice.penale.model.sic.criteria.csp.RicercaRicorsiBaseFuoriUdienzaCriteriaParameters;
import it.netservice.penale.model.sic.criteria.csp.RicercaRicorsiBaseInUdienzaCriteriaParameters;
import it.netservice.penale.model.sic.criteria.csp.RiunitiCriteriaParameters;
import it.netservice.penale.model.sic.csp.PenaleEsito;
import it.netservice.penale.model.sic.csp.PenaleUdienzaRicorso;

public class RicorsiService {
    private static final Logger LOGGER = Logger.getLogger(RicorsiService.class);

    @Inject
    PubblicazioneService pubblicazioneService;

    @PersistenceUnit(unitName = "sic-model-penale")
    protected EntityManagerFactory emfCaricamento;

    public RicorsiService() {
    }

    public RicercaRicorsiBaseDto ricercaRicorsi(InputPayloadBackend<RicercaRicorsiBaseInUdienzaCriteriaParameters> inputPayload)
            throws CspBackendException {
        EntityManager entityManager = emfCaricamento.createEntityManager();

        LOGGER.info("Controllo preventivo della corretta valorizzazione dei campi di ricerca");
        RicercaRicorsiBaseDto ricercaRicorsiBaseDto = new RicercaRicorsiBaseDto();

        if (inputPayload.getCriteriaParameters().getDataUdienzaDa() == null
                && (inputPayload.getCriteriaParameters().getNumero() == null || inputPayload.getCriteriaParameters().getAnno() == null)
                && inputPayload.getCriteriaParameters().getCognomeParte() == null) {
            ricercaRicorsiBaseDto.setMessage("È necessario completare i campi di ricerca necessari");
            return ricercaRicorsiBaseDto;
        }

        try {
            PagedResponse<RicercaRicorsiBaseInUdienza> pagedResponse = new PagedResponse<>();
            List<RicercaRicorsiBaseInUdienza> ricorsi;
            if (inputPayload.getCriteriaParameters().getCognomeParte() != null) {
                pagedResponse = findRicorsiByParte(inputPayload, ricercaRicorsiBaseDto, entityManager);
                if (pagedResponse == null || pagedResponse.getContent().isEmpty()) {
                    return ricercaRicorsiBaseDto;
                }
                ricorsi = (List<RicercaRicorsiBaseInUdienza>) pagedResponse.getContent();
            } else {
                CriteriaBuilderHelper<RicercaRicorsiBaseInUdienza> criteriaBuilderInUdienza = new CriteriaBuilderHelper<>(
                        RicercaRicorsiBaseInUdienza.class);
                // NRG fa parte della chiave primaria ricercaRicorsiBaseInUdienzaPK e non consente di ordinare solo per NRG,
                // ma ordina per tutte le colonne della chiave, pertanto per funzionare si ordina per l'nrgreale, formato in questo caso
                // dai campi numero e anno
                if (inputPayload.getPageInfo().getOrder().getPropertyName().equals("nrg")) {
                    inputPayload.getPageInfo().getOrder().setPropertyName("anno,numero");
                }

                LOGGER.info("Eseguo ricerca ricorsi in udienza");
                PagedResponse<RicercaRicorsiBaseInUdienza> pagedResponseInUdienza = criteriaBuilderInUdienza.resultListPaged(entityManager,
                        inputPayload);
                ricorsi = (List<RicercaRicorsiBaseInUdienza>) pagedResponseInUdienza.getContent();

                // Se si sta cercando per numero ricorso e non sono stati trovati risultati,
                // cerco nella vista dei ricorsi fuori udienza
                if ((ricorsi == null || ricorsi.isEmpty()) && inputPayload.getCriteriaParameters().getNumero() != null) {
                    LOGGER.info("Eseguo ricerca per ricorsi fuori udienza");
                    pagedResponse = getRicorsiFuoriUdienza(entityManager, inputPayload, ricorsi);
                } else {
                    pagedResponse = pagedResponseInUdienza;
                }
                // Nel caso l'ordine era stato modificato in numero,anno, reiserisco nrg come propertyName
                if (pagedResponse.getPageInfo().getOrder().getPropertyName().equals("anno,numero")) {
                    pagedResponse.getPageInfo().getOrder().setPropertyName("nrg");
                }
            }

            if (!ricorsi.isEmpty()) {
                for (RicercaRicorsiBaseInUdienza ricorso : ricorsi) {
                    // Parte commentata per uniformare comportamento di ricerca per data udienza, vedere card 16006 (serviva per rimuovere
                    // dalla lista i tolti dal ruolo)
                    // if (ricorso != null && ricorso.getRicercaRicorsiBaseInUdienzaPK().getNrg() != null
                    // && inputPayload.getCriteriaParameters().getDataUdienzaDa() != null
                    // && isToltoSospesoDalRuolo(String.valueOf(ricorso.getRicercaRicorsiBaseInUdienzaPK().getNrg()), entityManager)) {
                    // ricorsi.remove(ricorso);
                    // }
                    if (ricorso != null && ricorso.getRicercaRiunitiView() != null
                            && ricorso.getRicercaRiunitiView().getIdRicUdienPadre() != null) {
                        Map<String, Integer> mapAnnoNumero = Utils
                                .convertNrgRealeToAnnoAndNumero(ricorso.getRicercaRiunitiView().getNrgRealePadre());
                        if (!mapAnnoNumero.isEmpty()) {
                            PenaleRicercaRiunitiView ricercaRiunitiView = ricorso.getRicercaRiunitiView();
                            ricercaRiunitiView.setAnnoPadre(mapAnnoNumero.get(Constats.KEY_ANNO));
                            ricercaRiunitiView.setNumeroPadre(mapAnnoNumero.get(Constats.KEY_NUMERO));
                            ricorso.setRicercaRiunitiView(ricercaRiunitiView);
                        }
                    }

                    // Questa parte viene eseguita solo nel caso in cui i ricorsi sono associati ad un'udienza
                    if (ricorso.getRicercaRicorsiBaseInUdienzaPK().getIdRicUdien() != null) {
                        boolean oscuramentoSic = calcolareOscuramentoSicComplessivo(new SicPenaleRepository(entityManager), entityManager,
                                ricorso.getRicercaRicorsiBaseInUdienzaPK().getIdRicUdien());
                        ricorso.setOscuramentoSic(oscuramentoSic ? oscuramentoSic : ricorso.getOscuramentoSic() != null ? ricorso
                                .getOscuramentoSic() : false);
                        Timbro timbroByNrgAndIdUienza = pubblicazioneService.findTimbroByNrgAndIdUienza(entityManager,
                                ricorso.getRicercaRicorsiBaseInUdienzaPK().getIdUdienza(),
                                ricorso.getRicercaRicorsiBaseInUdienzaPK().getNrg());
                        Boolean oscuramentoDeskCsp = ricorso.getOscuramentoDeskCsp();
                        if (timbroByNrgAndIdUienza != null) {
                            String tipooscuramento = timbroByNrgAndIdUienza.getTipooscuramento();
                            if (tipooscuramento == null || tipooscuramento.equalsIgnoreCase("Nessuno")) {
                                oscuramentoDeskCsp = ricorso.getOscuramentoDeskCsp();
                            } else {
                                oscuramentoDeskCsp = true;
                            }
                        }
                        ricorso.setOscuramentoDeskCsp(oscuramentoDeskCsp);
                    }
                }
            }

            if (ricorsi == null || ricorsi.isEmpty() || (ricorsi.size() == 1 && ricorsi.get(0) == null)) {
                if (inputPayload.getCriteriaParameters().getDataUdienzaDa() != null
                        && inputPayload.getCriteriaParameters().getNumero() == null) {
                    ricercaRicorsiBaseDto.setMessage("Non ci sono udienze per i criteri di ricerca impostati");
                } else if (inputPayload.getCriteriaParameters().getNumero() != null
                        && inputPayload.getCriteriaParameters().getDataUdienzaDa() == null) {
                    ricercaRicorsiBaseDto.setMessage("Non ci sono ricorsi per i criteri di ricerca impostati");
                } else {
                    ricercaRicorsiBaseDto.setMessage("Non ci sono risultati per i criteri di ricerca impostati");
                }
            } else {
                ricercaRicorsiBaseDto.setResponse(pagedResponse);
            }
            String result = ricercaRicorsiBaseDto.getMessage() != null ? ricercaRicorsiBaseDto
                    .getMessage() : ("Ricorsi trovati " + ricorsi.size());
            LOGGER.info("Fine ricerca ricorsi. " + result);
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            throw new RicorsiExcpetion("Errore durante la ricerca dei ricorsi", RestErrorCodeEnum.SEARCH_RICORSI_ERROR, e);
        } finally {
            entityManager.close();
        }
        return ricercaRicorsiBaseDto;
    }

    private PagedResponse<RicercaRicorsiBaseInUdienza> findRicorsiByParte(
            InputPayloadBackend<RicercaRicorsiBaseInUdienzaCriteriaParameters> inputPayload, RicercaRicorsiBaseDto ricercaRicorsiBaseDto,
            EntityManager entityManager) {
        PagedResponse<RicercaRicorsiBaseInUdienza> pagedResponse = new PagedResponse<>();
        List<RicercaRicorsiKeysDto> listNrgAndIdRicudienByParte = getNrgsAndRicudienByParte(inputPayload, pagedResponse, entityManager);
        if (listNrgAndIdRicudienByParte == null || listNrgAndIdRicudienByParte.isEmpty()) {
            ricercaRicorsiBaseDto.setMessage("Non esistono risultati per i criteri di ricerca impostati");
            return null;
        }
        List<RicercaRicorsiBaseInUdienza> ricorsi = findRicorsiByNrgAndRicudienList(listNrgAndIdRicudienByParte, entityManager);
        pagedResponse.setContent(ricorsi);
        return pagedResponse;

    }

    private List<RicercaRicorsiKeysDto> getNrgsAndRicudienByParte(
            InputPayloadBackend<RicercaRicorsiBaseInUdienzaCriteriaParameters> inputPayload, PagedResponse pagedResponse,
            EntityManager entityManager) {
        RicercaPartiParams ricercaPartiParams = new RicercaPartiParams(inputPayload.getCriteriaParameters().getCognomeParte());
        ricercaPartiParams.setSezione(inputPayload.getCriteriaParameters().getSezioneUd());
        ricercaPartiParams.setDataUdDa(inputPayload.getCriteriaParameters().getDataUdienzaDa());
        ricercaPartiParams.setDataUdA(inputPayload.getCriteriaParameters().getDataUdienzaA());
        ricercaPartiParams.setTipoUd(inputPayload.getCriteriaParameters().getTipoUdienza());
        ricercaPartiParams.setCollegio(inputPayload.getCriteriaParameters().getAula());
        String nrgreale = inputPayload.getCriteriaParameters().getNumero() != null ? SicPenaleEntity.formatNrgReale(
                String.valueOf(inputPayload.getCriteriaParameters().getNumero()), inputPayload.getCriteriaParameters().getAnno(),
                null) : null;
        ricercaPartiParams.setNrgrealeDa(nrgreale);
        ricercaPartiParams.setNrgrealeA(nrgreale);
        ricercaPartiParams.setOrderBy(inputPayload.getPageInfo().getOrder().getPropertyName());
        ricercaPartiParams.setOrder(inputPayload.getPageInfo().getOrder().isAsc() ? "ASC" : "DESC");
        int firstResult = inputPayload.getPageInfo().getPageNumber() * inputPayload.getPageInfo().getPageSize();

        SicPenaleRepository repo = new SicPenaleRepository(entityManager);
        List<RicercaRicorsiKeysDto> result = repo.getRicorsiByCognomeParte(ricercaPartiParams, firstResult,
                inputPayload.getPageInfo().getPageSize());
        if (result != null && !result.isEmpty()) {
            int pageSize = inputPayload.getPageInfo().getPageSize();
            long totalRecords = repo.calculateTotRecordsRicorsiByParte(ricercaPartiParams);
            int totalPages = (int) ((totalRecords + pageSize - 1) / pageSize);
            inputPayload.getPageInfo().setTotPages(totalPages);
            inputPayload.getPageInfo().setTotRecordsCount(totalRecords);
            pagedResponse.setPageInfo(inputPayload.getPageInfo());
        }
        return result;
    }

    private List<RicercaRicorsiBaseInUdienza> findRicorsiByNrgAndRicudienList(List<RicercaRicorsiKeysDto> listNrgAndIdRicudienByParte,
            EntityManager entityManager) {
        List<Long> idRicudienList = new ArrayList<>();
        List<Long> nrgFuoriUdienzaList = new ArrayList<>();
        for (RicercaRicorsiKeysDto nrgAndRicudien : listNrgAndIdRicudienByParte) {
            if (nrgAndRicudien.getIdRicudien() != null)
                idRicudienList.add(nrgAndRicudien.getIdRicudien());
            else
                nrgFuoriUdienzaList.add(nrgAndRicudien.getNrg());
        }
        SicPenaleRepository repo = new SicPenaleRepository(entityManager);
        Map<Long, RicercaRicorsiBaseInUdienza> ricorsiInUdienza = repo.getRicorsiInUdienzaByIdRicudienList(idRicudienList);
        Map<Long, RicercaRicorsiBaseFuoriUdienza> ricorsiFuoriUdienza = repo.getRicorsiInUdienzaByIdNrgList(nrgFuoriUdienzaList);
        return mergeRicorsi(listNrgAndIdRicudienByParte, ricorsiInUdienza, ricorsiFuoriUdienza);
    }

    private List<RicercaRicorsiBaseInUdienza> mergeRicorsi(List<RicercaRicorsiKeysDto> listNrgAndIdRicudienByParte,
            Map<Long, RicercaRicorsiBaseInUdienza> ricorsiInUdienzaMap, Map<Long, RicercaRicorsiBaseFuoriUdienza> ricorsiFuoriUdienzaMap) {
        List<RicercaRicorsiBaseInUdienza> ricorsiMergedList = new ArrayList<>();
        for (RicercaRicorsiKeysDto nrgAndRicudien : listNrgAndIdRicudienByParte) {
            if (nrgAndRicudien.getIdRicudien() != null)
                ricorsiMergedList.add(ricorsiInUdienzaMap.get(nrgAndRicudien.getIdRicudien()));
            else {
                RicercaRicorsiBaseFuoriUdienza ricorsoFuoriUdienza = ricorsiFuoriUdienzaMap.get(nrgAndRicudien.getNrg());
                ricorsiMergedList.add(convertRicorsoFuoriUdienzaToInUdienza(ricorsoFuoriUdienza));
            }
        }
        return ricorsiMergedList;
    }

    private PagedResponse<RicercaRicorsiBaseInUdienza> getRicorsiFuoriUdienza(EntityManager entityManager,
            InputPayloadBackend<RicercaRicorsiBaseInUdienzaCriteriaParameters> inputPayload, List<RicercaRicorsiBaseInUdienza> ricorsi) {
        CriteriaBuilderHelper<RicercaRicorsiBaseFuoriUdienza> criteriaBuilderFuoriUdienza = new CriteriaBuilderHelper<>(
                RicercaRicorsiBaseFuoriUdienza.class);
        InputPayloadBackend<RicercaRicorsiBaseFuoriUdienzaCriteriaParameters> inputPayloadFuoriUdienza = getInputPayloadRicorsiFuoriUdienza(
                inputPayload);

        PagedResponse<RicercaRicorsiBaseFuoriUdienza> pagedResponseFuoriUdienza = criteriaBuilderFuoriUdienza.resultListPaged(entityManager,
                inputPayloadFuoriUdienza);
        List<RicercaRicorsiBaseFuoriUdienza> ricorsiFuoriUdienza = (List<RicercaRicorsiBaseFuoriUdienza>) pagedResponseFuoriUdienza
                .getContent();

        // Per evitare di inviare 2 liste separate di ricorsi in base alla vista esaminata, i ricorsi fuori udienza vengono mappati dentro
        // la lista dei ricorsi
        mapRicorsiFuoriUdienza(ricorsi, ricorsiFuoriUdienza);
        PagedResponse<RicercaRicorsiBaseInUdienza> result = new PagedResponse<>();
        result.setContent(ricorsi);
        result.setPageInfo(pagedResponseFuoriUdienza.getPageInfo());
        return result;
    }

    private InputPayloadBackend<RicercaRicorsiBaseFuoriUdienzaCriteriaParameters> getInputPayloadRicorsiFuoriUdienza(
            InputPayloadBackend<RicercaRicorsiBaseInUdienzaCriteriaParameters> inputPayload) {
        InputPayloadBackend<RicercaRicorsiBaseFuoriUdienzaCriteriaParameters> inputPayloadFuoriUdienza = new InputPayloadBackend<>();
        PageInfo pageInfo = new PageInfo();
        PageInfo.Order order = new PageInfo.Order();
        // Controlla l'ordinamento per verificare se presente per i ricorsi fuori udienza
        List<String> ordinamentiFuoriUdienza = Arrays.asList("nrg", "ruolo", "detParti", "reato", "valPond");
        if (!ordinamentiFuoriUdienza.contains(order.getPropertyName()))
            order.setPropertyName("nrg");
        else {
            order.setPropertyName(inputPayload.getPageInfo().getOrder().getPropertyName());
        }
        order.setAsc(inputPayload.getPageInfo().getOrder().isAsc());
        pageInfo.setOrder(order);
        pageInfo.setPageNumber(inputPayload.getPageInfo().getPageNumber());
        pageInfo.setPageSize(inputPayload.getPageInfo().getPageSize());
        inputPayloadFuoriUdienza.setPageInfo(pageInfo);
        inputPayloadFuoriUdienza.setDistinct(inputPayload.getDistinct());

        RicercaRicorsiBaseFuoriUdienzaCriteriaParameters criteriaParametersFuoriUdienza = new RicercaRicorsiBaseFuoriUdienzaCriteriaParameters();
        criteriaParametersFuoriUdienza.setNrgs(inputPayload.getCriteriaParameters().getNrgs());
        criteriaParametersFuoriUdienza.setNumero(inputPayload.getCriteriaParameters().getNumero());
        criteriaParametersFuoriUdienza.setAnno(inputPayload.getCriteriaParameters().getAnno());
        criteriaParametersFuoriUdienza.setSezioneUd(inputPayload.getCriteriaParameters().getSezioneUd());
        inputPayloadFuoriUdienza.setCriteriaParameters(criteriaParametersFuoriUdienza);
        return inputPayloadFuoriUdienza;
    }

    private void mapRicorsiFuoriUdienza(List<RicercaRicorsiBaseInUdienza> ricorsi,
            List<RicercaRicorsiBaseFuoriUdienza> ricorsiFuoriUdienza) {
        if (ricorsiFuoriUdienza != null && !ricorsiFuoriUdienza.isEmpty()) {
            if (ricorsi == null)
                ricorsi = new ArrayList<>();
            for (RicercaRicorsiBaseFuoriUdienza ricorsoFuoriUdienza : ricorsiFuoriUdienza) {
                ricorsi.add(convertRicorsoFuoriUdienzaToInUdienza(ricorsoFuoriUdienza));
            }
        }
    }

    private RicercaRicorsiBaseInUdienza convertRicorsoFuoriUdienzaToInUdienza(RicercaRicorsiBaseFuoriUdienza ricorsoFuoriUdienza) {
        RicercaRicorsiBaseInUdienza ricorso = new RicercaRicorsiBaseInUdienza();
        ricorso.setRicercaRicorsiBaseInUdienzaPK(new RicercaRicorsiBaseInUdienzaPK());
        ricorso.getRicercaRicorsiBaseInUdienzaPK().setNrg(ricorsoFuoriUdienza.getNrg());
        ricorso.setNumero(ricorsoFuoriUdienza.getNumero());
        ricorso.setAnno(ricorsoFuoriUdienza.getAnno());
        ricorso.setSezioneUd(ricorsoFuoriUdienza.getSezioneUd());
        ricorso.setDetParti(ricorsoFuoriUdienza.getDetParti());
        ricorso.setReato(ricorsoFuoriUdienza.getReato());
        ricorso.setValPond(ricorsoFuoriUdienza.getValPond());
        return ricorso;
    }

    private boolean calcolareOscuramentoSicComplessivo(SicPenaleRepository repo, EntityManager entityManager, Long idRicUdien)
            throws SicRepositoryException {
        List<PenaleRicercaRiunitiView> ricorsoRiunitoViewByIdRicUdienPadre = repo.getRicorsoRiunitoViewByIdRicUdienPadre(idRicUdien);
        if (ricorsoRiunitoViewByIdRicUdienPadre != null && !ricorsoRiunitoViewByIdRicUdienPadre.isEmpty()) {
            LOGGER.info("Calcolo dell'oscuramento del sic per idRicUdien:" + idRicUdien);
            List<Long> idsRicUdienFigli = new ArrayList<>();
            for (PenaleRicercaRiunitiView riunitiFigli : ricorsoRiunitoViewByIdRicUdienPadre) {
                idsRicUdienFigli.add(riunitiFigli.getIdRicUdienza());
            }
            InputPayloadBackend<PenaleEsitoCriteriaParameters> inputPayload = new InputPayloadBackend<>();
            PenaleEsitoCriteriaParameters criteriaParametersPenaleEsito = new PenaleEsitoCriteriaParameters();
            criteriaParametersPenaleEsito.setIsdRicorsoUdienza(idsRicUdienFigli);
            inputPayload.setCriteriaParameters(criteriaParametersPenaleEsito);
            PageInfo pageInfo = new PageInfo();
            PageInfo.Order order = new PageInfo.Order();
            order.setPropertyName("idEsito");
            order.setAsc(true);
            pageInfo.setOrder(order);
            pageInfo.setPageSize(200);
            inputPayload.setPageInfo(pageInfo);
            CriteriaBuilderHelper<PenaleEsito> criteriaBuilderPenaleEsito = new CriteriaBuilderHelper<>(PenaleEsito.class);
            PagedResponse<PenaleEsito> pagedResponse = criteriaBuilderPenaleEsito.resultListPaged(entityManager, inputPayload);
            List<PenaleEsito> listaEsitiRiuniti = (List<PenaleEsito>) pagedResponse.getContent();
            for (PenaleEsito esitoRiunito : listaEsitiRiuniti) {
                if (esitoRiunito.getPrivacy() != null && esitoRiunito.getPrivacy().getSigla().equalsIgnoreCase("SI")) {
                    return true;
                }
            }
        }
        return false;
    }

    private static Boolean isToltoSospesoDalRuolo(String nrg, EntityManager entityManager) {
        LOGGER.info("Verifica tolto sospeso dal ruolo per nrg:" + nrg);
        Query query = entityManager.createNativeQuery("SELECT * FROM PENALE_STORIA WHERE NRG = " + nrg + " AND ID_FUNZIONE = 33");
        // query.setParameter("idSentenza", idSentenza);
        List<Object> resultList = query.getResultList();
        return resultList != null && !resultList.isEmpty();
    }

    public List<PenaleUdienzaRicorso> getRicorsiRiuniti(Long idRicUdienPadre) throws CspBackendException {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            Set<Long> listRiunitiDaVisualizzare = new HashSet<>();
            SicPenaleRepository repo = new SicPenaleRepository(entityManager);
            PenaleUdienzaRicorso udienzaRicorso = repo.getRicordoUdienzaByIdRicUdienza(idRicUdienPadre);
            if (udienzaRicorso.getRicorso().getNrg() != null) {
                List<PenaleUdienzaRicorso> ricordoUdienzaRiunitiFigli = repo
                        .getRicordoUdienzaRiunitiFigli(udienzaRicorso.getRicorso().getNrg());
                for (PenaleUdienzaRicorso udienSentenza : ricordoUdienzaRiunitiFigli) {
                    listRiunitiDaVisualizzare.add(udienSentenza.getIdRicorsoUdienza());
                }
            }
            Long idSentPadre = udienzaRicorso.getvSentenza().getId();
            if (idSentPadre != null) {
                List<PenaleUdienzaRicorso> ricordoUdienzaRiunitiFigliByIdSent = repo.getRicordoUdienzaRiunitiFigliByIdSent(idSentPadre);
                for (PenaleUdienzaRicorso udienSentenza : ricordoUdienzaRiunitiFigliByIdSent) {
                    listRiunitiDaVisualizzare.add(udienSentenza.getIdRicorsoUdienza());
                }
            }
            InputPayloadBackend<RiunitiCriteriaParameters> inputPayload = new InputPayloadBackend<>();
            RiunitiCriteriaParameters criteriaParameters = new RiunitiCriteriaParameters();
            criteriaParameters.setIdsRicorsoUdienza(listRiunitiDaVisualizzare);
            inputPayload.setCriteriaParameters(criteriaParameters);
            PageInfo pageInfo = new PageInfo();
            PageInfo.Order order = new PageInfo.Order();
            order.setPropertyName("nrg");
            order.setAsc(true);
            pageInfo.setOrder(order);
            pageInfo.setPageSize(200);
            inputPayload.setPageInfo(pageInfo);
            CriteriaBuilderHelper<PenaleUdienzaRicorso> criteriaBuilder = new CriteriaBuilderHelper<>(PenaleUdienzaRicorso.class);
            PagedResponse<PenaleUdienzaRicorso> pagedResponse = criteriaBuilder.resultListPaged(entityManager, inputPayload);
            List<PenaleUdienzaRicorso> result = new ArrayList<>();
            for (PenaleUdienzaRicorso ricUdien : (List<PenaleUdienzaRicorso>) pagedResponse.getContent()) {
                Map<String, Integer> stringIntegerMap = Utils
                        .convertNrgRealeToAnnoAndNumero(Long.valueOf(ricUdien.getRicorso().getNrgReale()));
                if (stringIntegerMap.size() >= 2) {
                    ricUdien.setAnno(stringIntegerMap.get(Constats.KEY_ANNO).toString());
                    ricUdien.setNumero(stringIntegerMap.get(Constats.KEY_NUMERO).toString());
                }
                result.add(ricUdien);
            }
            return result;
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            throw new RicorsiExcpetion("Errore durante la ricerca dei riuniti.", RestErrorCodeEnum.RIUNITI_RICORSO_ERROR, e);
        } finally {
            entityManager.close();
        }
    }
}
