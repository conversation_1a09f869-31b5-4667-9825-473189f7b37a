package it.netservice.csp.backend.dto;

import java.util.Date;

/**
 * Classe utilizzata per contenere i parametri di ricerca da utilizzare nella ricerca dei ricorsi e delle udienze che includono l'utilizzo
 * del cognome della parte
 */
public class RicercaPartiParams {
    private String cognomeParte;
    private String sezione;
    private Date dataUdDa;
    private Date dataUdA;
    private String tipoUd;
    private String collegio;
    private String nrgrealeDa;
    private String nrgrealeA;
    private String cfPresidente;
    private String cfEstensore;
    private Date dataMinutaDa;
    private Date dataMinutaA;
    private Date dataPubblicazioneDa;
    private Date dataPubblicazioneA;

    private String orderBy;
    private String order;

    private Boolean removeRiuniti;

    public RicercaPartiParams(String cognomeParte) {
        super();
        this.cognomeParte = cognomeParte;
    }

    public String getCognomeParte() {
        return cognomeParte;
    }

    public void setCognomeParte(String cognomeParte) {
        this.cognomeParte = cognomeParte;
    }

    public String getSezione() {
        return sezione;
    }

    public void setSezione(String sezione) {
        this.sezione = sezione;
    }

    public Date getDataUdDa() {
        return dataUdDa;
    }

    public void setDataUdDa(Date dataUdDa) {
        this.dataUdDa = dataUdDa;
    }

    public Date getDataUdA() {
        return dataUdA;
    }

    public void setDataUdA(Date dataUdA) {
        this.dataUdA = dataUdA;
    }

    public String getTipoUd() {
        return tipoUd;
    }

    public void setTipoUd(String tipoUd) {
        this.tipoUd = tipoUd;
    }

    public String getCollegio() {
        return collegio;
    }

    public void setCollegio(String collegio) {
        this.collegio = collegio;
    }

    public String getNrgrealeDa() {
        return nrgrealeDa;
    }

    public void setNrgrealeDa(String nrgrealeDa) {
        this.nrgrealeDa = nrgrealeDa;
    }

    public String getNrgrealeA() {
        return nrgrealeA;
    }

    public void setNrgrealeA(String nrgrealeA) {
        this.nrgrealeA = nrgrealeA;
    }

    public String getCfPresidente() {
        return cfPresidente;
    }

    public void setCfPresidente(String cfPresidente) {
        this.cfPresidente = cfPresidente;
    }

    public String getCfEstensore() {
        return cfEstensore;
    }

    public void setCfEstensore(String cfEstensore) {
        this.cfEstensore = cfEstensore;
    }

    public Date getDataMinutaDa() {
        return dataMinutaDa;
    }

    public void setDataMinutaDa(Date dataMinutaDa) {
        this.dataMinutaDa = dataMinutaDa;
    }

    public Date getDataMinutaA() {
        return dataMinutaA;
    }

    public void setDataMinutaA(Date dataMinutaA) {
        this.dataMinutaA = dataMinutaA;
    }

    public Date getDataPubblicazioneDa() {
        return dataPubblicazioneDa;
    }

    public void setDataPubblicazioneDa(Date dataPubblicazioneDa) {
        this.dataPubblicazioneDa = dataPubblicazioneDa;
    }

    public Date getDataPubblicazioneA() {
        return dataPubblicazioneA;
    }

    public void setDataPubblicazioneA(Date dataPubblicazioneA) {
        this.dataPubblicazioneA = dataPubblicazioneA;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public Boolean getRemoveRiuniti() {
        return removeRiuniti;
    }

    public void setRemoveRiuniti(Boolean removeRiuniti) {
        this.removeRiuniti = removeRiuniti;
    }
}
