package it.netservice.csp.backend.rest.auth;

import java.util.Date;

import javax.enterprise.context.ApplicationScoped;

import org.apache.log4j.Logger;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;

import it.netservice.penale.model.sic.Utente;

/**
 * <AUTHOR>
 */
@ApplicationScoped
public class TokenManager {

    private static final Logger LOGGER = Logger.getLogger(TokenManager.class);
    private final String minutiSessioneCscClient = System.getProperty("minutiSessioneCscClient", "480");
    private Algorithm algorithmHS = Algorithm.HMAC256("secret");
    private static final String ISSUER = "csp-backend";

    public TokenManager() {
    }

    public String createToken(Utente utente) {
        LOGGER.info("Generazione token per utente:" + utente.getIdUtente());
        Date expiration = new Date(new Date().getTime() + 1000l * 60l * Long.parseLong(minutiSessioneCscClient));
        Long[] sezioniArray = new Long[utente.getIdSezioni().size()];
        utente.getIdSezioni().toArray(sezioniArray);
        String[] sigleSezioniArray = new String[utente.getSigleSezioni().size()];
        utente.getSigleSezioni().toArray(sigleSezioniArray);
        return JWT.create().withIssuer(ISSUER).withSubject(utente.getIdentificativo()).withClaim("nome", utente.getNome())
                .withClaim("cognome", utente.getCognome()).withClaim("idSezione", utente.getSezione().getIdParam())
                .withClaim("idSezioneRG", utente.getIdSezioneRG()).withArrayClaim("idSezioni", sezioniArray)
                .withClaim("siglaSezione", utente.getSezione().getSigla()).withClaim("idProfilo", utente.getIdProfilo())
                .withArrayClaim("sigleSezioni", sigleSezioniArray).withClaim("idUtente", utente.getIdUtente())
                .withClaim("sentenzaMinuta", utente.isSentenzaMinuta()).withClaim("depositoSentenza", utente.isDepositoSentenza())
                .withClaim("numRaccGenerale", utente.isNumRaccGenerale()).withClaim("gestionePagamenti", utente.isGestionePagamenti())
                .withClaim("verificaDati", utente.isVerificaDati()).withClaim("verificaPagamenti", utente.isVerificaPagamenti())
                .withClaim("ruoloUdienza", utente.isRuoloUdienza()).withClaim("idSezioneSesta", utente.getIdSezioneSesta())
                .withClaim("codiceFiscale", utente.getCodiceFiscale()).withClaim("idProfilo", utente.getIdProfilo())
                .withClaim("admin", utente.isAdmin()).withClaim("configEnabled", utente.isConfigEnabled()).withExpiresAt(expiration)
                .withClaim("readOnly", utente.isReadOnly()).withNotBefore(new Date()).sign(algorithmHS);
    }

    public DecodedJWT verifyToken(String token) {
        try {
            JWTVerifier verifier = JWT.require(algorithmHS).withIssuer(ISSUER).build();
            return verifier.verify(token);
        } catch (JWTVerificationException jwte) {
            LOGGER.warn("Token non valido", jwte);
            return null;
        }
    }

}
