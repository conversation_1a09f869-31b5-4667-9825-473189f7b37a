/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package it.netservice.csp.backend.sic.service.depositi;

import it.netservice.csp.backend.excpetion.GestioneSicException;
import it.netservice.penale.model.enums.TipoOscuramentoPubblica;
import it.netservice.penale.model.enums.TipoProvvedimentoMagistrato;

/**
 *
 * <AUTHOR>
 */
public class PubblicazioneUtil {

    /**
     * Metodo per formattare un qualsiasi numero dalla forma padding a un più leggibile numero/anno
     *
     * @param numeroSIC
     *            numero recuperato da db con il padding
     * @param posizioneFineNumero
     *            punto in cui finisce il substring del numeroa
     * @return
     */

    public static String formatNumero(long numeroSIC, int posizioneFineNumero) {
        String res = "";
        if (numeroSIC > 0) {
            String anno = Long.toString(numeroSIC).substring(0, 4);
            // varia a seconda del numero da formattare, se è un Nrg la posizione è 10, se è sezionale è 9 perchè ha un padding diverso
            String numero = Long.valueOf(Long.toString(numeroSIC).substring(4, posizioneFineNumero)).toString();
            res = String.format("%s/%s", numero, anno);
        }
        return res;
    }

    public static Long mapTipoSent(TipoProvvedimentoMagistrato tipoVerificato) throws GestioneSicException {
        if (tipoVerificato == null) {
            throw new GestioneSicException("tipoVerificato null");
        }

        switch (tipoVerificato) {
            case Sentenza:
                return new Long(1040);
            case Ordinanza:
                return new Long(1041);
            default:
                throw new GestioneSicException("Tipo non supportato");
        }
    }

    public static Long mapOscuramento(TipoOscuramentoPubblica oscuramentoDati) {
        if (TipoOscuramentoPubblica.Nessuno.equals(oscuramentoDati)) {
            return new Long(187018);
        } else {
            return new Long(187017);
        }
    }

    public static boolean hasOscurato(TipoOscuramentoPubblica oscuramentoDati) {
        return oscuramentoDati != null && !TipoOscuramentoPubblica.Nessuno.equals(oscuramentoDati);
    }
}
