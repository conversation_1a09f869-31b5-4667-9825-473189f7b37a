plugins {
    id "org.sonarqube" version "4.0.0.2929"
    id "netservice" version "${nsPluginVersion}"
    id 'org.springframework.boot' version '2.7.9'
}

allprojects {
    group = 'products.cassazione.csp.backend'
}
subprojects {
    apply plugin: 'maven-publish'
    plugins.withType(JavaPlugin) {
        sourceCompatibility = 1.7
        targetCompatibility = 1.7

        dependencies {
            // fornite da JBoss
            implementation 'org.json:json:20210307'
            compileOnly 'org.hibernate:hibernate-core:4.0.1.Final'
            compileOnly 'javax.persistence:javax.persistence-api:2.2'
            compileOnly 'javax.annotation:javax.annotation-api:1.2'
            compileOnly "org.codehaus.jackson:jackson-core-asl:1.9.2"
            compileOnly "org.codehaus.jackson:jackson-mapper-asl:1.9.2"
            compileOnly 'org.jboss.resteasy:resteasy-multipart-provider:2.3.2.Final'
            compileOnly "javax.servlet:javax.servlet-api:3.0.1"
            compileOnly 'javax.enterprise:cdi-api:1.0-SP4'

            // altro (tutto compileonly perchè le lib da includere nell'artefatto sono definite nel build.gradle dell'ear
            compileOnly 'libraries.adn-client:adn-client:1.0.0'
            compileOnly 'javax.enterprise:cdi-api:1.0-SP4'
            compileOnly "libraries.common-rest:common-rest:${commonRestVersion}"
            compileOnly 'commons-beanutils:commons-beanutils:1.9.4'
            compileOnly 'org.apache.commons:commons-digester3:3.2'
            compileOnly 'org.apache.poi:poi:3.14'
            compileOnly 'org.apache.poi:poi-ooxml:3.14'
            compileOnly 'com.google.code.gson:gson:2.8.6'
            compileOnly group: 'libraries.nscommon', name: 'ns-common', version: '1.5.0'
            compileOnly group: 'com.auth0', name: 'java-jwt', version: '3.9.0', transitive: false
            // !!! questa dipende da jackson-core che potrebbe andare in conflito con quello deploiato su JBoss
            compileOnly group: 'com.squareup.okhttp3', name: 'okhttp', version: '3.12.10'
            compileOnly group: 'org.apache.axis', name: 'axis', version: '1.4'
            compileOnly group: 'org.apache.axis', name: 'axis-jaxrpc-no-qname', version: '1.4'
            compileOnly group: 'commons-discovery', name: 'commons-discovery', version: '0.2'
            compileOnly group: 'wsdl4j', name: 'wsdl4j', version: '1.4'
            compileOnly group: 'it.netservice.csc.documentale', name: 'csc-documentale', version: '1.0.0'
            compileOnly group: 'libraries.cassazione.sic-model-penale', name: 'sic-model-penale', version: sicPenaleModelVersion
            compileOnly group: 'libraries.cassazione.sic-common', name: 'sic-common', version: '2.0.0'
			compileOnly group: 'org.apache.commons', name: 'commons-text', version: '1.1'


            testImplementation 'junit:junit:4.12'
            testImplementation 'commons-io:commons-io:2.6'
            testImplementation 'commons-beanutils:commons-beanutils:1.9.4'
            testImplementation 'org.apache.commons:commons-digester3:3.2'
            testImplementation "libraries.common-rest:common-rest:${commonRestVersion}"
            testImplementation 'org.hibernate:hibernate-core:4.0.1.Final'
            testImplementation 'javax.persistence:javax.persistence-api:2.2'
            testImplementation 'javax.annotation:javax.annotation-api:1.2'
            testImplementation group: 'libraries.cassazione.sic-model-penale', name: 'sic-model-penale', version: sicPenaleModelVersion
        }
    }
}
