package it.netservice.csp.backend.rest.auth;

import java.util.List;

import javax.inject.Inject;
import javax.ws.rs.WebApplicationException;
import javax.ws.rs.ext.Provider;

import org.apache.log4j.Logger;
import org.jboss.resteasy.annotations.interception.ServerInterceptor;
import org.jboss.resteasy.core.ResourceMethod;
import org.jboss.resteasy.core.ServerResponse;
import org.jboss.resteasy.spi.Failure;
import org.jboss.resteasy.spi.HttpRequest;
import org.jboss.resteasy.spi.UnauthorizedException;
import org.jboss.resteasy.spi.interception.PreProcessInterceptor;

import com.auth0.jwt.interfaces.DecodedJWT;

/**
 *
 * <AUTHOR>
 */
@Provider
@ServerInterceptor
public class SecurityInterceptor implements PreProcessInterceptor {

    private static final Logger LOGGER = Logger.getLogger(SecurityInterceptor.class);

    @Inject
    private TokenManager tokenManager;

    @Override
    public ServerResponse preProcess(HttpRequest request, ResourceMethod resourceMethod) throws Failure, WebApplicationException {
        if (resourceMethod.getResourceClass().isAnnotationPresent(NoSicAuthService.class)) {
            return null;
        }
        List<String> authHeaders = request.getHttpHeaders().getRequestHeader("x-auth-token");
        if (authHeaders == null || authHeaders.isEmpty() || authHeaders.get(0) == null || authHeaders.get(0).isEmpty()) {
            throw new UnauthorizedException("Token mancante");
        }
        DecodedJWT jwt = tokenManager.verifyToken(authHeaders.get(0));
        if (jwt == null) {
            throw new UnauthorizedException("Token non valido");
        }
        LOGGER.debug("Utente " + jwt.getSubject() + " valido");

        return null;
    }
}
