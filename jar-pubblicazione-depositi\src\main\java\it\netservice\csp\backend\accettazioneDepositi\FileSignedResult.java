package it.netservice.csp.backend.accettazioneDepositi;

import it.netservice.csc.backend.ws.gestioneatti.types.IdAtto;

public class FileSignedResult {
    IdAtto idAttoControfirmatoOscurato;
    IdAtto idAttoControfirmato;
    IdAtto idAttoControfirmatoXML;

    public IdAtto getIdAttoControfirmatoOscurato() {
        return idAttoControfirmatoOscurato;
    }

    public void setIdAttoControfirmatoOscurato(IdAtto idAttoControfirmatoOscurato) {
        this.idAttoControfirmatoOscurato = idAttoControfirmatoOscurato;
    }

    public IdAtto getIdAttoControfirmato() {
        return idAttoControfirmato;
    }

    public void setIdAttoControfirmato(IdAtto idAttoControfirmato) {
        this.idAttoControfirmato = idAttoControfirmato;
    }

    public IdAtto getIdAttoControfirmatoXML() {
        return idAttoControfirmatoXML;
    }

    public void setIdAttoControfirmatoXML(IdAtto idAttoControfirmatoXML) {
        this.idAttoControfirmatoXML = idAttoControfirmatoXML;
    }
}
