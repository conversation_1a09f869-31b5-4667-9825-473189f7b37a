/.gradle/
/.project
.idea
.gradle
.settings
.classpath
.project
.class
*.iml
*.class
*.ear
/.nb-gradle/
/build/
.nb-gradle-properties
/distribution/patch/build/

/war-cscbackend/build/*
/war-cspbackend/build/*
/distribution/build/
docker/csc-backend/temp
docker/csp-backend/temp
war-cscbackend/build/
war-cscbackend/private/
war-cspbackend/build/*
war-cspbackend/private/
*/bin/*
.vscode/settings.json
/jar-servizi-ext/build/
/jar-gestione-sic/build/
/jar-servizi-ext - Copia/build/
/jar-csc-model/build/
/jar-csc-model - Copia/build/
/jar-csc-common/build/
/jar-csp-model/build/
/jar-csp-model - Copia/build/
/jar-csp-common/build/
/jar-proposte-accelerate/build

/war-cscbackend - Copia/build/
/war-cspbackend - Copia/build/
/ear-cscbackend/build/
/ear-cspbackend/build/
jar-cscbackend/build/
/jar-cspbackend/build/
/jar-csc-common - Copia/build/
/ear-cspbackend/build/
jar-cspbackend/build/

/jar-csp-common - Copia/build/
/jar-elaborazione-depositi/build/
/jar-servizi-ext/private/

/jar-firma-remota/build/
/jar-pubblicazione-depositi/build/
/jar-ucu-model/build/
/jar-persistence-utils/build/
/jar-gestione-pdf/build/
/jar-procura/build/
/war-cspbackend-springboot/build/
docker/csp-backend-springboot/temp
war-cspbackend-springboot/build/
/war-cspbackend-springboot/build/
war-cspbackend-springboot/build/*
war-cspbackend/build/
/war-cspbackend/build/
/war-cspbackend/build
war-cspbackend/build/libs/*
war-cspbackend-springboot/private/
/ear-cspbackend/build/
/war-cspbackend/build/
./history
./.idea
.history/