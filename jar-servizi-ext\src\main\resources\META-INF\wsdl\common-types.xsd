<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns="http://www.giustizia.it/gl/common/types" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.giustizia.it/gl/common/types" elementFormDefault="unqualified" attributeFormDefault="unqualified">
  <xs:import namespace="http://schemas.xmlsoap.org/soap/encoding/" schemaLocation="soap-encoding.xsd"/>
  <xs:complexType name="DeliveryInfo">
    <xs:all>
      <xs:element name="destination"   type="xs:string"   nillable="false"/>
      <xs:element name="msgId"         type="xs:string"   nillable="true"/>
      <xs:element name="date"          type="xs:dateTime" nillable="true"/>
      <xs:element name="catId"         type="xs:string"   nillable="true"/>
      <xs:element name="deliveryError" type="xs:boolean"  nillable="true"/>
    </xs:all>
  </xs:complexType>
  <xs:complexType name="ContentInfo">
    <xs:all>
      <xs:element name="sender" type="xs:string" nillable="false"/>
      <xs:element name="catId" type="xs:string" nillable="false"/>
      <xs:element name="parentId" type="xs:string" nillable="true"/>
      <xs:element name="docType" type="xs:string" nillable="true"/>
      <xs:element name="filename" type="xs:string" nillable="false"/>
      <xs:element name="mimeType" type="xs:string" nillable="false"/>
      <xs:element name="docDate" type="xs:dateTime" nillable="false"/>
      <xs:element name="size" type="xs:long"/>
      <xs:element name="attachments">
        <xs:complexType>
          <xs:complexContent>
            <xs:restriction base="soapenc:Array">
              <xs:attribute ref="soapenc:arrayType" wsdl:arrayType="ContentInfo[]"/>
            </xs:restriction>
          </xs:complexContent>
        </xs:complexType>
      </xs:element>
      <xs:element name="deliveryInfos">
        <xs:complexType>
          <xs:complexContent>
            <xs:restriction base="soapenc:Array">
              <xs:attribute ref="soapenc:arrayType" wsdl:arrayType="DeliveryInfo[]"/>
            </xs:restriction>
          </xs:complexContent>
        </xs:complexType>
      </xs:element>
    </xs:all>
  </xs:complexType>
  <xs:complexType name="IdFascicolo">
    <xs:all>
      <xs:element name="anno" type="xs:string" nillable="true"/>
      <xs:element name="numero" type="xs:string" nillable="true"/>
      <xs:element name="subProcedimento" type="xs:string" nillable="true"/>
      <xs:element name="registro" type="xs:string" nillable="false"/>
      <xs:element name="ufficio" type="xs:string" nillable="false"/>
      <xs:element name="dataUltimaModifica" type="xs:dateTime" nillable="true"/>
      <xs:element name="attorePrincipale" type="xs:string" nillable="true"/>
      <xs:element name="convenutoPrincipale" type="xs:string" nillable="true"/>
      <xs:element name="idSgr" type="xs:string" nillable="true"/>
      <xs:element name="idRepository" type="xs:string" nillable="true"/>
    </xs:all>
  </xs:complexType>
  <xs:complexType name="IdAtto">
    <xs:all>
      <xs:element name="id" type="xs:string" nillable="true"/>
      <xs:element name="fascicolo" type="IdFascicolo" nillable="false"/>
    </xs:all>
  </xs:complexType>
</xs:schema>
