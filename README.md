# CSP-Backend

Parte backend che espone i servizi per il CSP-Client

E' deploiato su JBoss AS 7.

Si interfaccia con:
 - schema MIGRA del SIC-Penale
 - GL-Cass per la parte dei depositi
 - ADN per reperire l'accesso gli utenti Magistrato
 - Reginde per reperire l'accesso gli utenti avvocati
  
 
## Docker

All'interno della cartella docker sono presenti file bat e di configurazione per avviare il csp-backend all' interno di un container locale che espone i servizi su http://localhost:8086

## Informazioni tecniche e funzionali

### Servizio Ricerca Ricorsi

Il servizio consente di effettuare ricerche per recuperare ricorsi, sia all'interno che all'esterno di un'udienza. Per tale scopo vengono utilizzate due viste (il motivo è dovuto alla presenza di due chiavi differenti):
* `V_RICERCA_RICORSI_BASE_IN`: contiene tutti i ricorsi che si trovano in un'Udienza, possono essere presenti anche più record per ciascun ricorso. La chiave è costituita dalle colonne `NRG`, `ID_UDIEN` e `ID_RICUDIEN`. 
* `V_RICERCA_RICORSI_BASE_OUT`: contiene tutti i ricorsi non associati ad alcuna Udienza, in tal caso esisterà un solo record per ciascun ricorso e la chiave è costituita dalla colonna `NRG`.

La ricerca richiede uno dei seguenti parametri obbligatori (ai quali possono essere aggiunti anche altri parametri opzionali):
* Data Udienza: viene indicata la data dell'Udienza, verranno quindi restituiti i fascicoli associati ad un'Udienza per la data indicata. La query di ricerca viene eseguita solo nella vista `V_RICERCA_RICORSI_BASE_IN`
  * Da notare che per questa tipologia di ricerca viene anche verificato se il fascicolo è stato tolto o sospeso dal ruolo (eseguendo una ricerca per nrg e per l'apposita funzione nella vista `PENALE_STORIA`), se la condizione è verificata il ricorso viene rimosso dalla lista dei risultati.


* Numero e Anno: la ricerca viene effettuata cercando per numero e anno del ricorso. La ricerca viene eseguita prima nella vista `V_RICERCA_RICORSI_BASE_IN`, nel caso in cui non sono stati trovati risultati viene effettuata una ricerca anche all'interno della vista `V_RICERCA_RICORSI_BASE_OUT`.


