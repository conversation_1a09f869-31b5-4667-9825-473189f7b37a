package it.netservice.csp.backend.service.impostazioni;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.apache.log4j.Logger;

import it.netservice.penale.model.csp.Impostazioni;

public class ImpostazioniService {
    private static final Logger LOGGER = Logger.getLogger(ImpostazioniService.class);

    public ImpostazioniService() {
    }

    public Impostazioni getImpostazioniByCf(EntityManager entityManager, String codiceFiscale) {
        Query query = entityManager.createNativeQuery("SELECT * FROM SETTINGS WHERE CF_UTENTE = :codiceFiscale", Impostazioni.class);

        query.setParameter("codiceFiscale", codiceFiscale);

        List<Impostazioni> result = (List<Impostazioni>) query.getResultList();

        if (result != null && result.size() > 0) {
            return result.get(0);
        } else {
            return null;
        }
    }

}
