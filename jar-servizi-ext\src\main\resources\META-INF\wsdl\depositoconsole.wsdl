<definitions xmlns="http://schemas.xmlsoap.org/wsdl/"
	     xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
	     xmlns:http="http://schemas.xmlsoap.org/wsdl/http/"
	     xmlns:xs="http://www.w3.org/2001/XMLSchema"
	     xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/"
	     xmlns:apachesoap="http://xml.apache.org/xml-soap"
	     xmlns:ns="urn:DepositoConsole"
	     targetNamespace="urn:DepositoConsole">

    <message name="DepositoConsoleRequest">
	<part name="InfoInoltro" type="apachesoap:DataHandler"/>
	<part name="Atto" type="apachesoap:DataHandler"/>
	<part name="token" type="xs:string"/>
    </message>
    <message name="DepositoConsoleResponse">
	<part name="DepositoConsoleResult" type="xs:string"/>
    </message>
    <portType name="ServizioDepositoConsole">
	<operation name="DepositoConsole">
	    <input message="ns:DepositoConsoleRequest"/>
	    <output message="ns:DepositoConsoleResponse"/>
	</operation>
    </portType>
    <binding name="DepositoConsoleBinding" type="ns:ServizioDepositoConsole">
	<soap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http"/>
	<operation name="DepositoConsole">
	    <soap:operation soapAction=""/>
	    <input name="DepositoConsoleRequest">
		<soap:body use="encoded" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
	    </input>
	    <output name="DepositoConsoleResponse">
		<soap:body use="encoded" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"/>
	    </output>
	</operation>
    </binding>
    <service name="wsDepositoConsole">
	<port name="ServizioDepositoConsole" binding="ns:DepositoConsoleBinding">
	    <soap:address location="http://localhost:8080/Receiver/internal"/>
	</port>
    </service>
</definitions>
