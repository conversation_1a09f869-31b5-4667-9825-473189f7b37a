/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package it.netservice.csp.backend.elaborazioneDepositi.service;

import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.List;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;
import javax.persistence.Query;
import javax.xml.rpc.ServiceException;

import org.apache.log4j.Logger;

import it.netservice.csp.backend.depositi.ContentDeposito;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.DepositoException;
import it.netservice.csp.backend.ext.service.ServiceBuilder;
import it.netservice.csp.backend.ws.depositi.ContentSummaryType;
import it.netservice.csp.backend.ws.depositi.EnvSummaryType;
import it.netservice.csp.backend.ws.depositi.LogEsiti;
import it.netservice.penale.model.common.dettaglioricorso.ProvvedimentoPenale;
import it.netservice.penale.model.common.dettaglioricorso.StoricoProvvedimento;
import it.netservice.penale.model.enums.StatoProvvedimento;
import it.netservice.penale.model.sic.Deposito;
import it.netservice.penale.model.sic.ElaborazioneDeposito;

/**
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class DepositoService {

    private static final Logger LOGGER = Logger.getLogger(DepositoService.class);

    @Inject
    private ElaborazioneDepositoService elaborazioneDepositoService;

    public static List<ContentDeposito> getContentDeposito(ServiceBuilder serviceBuilder, String idDeposito)
            throws CspBackendException, RemoteException, MalformedURLException, ServiceException {
        LOGGER.info("Recupero informazioni per deposito " + idDeposito);
        List<ContentDeposito> result = new ArrayList<>();
        if (idDeposito == null) {
            throw new CspBackendException("Riferimento deposito non corretto. idDeposito null", RestErrorCodeEnum.ID_DEPOSITO_NULL, 433);
        }
        LogEsiti service = serviceBuilder.getLogEsitiService();
        EnvSummaryType[] events = service.querySummary(new String[] { idDeposito }).getEnvSummary();
        if (events == null || events.length == 0) {
            LOGGER.error("Deposito non trovato sul GL. idDeposito:" + idDeposito);
            throw new CspBackendException("Deposito non trovato sul GL", RestErrorCodeEnum.GL_DEPOSITO_NOT_FOUND, 433);
        }
        EnvSummaryType ev = events[0];
        if (ev.getContents() != null && ev.getContents().getContentSummary() != null) {
            for (ContentSummaryType content : ev.getContents().getContentSummary()) {
                ContentDeposito contentDeposito = new ContentDeposito();
                contentDeposito.setCatId(content.getCatId());
                contentDeposito.setNome(content.getName());
                contentDeposito.setTipo(content.getDocType());
                contentDeposito.setStatus(content.getStatus());
                result.add(contentDeposito);
            }
        }
        LOGGER.info("Recupero informazioni per deposito " + idDeposito + " completato");
        return result;

    }

    public static List<ContentDeposito> getContentDeposito(ServiceBuilder serviceBuilder, String idDeposito, String tipo)
            throws CspBackendException, RemoteException, MalformedURLException, ServiceException {
        List<ContentDeposito> content = getContentDeposito(serviceBuilder, idDeposito);
        List<ContentDeposito> result = new ArrayList<>();

        for (ContentDeposito c : content) {
            if (tipo.equals(c.getTipo())) {
                result.add(c);
            }
        }
        return result;
    }

    public int countDepositiInCarico(EntityManager entityManager, Long idUtente) {
        Query query = entityManager.createNativeQuery(
                "select count (*) from CSPBACKEND_DEPOSITI where idutente=" + idUtente.toString() + " AND stato = 'NEW'");
        return ((BigDecimal) query.getSingleResult()).intValue();
    }

    public void prendiInCaricoMultipli(EntityManager entityManager, List<Long> idDepositi, Long idUtente) throws Exception {
        EntityTransaction transaction = null;
        try {
            LOGGER.info("Presa in carico multipla di " + idDepositi.size() + " depositi da utente " + idUtente);
            for (Long idDeposito : idDepositi) {
                Deposito deposito = entityManager.find(Deposito.class, idDeposito);
                if (deposito.getIdUtente() != null) {
                    LOGGER.error("Il deposito è già stato preso in carico. idDeposito:" + idDeposito);
                    throw new DepositoException("Il deposito è già stato preso in carico", RestErrorCodeEnum.DEPOSITO_GIA_ASSEGNATO);
                }
                if (!deposito.getStato().equals(Deposito.Stato.NEW)) {
                    LOGGER.error("Presa in carico possibile solo per i nuovi depositi. idDeposito:" + idDeposito);
                    throw new DepositoException("Presa in carico possibile solo per i nuovi depositi",
                            RestErrorCodeEnum.PRESA_IN_CARICO_DEPOSITO_ONLY_NEW);
                }
            }
            transaction = entityManager.getTransaction();
            transaction.begin();

            for (Long idDeposito : idDepositi) {
                Deposito deposito = entityManager.find(Deposito.class, idDeposito);
                String tipoDeposito = deposito.getTipo().toLowerCase();
                checkHasBeenValorisedByTheSic(entityManager, tipoDeposito, deposito.getIdCat());
                deposito.setIdUtente(idUtente);
                deposito.setDataIncarico(System.currentTimeMillis());
                entityManager.merge(deposito);
            }

            transaction.commit();
            LOGGER.info("Depositi presi in carico con successo da operatore " + idUtente);
        } catch (DepositoException ex) {
            throw ex;
        } catch (Throwable ex) {
            if (transaction != null && transaction.isActive()) {
                transaction.rollback();
                LOGGER.error("Errore durante la presa in carico multipla dei depositi");
                throw new DepositoException("Errore durante la presa in carico multipla", RestErrorCodeEnum.PRESA_IN_CARICO_GENERIC_ERROR,
                        ex);
            }
            throw ex;
        }
    }

    public void prendiInCarico(EntityManager entityManager, Long idDeposito, Long idUtente) throws Exception {
        EntityTransaction transaction = null;
        try {
            LOGGER.info("Presa in carico deposito " + idDeposito + " da utente " + idUtente);
            transaction = entityManager.getTransaction();
            transaction.begin();
            Deposito deposito = entityManager.find(Deposito.class, idDeposito);
            if (!deposito.getStato().equals(Deposito.Stato.NEW)) {
                LOGGER.error("Presa in carico possibile solo per i nuovi depositi. idDeposito:" + idDeposito);
                throw new DepositoException("Presa in carico possibile solo per i nuovi depositi",
                        RestErrorCodeEnum.PRESA_IN_CARICO_DEPOSITO_ONLY_NEW);
            }
            String tipoDeposito = deposito.getTipo().toLowerCase();
            checkHasBeenValorisedByTheSic(entityManager, tipoDeposito, deposito.getIdCat());
            deposito.setIdUtente(idUtente);
            deposito.setDataIncarico(System.currentTimeMillis());
            entityManager.merge(deposito);
            transaction.commit();

            LOGGER.info("Deposito " + idDeposito + " preso in carico da operatore " + idUtente);
        } catch (Throwable ex) {
            if (transaction != null && transaction.isActive()) {
                transaction.rollback();
                LOGGER.error("Errore durante la presa in carico del deposito. idDeposito:" + idDeposito);
                throw new DepositoException("Errore durante la presa in carico", RestErrorCodeEnum.PRESA_IN_CARICO_GENERIC_ERROR, ex);
            }
        }
    }

    /**
     * Verifica se il deposito è stato pubblicato o depositato dal SIC.
     *
     * @param entityManager
     *            entity manager dove eseguire la transazione
     * @param idCat
     *            numero deposito del deposito
     * @param tipoDeposito
     *            il tipo di deposito (ordinanza, sentenza, minuta ordinanza, minuta sentenza)
     * @throws CspBackendException
     *             se il deposito è stato pubblicato o depositato dal SIC.
     */
    public void checkHasBeenValorisedByTheSic(EntityManager entityManager, String tipoDeposito, Long idCat) throws CspBackendException {
        LOGGER.info("Avvio verifica che il eposito " + idCat + " non sia stato pubblicato/depositato da SIC");
        Query query = entityManager.createNativeQuery(
                "SELECT " + "(CASE WHEN pts.DATAMINUTA IS NOT NULL AND DEPOSITO_TELEMATICO = 0 THEN 'S' ELSE 'N' END) AS DEPOSITATA, "
                        + "(CASE WHEN pts.DATAPUBBL IS NOT NULL  AND DEPOSITO_TELEMATICO = 0 THEN 'S' ELSE 'N' END) AS PUBBLICATA,"
                        + "pp.ID_UDIEN AS id_udien," + "pp.NRG  AS nrg FROM PENALE_T_RICUDIEN pr "
                        + "JOIN PENALE_ESITO pte ON (pr.ID_RICUDIEN  = pte.ID_RICUDIEN  ) "
                        + "JOIN PENALE_ESITOSENT pes ON (pte.ID_ESITO = pes.ID_ESITO)"
                        + "JOIN PENALE_T_SENTENZA pts ON (pes.ID_SENT  = pts.ID_SENT)"
                        + "JOIN PENALE_PROVVEDIMENTI pp ON (pr.nrg = pp.NRG and pr.ID_UDIEN = pp.ID_UDIEN)" + "WHERE pp.FK_IDCAT = :IDCAT");
        query.setParameter("IDCAT", idCat);
        Object[] singleResult = (Object[]) query.getSingleResult();
        if (singleResult != null) {
            if ((tipoDeposito.equalsIgnoreCase("sentenza") || tipoDeposito.equalsIgnoreCase("ordinanza")) && singleResult[1].equals('S')) {
                LOGGER.error("Il deposito è stato pubblicato dal SIC. idCat" + idCat);
                throw new DepositoException("Il deposito è stato pubblicato dal SIC.", RestErrorCodeEnum.DEPOSITO_PUBBLICATO_SIC);
            }
            if ((tipoDeposito.equalsIgnoreCase("MinutaOrdinanza") || tipoDeposito.equalsIgnoreCase("MinutaSentenza"))
                    && singleResult[0].equals('S')) {
                if (singleResult[1].equals('S')) {
                    LOGGER.error("Il deposito è stato pubblicato dal SIC. idCat" + idCat);
                    throw new DepositoException("Il deposito è stato pubblicato dal SIC.", RestErrorCodeEnum.DEPOSITO_PUBBLICATO_SIC);
                }
                Long idUdien = ((BigDecimal) singleResult[2]).longValue();
                Long nrg = ((BigDecimal) singleResult[3]).longValue();
                List<StoricoProvvedimento> resultList = this.getStoricoProvvedimento(getIdProvvByNrg(entityManager, nrg, idUdien),
                        entityManager);
                for (StoricoProvvedimento pro : resultList) {
                    if (pro.getStato() != null && (StatoProvvedimento.BUSTA_RIFIUTATA.equals(pro.getStato())
                            || StatoProvvedimento.MINUTA_DA_MODIFICARE.equals(pro.getStato())
                            || StatoProvvedimento.MINUTA_MODIFICATA.equals(pro.getStato()))) {
                        LOGGER.info("Deposito " + idCat + " non è stato pubblicato/depositato da SIC");
                        return;
                    }

                }
                LOGGER.error("Il deposito è stato depositato dal SIC. idCat" + idCat);
                throw new DepositoException("Il deposito è stato depositato dal SIC.", RestErrorCodeEnum.DEPOSITO_DEPOSITATO_SIC);
            }
        }

    }

    public static List<StoricoProvvedimento> getStoricoProvvedimento(String idProvv, EntityManager entityManager) {
        Query query = entityManager.createNativeQuery(
                "SELECT ppcs.* FROM PENALE_PROVV_CHANGE_STATUS ppcs WHERE ppcs.ID_PROVV IN (SELECT *"
                        + "   FROM TABLE(get_provvedimenti_collegati(:idProvv))) ORDER BY ppcs.DATE_CHANGE DESC",
                StoricoProvvedimento.class);
        query.setParameter("idProvv", idProvv);
        List<StoricoProvvedimento> resultList = query.getResultList();
        return resultList;
    }

    public String getIdProvvByNrg(EntityManager entityManager, Long nrg, Long idUdien) {
        Query query = entityManager.createNativeQuery(
                "SELECT * FROM PENALE_PROVVEDIMENTI pp WHERE NRG = :nrg and ID_UDIEN=:ID_UDIEN ORDER BY DATA_ULTIMA_MODIFICA DESC ",
                ProvvedimentoPenale.class);
        query.setParameter("nrg", nrg);
        query.setParameter("ID_UDIEN", idUdien);
        List<ProvvedimentoPenale> result = query.getResultList();
        if (result != null && result.size() > 0) {
            return result.get(0).getIdProvv();
        }
        return null;
    }

    public void rimuoviInCarico(EntityManager entityManager, Long idUtente) throws Exception {
        LOGGER.info("Rimozione dei depositi in carico all'utente " + idUtente);
        EntityTransaction transaction = null;
        try {
            transaction = entityManager.getTransaction();
            transaction.begin();
            Query query = entityManager.createNativeQuery(
                    "update CSPBACKEND_DEPOSITI set idutente=null where idutente=" + idUtente.toString() + " AND stato = 'NEW'");
            query.executeUpdate();
            transaction.commit();
            LOGGER.info("Depositi in carico all'utente " + idUtente + " rimossi");
        } catch (Throwable ex) {
            if (transaction != null && transaction.isActive()) {
                transaction.rollback();
                LOGGER.error("Errore durante la rimozione della presa in carico dell'utente. idUtente: " + idUtente);
                throw new DepositoException("Errore durante la rimozione della presa in carico dell'utente",
                        RestErrorCodeEnum.DEPOSITO_GENERIC_ERROR, ex);
            }
        }

    }

    public void modificaTipoDeposito(EntityManager entityManager, Long idDeposito, String tipoDeposito) throws Exception {
        LOGGER.info("Inizio modifica per deposito " + idDeposito + " a tipo " + tipoDeposito);
        EntityTransaction transaction = null;
        try {
            transaction = entityManager.getTransaction();
            transaction.begin();
            Deposito deposito = entityManager.find(Deposito.class, idDeposito);
            if (!deposito.getStato().equals(Deposito.Stato.NEW)) {
                LOGGER.error(
                        "Modifica tipo possibile solo per i nuovi depositi. idDeposito:" + idDeposito + ", stato:" + deposito.getStato());
                throw new DepositoException("Modifica tipo possibile solo per i nuovi depositi", RestErrorCodeEnum.DEPOSITO_NOT_NEW);
            }

            if (deposito.getTipoModificato() == null) {
                deposito.setTipoModificato(deposito.getTipo());
            }
            deposito.setTipo(tipoDeposito);
            entityManager.merge(deposito);

            ElaborazioneDeposito elabDep = elaborazioneDepositoService.findElaborazioneDepositoByIdDeposito(entityManager, idDeposito,
                    null);
            elaborazioneDepositoService.eliminaElaborazioneDeposito(entityManager, elabDep.getId(), false);

            transaction.commit();
        } catch (Throwable ex) {
            if (transaction != null && transaction.isActive()) {
                transaction.rollback();
                throw ex;
            }
        }
    }

    public Long getIdDepositoInCarico(EntityManager entityManager, Long idUtente) {
        Query query = entityManager.createNativeQuery("select IDCAT from CSPBACKEND_DEPOSITI where idutente=" + idUtente.toString());
        try {
            return ((BigDecimal) query.getSingleResult()).longValue();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Verifica se il deposito è stato lavorato da SIC.
     *
     * @param entityManager
     *            entity manager dove eseguire la query
     * @param tipoDeposito
     *            il tipo di deposito (ordinanza, sentenza, minuta ordinanza, minuta sentenza)
     * @param idCat
     *            numero deposito del deposito
     * @return true se il deposito è stato lavorato da SIC, false altrimenti
     */
    public boolean isDepositoLavoratoDaSIC(EntityManager entityManager, String tipoDeposito, Long idCat) {
        LOGGER.info("Verifica se il deposito " + idCat + " è stato lavorato da SIC");
        try {
            Query query = entityManager.createNativeQuery("SELECT "
                    + "(CASE WHEN pts.DATAMINUTA IS NOT NULL AND DEPOSITO_TELEMATICO = 0 THEN 'S' ELSE 'N' END) AS DEPOSITATA, "
                    + "(CASE WHEN pts.DATAPUBBL IS NOT NULL  AND DEPOSITO_TELEMATICO = 0 THEN 'S' ELSE 'N' END) AS PUBBLICATA,"
                    + "pp.ID_UDIEN AS id_udien," + "pp.NRG  AS nrg FROM PENALE_T_RICUDIEN pr "
                    + "JOIN PENALE_ESITO pte ON (pr.ID_RICUDIEN  = pte.ID_RICUDIEN  ) "
                    + "JOIN PENALE_ESITOSENT pes ON (pte.ID_ESITO = pes.ID_ESITO)"
                    + "JOIN PENALE_T_SENTENZA pts ON (pes.ID_SENT  = pts.ID_SENT)"
                    + "JOIN PENALE_PROVVEDIMENTI pp ON (pr.nrg = pp.NRG and pr.ID_UDIEN = pp.ID_UDIEN)" + "WHERE pp.FK_IDCAT = :IDCAT");
            query.setParameter("IDCAT", idCat);

            try {
                Object[] singleResult = (Object[]) query.getSingleResult();
                if (singleResult != null) {
                    // Per Sentenza o Ordinanza, verifica se è stata pubblicata
                    if ((tipoDeposito.equalsIgnoreCase("sentenza") || tipoDeposito.equalsIgnoreCase("ordinanza"))
                            && singleResult[1].equals('S')) {
                        LOGGER.info("Il deposito " + idCat + " è stato pubblicato dal SIC");
                        return true;
                    }

                    // Per MinutaOrdinanza o MinutaSentenza, verifica se è stata depositata o pubblicata
                    if ((tipoDeposito.equalsIgnoreCase("MinutaOrdinanza") || tipoDeposito.equalsIgnoreCase("MinutaSentenza"))
                            && singleResult[0].equals('S')) {
                        if (singleResult[1].equals('S')) {
                            LOGGER.info("Il deposito " + idCat + " è stato pubblicato dal SIC");
                            return true;
                        }

                        Long idUdien = ((BigDecimal) singleResult[2]).longValue();
                        Long nrg = ((BigDecimal) singleResult[3]).longValue();
                        String idProvv = getIdProvvByNrg(entityManager, nrg, idUdien);
                        List<StoricoProvvedimento> resultList = getStoricoProvvedimento(idProvv, entityManager);

                        for (StoricoProvvedimento pro : resultList) {
                            if (pro.getStato() != null && (StatoProvvedimento.BUSTA_RIFIUTATA.equals(pro.getStato())
                                    || StatoProvvedimento.MINUTA_DA_MODIFICARE.equals(pro.getStato())
                                    || StatoProvvedimento.MINUTA_MODIFICATA.equals(pro.getStato()))) {
                                LOGGER.info("Il deposito " + idCat + " non è stato lavorato da SIC");
                                return false;
                            }
                        }

                        LOGGER.info("Il deposito " + idCat + " è stato depositato dal SIC");
                        return true;
                    }
                }
            } catch (javax.persistence.NoResultException e) {
                LOGGER.info("Nessun risultato trovato per il deposito " + idCat);
            }

            return false;
        } catch (Exception e) {
            LOGGER.warn("Errore durante la verifica se il deposito è stato lavorato da SIC. idDeposito:" + idCat, e);
            return false;
        }
    }

}
