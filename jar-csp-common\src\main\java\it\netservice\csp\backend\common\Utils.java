package it.netservice.csp.backend.common;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class Utils {

    public static Date convertDateTimeToDate(Date dateTime) throws ParseException {
        SimpleDateFormat simpleDate = new SimpleDateFormat("dd-MM-yyyy");
        String dateString = simpleDate.format(dateTime);
        return simpleDate.parse(dateString);
    }

    public static Map<String, Integer> convertNrgRealeToAnnoAndNumero(Long nrgReale) {
        if (nrgReale != null && nrgReale > 0) {
            Map<String, Integer> result = new HashMap<>();
            Integer anno = Integer.valueOf(String.valueOf(nrgReale).substring(0, 4));
            Integer numero = Integer.valueOf(String.valueOf(nrgReale).substring(4, 10));
            result.put(Constats.KEY_ANNO, anno);
            result.put(Constats.KEY_NUMERO, numero);
            return result;
        }
        return Collections.emptyMap();
    }

    public static boolean isTrue(Boolean bool) {
        return bool != null && bool.booleanValue();
    }
}
