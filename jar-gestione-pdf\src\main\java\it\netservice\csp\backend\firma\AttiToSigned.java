package it.netservice.csp.backend.firma;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

public class AttiToSigned {
    InputStream originaleStream;
    File controFirmato;
    InputStream originaleStreamOscurato;

    File controFirmatoOscurato;

    InputStream originaleStreamXML;

    File controFirmatoXML;
    String username;
    String password;
    byte[] content;
    String fileName;
    byte[] contentOcurato;
    String fileNameOscurato;
    byte[] contentXML;
    String fileNameXML;

    String oneTimePassword;

    public InputStream getOriginaleStream() {
        return originaleStream;
    }

    public void setOriginaleStream(InputStream originaleStream) {
        this.originaleStream = originaleStream;
    }

    public File getControFirmato() {
        return controFirmato;
    }

    public void setControFirmato(File controFirmato) {
        this.controFirmato = controFirmato;
        this.controFirmato.deleteOnExit();
    }

    public InputStream getOriginaleStreamOscurato() {
        return originaleStreamOscurato;
    }

    public void setOriginaleStreamOscurato(InputStream originaleStreamOscurato) {
        this.originaleStreamOscurato = originaleStreamOscurato;
    }

    public File getControFirmatoOscurato() {
        return controFirmatoOscurato;
    }

    public void setControFirmatoOscurato(File controFirmatoOscurato) {
        this.controFirmatoOscurato = controFirmatoOscurato;
        this.controFirmatoOscurato.deleteOnExit();
    }

    public InputStream getOriginaleStreamXML() {
        return originaleStreamXML;
    }

    public void setOriginaleStreamXML(InputStream originaleStreamXML) {
        this.originaleStreamXML = originaleStreamXML;
    }

    public File getControFirmatoXML() {
        return controFirmatoXML;
    }

    public byte[] getContent() {
        return content;
    }

    public void setContent(byte[] content) {
        this.content = content;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) throws IOException {
        this.fileName = fileName;
        setControFirmato(File.createTempFile(fileName, ".pdf"));
    }

    public byte[] getContentOcurato() {
        return contentOcurato;
    }

    public void setContentOcurato(byte[] contentOcurato) {
        this.contentOcurato = contentOcurato;
    }

    public String getFileNameOscurato() {
        return fileNameOscurato;
    }

    public void setFileNameOscurato(String fileNameOscurato) throws IOException {
        this.fileNameOscurato = fileNameOscurato;

        setControFirmatoOscurato(File.createTempFile(fileNameOscurato, ".pdf"));
    }

    public byte[] getContentXML() {
        return contentXML;
    }

    public void setContentXML(byte[] contentXML) throws IOException {
        this.contentXML = contentXML;
        setControFirmatoXML(File.createTempFile(fileNameXML, ".pdf"));
    }

    public String getFileNameXML() {
        return fileNameXML;
    }

    public void setFileNameXML(String fileNameXML) {
        this.fileNameXML = fileNameXML;
    }

    public void setControFirmatoXML(File controFirmatoXML) {
        this.controFirmatoXML = controFirmatoXML;
        this.controFirmatoXML.deleteOnExit();

    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getOneTimePassword() {
        return oneTimePassword;
    }

    public void setOneTimePassword(String oneTimePassword) {
        this.oneTimePassword = oneTimePassword;
    }
}
