package it.netservice.csp.backend;

import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import javax.ws.rs.ext.Provider;

import org.apache.log4j.Logger;

import it.netservice.common.rest.CommonRestWarning;
import it.netservice.csp.backend.dto.RestError;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;

@Provider
public class CspCommonRestWarningHandler implements ExceptionMapper<CommonRestWarning> {
    private static final Logger LOGGER = Logger.getLogger(CspCommonRestWarningHandler.class);

    @Override
    public Response toResponse(CommonRestWarning ex) {
        RestError errorDto = new RestError(ex.getMessage(), RestErrorCodeEnum.CSP_COMMON_REST_WARNING, null, null);
        errorDto.setStatus(500);
        // You can place whatever logic you need here
        LOGGER.error(errorDto, ex);
        return Response.status(errorDto.getStatus()).entity(errorDto).header("Access-Control-Allow-Origin", "*")
                .header("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS")
                .header("Access-Control-Expose-Headers", "Content-Type, Content-Disposition").header("Access-Control-Allow-Headers",
                        "Origin, X-Requested-With, Content-Type, Content-Disposition, Accept, Authorization, x-auth-token")
                .build();
    }
}
