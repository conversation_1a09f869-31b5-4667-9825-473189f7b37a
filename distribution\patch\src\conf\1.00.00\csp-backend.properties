# SERVIZI REGINDE
reginde.interrogazione.interni.url=http://reginde.processotelematico.giustizia.it/ServiziInterrogazioneRegindeExt/ServiziInterrogazioneInterni

# SERVIZI PST-BE
pst-be.url=http://servizibe.processotelematico.giustizia.it/servizi/CatalogoServizi?wsdl

# PROXY CASSAZIONE
#proxyCass.url=https://***********/
proxyCass.url=https://***********/


# SERVIZI GL-CASS
gestorelocale.url=http://************/Cassazione/rpcrouter
gestorelocale.username=SIC
gestorelocale.password=XXXXXXXX
#Ufficio Collaudo
#gestorelocale.ufficio=99999CSCTE
#Esercizio
gestorelocale.ufficio=80417740588
gestorelocale.registro=CASSPENALE

# INTERROGAZIONE ADN COLLAUDO
#ldap.host=dccednaute001.usr.root.jus
#ldap.port=3268
#ldap.user=CN=DGSIA Desk Magistrato LDAP User,OU=Utenti e Gruppi applicativi,DC=usr,DC=root,DC=jus
#ldap.password=ScnG4rt6y8
#ldap.userBaseDnMagistrati=OU=Sfb DGMC Users,OU=Infra,DC=usr,DC=root,DC=jus

# INTERROGAZIONE ADN
#ldap.host=dcbalrmute002.usr.root.jus
ldap.host=dccednaute001.usr.root.jus
ldap.port=3268
ldap.user=CN=DGSIA Desk Magistrato LDAP User,OU=Utenti e Gruppi applicativi,DC=usr,DC=root,DC=jus
ldap.password=ScnG4rt6y8
#ldap.userBaseDnMagistrati=OU=Sfb DGMC Users,OU=Infra,DC=usr,DC=root,DC=jus
ldap.userBaseDnMagistrati=OU=PREORG,DC=usr,DC=root,DC=jus



# Servizi Desk
#servizi-desk.url=http://************:8080

show_sql=true

# FIRMA REMOTA
firma-remota.url=https://firmaremota.giustizia.it/FirmaRemota/v1/extra
jre8_path=/usr/lib/jvm/jre1.8.0_281

# DOCUMENTALE
documentale.endpoint=http://***********:8080/EfGiustizia/services/EfGiustiziaService
documentale.auth.codice=SIC
documentale.auth.password=XXXXX
documentale.auth.ufficio=101000001
documentale.auth.id.utente=111

documentale.ftp.host=************
documentale.ftp.port=21
documentale.ftp.rootuser=SIC
documentale.ftp.rootpwd=XXXX
documentale.ftp.bigthreshold=15000
#documentale.test=true
