package it.netservice.csp.backend.rest.impostazioni;

import javax.annotation.security.RolesAllowed;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.Response;

import org.apache.log4j.Logger;

import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.ext.service.ServiceBuilder;
import it.netservice.csp.backend.rest.AbstractCspBackendController;
import it.netservice.csp.backend.rest.auth.SecurityUtil;
import it.netservice.csp.backend.rest.auth.TokenManager;
import it.netservice.csp.backend.service.impostazioni.ImpostazioniService;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.penale.model.csp.Impostazioni;

/**
 * Servizi di ricerca ricorsi per il magistrato penale.
 */
@RolesAllowed({ "CSPCLIENT" })
@Path("/impostazioni")
public class ImpostazioniController extends AbstractCspBackendController {

    private static final Logger LOGGER = Logger.getLogger(ImpostazioniController.class);

    @Inject
    private ServiceBuilder serviceBuilder;

    @Inject
    private ImpostazioniService impostazioniService;

    @Inject
    private SecurityUtil util;

    @Inject
    private TokenManager tokenManager;

    @GET
    public Response getImpostazioniByCf(@Context HttpServletRequest context) throws CspBackendException {
        LOGGER.info("Recupero informazioni in impostazioni per l'utente");
        EntityManager entityManager = emfCaricamento.createEntityManager();
        SicPenaleRepository repo = new SicPenaleRepository(entityManager);
        try {
            String codiceFiscale = util.getCodiceFiscale(tokenManager.verifyToken(context.getHeader("x-auth-token")));
            if (codiceFiscale == null || codiceFiscale.equalsIgnoreCase("")) {
                throw new CspBackendException("Errore nel recupero del Token", RestErrorCodeEnum.TOKEN_ERROR, 433);
            }

            Impostazioni result = impostazioniService.getImpostazioniByCf(entityManager, codiceFiscale);
            LOGGER.info("Recupero informazioni in impostazioni per l'utente completato");
            return Response.ok(result).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante il recupero delle informazioni dell'utente");
            throw new CspBackendException("Errore durante il recupero delle informazioni dell'utente", RestErrorCodeEnum.TOKEN_ERROR, 433);
        } finally {
            entityManager.close();
        }
    }

}
