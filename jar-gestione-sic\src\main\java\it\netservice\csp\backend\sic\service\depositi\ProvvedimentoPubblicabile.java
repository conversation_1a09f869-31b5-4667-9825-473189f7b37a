package it.netservice.csp.backend.sic.service.depositi;

import org.apache.log4j.Logger;

import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.GestioneSicException;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.penale.model.csp.ElaborazioneProvvedimento;

public class ProvvedimentoPubblicabile extends DepositoAbstract<ElaborazioneProvvedimento, SicPenaleRepository> {

    private static final Logger LOGGER = Logger.getLogger(ProvvedimentoPubblicabile.class);

    public ProvvedimentoPubblicabile(SicPenaleRepository repo, String operatore) throws GestioneSicException {
        super(repo, operatore);
    }

    @Override
    public void accetta(ElaborazioneProvvedimento elabDep) throws CspBackendException {
        // LOGGING NRG/ID_UDIEN - Inizio ProvvedimentoPubblicabile.accetta
        if (elabDep != null && elabDep.getDeposito() != null) {
            LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] ProvvedimentoPubblicabile.accetta - START - IDCAT: %d, Tipo Deposito: %s",
                    elabDep.getDeposito().getIdCat(), elabDep.getDeposito().getTipo()));
        } else {
            LOGGER.warn("[LOGGING NRG/ID_UDIEN] ProvvedimentoPubblicabile.accetta - START - elabDep or elabDep.getDeposito() is null.");
        }
        ricorso = repo.getRicorsoByElaborazioneDeposito(elabDep);
        if (ricorso != null) {
            LOGGER.info(
                    String.format("[LOGGING NRG/ID_UDIEN] ProvvedimentoPubblicabile.accetta - RicorsoSic recuperato. NRG: %s, IDCAT: %d",
                            ricorso.getNrg(), elabDep.getDeposito().getIdCat()));
        } else {
            LOGGER.warn(String.format("[LOGGING NRG/ID_UDIEN] ProvvedimentoPubblicabile.accetta - RicorsoSic NON recuperato per IDCAT: %d",
                    elabDep.getDeposito().getIdCat()));
        }
        // Note: This method does not appear to directly set or ensure ID_UDIEN is set in PENALE_PROVVEDIMENTI.
        LOGGER.info(String.format(
                "[LOGGING NRG/ID_UDIEN] ProvvedimentoPubblicabile.accetta - END - No explicit ID_UDIEN handling in this method. IDCAT: %d",
                elabDep.getDeposito().getIdCat()));
    }

    @Override
    protected void rifiuta(ElaborazioneProvvedimento elabDep) throws CspBackendException {
    }

}
