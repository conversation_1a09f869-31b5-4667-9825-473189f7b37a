/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package it.netservice.csp.backend.firma;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.itextpdf.text.pdf.PdfReader;

/**
 *
 * <AUTHOR>
 */
public class ContaPaginePDF {

    public static int contaPaginePDF(byte[] content) {
        PdfReader reader = null;
        try (InputStream pdfStream = new ByteArrayInputStream(content)) {
            reader = new PdfReader(pdfStream, new byte[0]);
            int pages = reader.getNumberOfPages();
            reader.close();
            return pages;
        } catch (IOException ex) {
            if (reader != null) {
                reader.close();
            }
            Logger.getLogger(ContaPaginePDF.class.getName()).log(Level.INFO, null, ex);
        }
        return 0;
    }

}
