package it.netservice.csp.backend.dto;

public enum RestErrorCodeEnum {
    // usata per errori del common rest warning
    CSP_COMMON_REST_WARNING,
    // usata per segnare un errore BE generico
    GENERIC_ERROR,
    // usata per indicare che le credenziali (username e/o password) di accesso non sono corrette
    USER_PASS_INVALID,
    // usata per segnalare che l'utente non è stato trovato
    USER_NOT_FOUND,
    // usata per segnalare che il token è null
    TOKEN_NULL,
    // per segnalare un errore nel recupero delle informazioni relative al token
    TOKEN_ERROR,
    // usata per segnalare che si è verificato un errore generico quando si sta firmato un provvedimento
    FIRMA_REMOTA_GENERIC_ERROR,
    // usata per segnalare la mancanza di campi obbligatori
    FIELD_MANDATORY_ERROR,
    // id del deposito non valorizzato
    ID_DEPOSITO_NULL,
    // idcat del deposito non valorizzato
    IDCAT_DEPOSITO_NULL,
    // usata per indicare che l'id del provvedimento è null
    ID_PROVV_NULL,
    // usata per indicare che l'nrg non è stato fornito
    NRG_NULL,
    // deposito non trovato in db
    DEPOSITO_NOT_FOUND,
    // errore durante la ricerca dei depositi
    DEPOSITO_SEARCH_ERROR,
    // errore generico arrivato da GL-CASS
    GL_GENERIC_ERROR,
    // deposito non trovato sul glcass
    GL_DEPOSITO_NOT_FOUND,
    // registro non trovato nel gestore locale
    GL_REGISTRO_NOT_FOUND,
    // errore generico in elaborazione deposito
    ELABORAZIONE_DEPOSITO_GENERIC_ERROR,
    // usata per indicare un errore in fase di salvataggio di un'elaborazione
    ELABORAZIONE_DEPOSITO_SAVE_ERROR,
    // usata per indicare un errore in fase di eliminazione di un'elaborazione
    ELABORAZIONE_DEPOSITO_DELETE_ERROR,
    // usata per depositi che risultano essere già accettati o rifiutati
    DEPOSITO_ACCETTATO_RIFIUTATO,
    // usato nel caso in cui il deposito principale di un deposito secondario non è stato ancora accettato
    DEPOSITO_PRINCIPALE_NON_ACCETTATO,
    // errore generico in accettazione depositi
    ACCETTAZIONE_DEPOSITI_GENERIC_ERROR,
    // usata per indicare che la sessione è scaduta o assente
    SESSION_EXPIRED,
    // errore generato in accettazione durante la fase di salvataggio sul db
    ACCETTAZIONE_DEPOSITI_SAVE_ERROR,
    // usata per indicare un errore generico nella parte di gestione del sic
    GESTIONE_SIC_GENERIC_ERROR,
    // usato per indicare errori durante l'accettazione del deposito sul SIC
    ACCETTAZIONE_DEPOSITO_SIC_ERROR,
    // usato per segnalare errori generici in fase di rifiuto di un deposito
    RIFIUTA_DEPOSITO_GENERIC_ERROR,
    // usato per indicare errori verificati nel salvataggio dei dati durante il rifiuto di un deposito
    RIFIUTA_DEPOSITO_SAVE_ERROR,
    // usato per segnalare l'assenza dei dati rifiuto del deposito
    DATI_RIFIUTI_NULL,
    // usata per indicare errori durante l'inizializzazione dell'ìscrizione al deposito dell'utente
    INIT_ISCRIZIONE_DEPOSITO_ERROR,
    // errore generico sollevato durante il deposito
    DEPOSITO_GENERIC_ERROR,
    // usato per segnalare che il deposito è stato pubblicato da SIC
    DEPOSITO_PUBBLICATO_SIC,
    // usato per segnalare che il deposito è stato depositato da SIC
    DEPOSITO_DEPOSITATO_SIC,
    // errore generico sollevato durante la presa in carico
    PRESA_IN_CARICO_GENERIC_ERROR,
    // usato per segnalare che solo i nuovi depositi possono essere presi in carico
    PRESA_IN_CARICO_DEPOSITO_ONLY_NEW,
    // errori in fase di salvataggio di un ricorso
    RICORSO_SAVE_ERROR,
    // usato in caso di Oscuramento dati / tipo verificato / dispositivo non valorizzati correttamente
    DATA_PROVV_NULL,
    // usata per segnalare l'assenza dei dati udienza
    DATI_UDIEN_NULL,
    // errore generico sollevato in fase di pubblicazione
    PUBBLICAZIONE_GENERIC_ERROR,
    // usata per indicare che è già in corso la pubblicazione del provvedimento
    PUBBLICAZIONE_IN_CORSO,
    // errore sollevato nel caso in cui non è stato trovato l'atto
    ATTO_NOT_FOUND,
    // usata per segnalare un errore durante l'update della sentenza su db
    UPDATE_SENTENZA_ERROR,
    // usata per segnalare errori durante il salvataggio dei timbri
    TIMBRI_SAVE_ERROR,
    // usata per segnalare errori in fase di pubblicazione a ufficio copie
    UCU_PUBB_ERROR,
    // errore in fase di aggiornamento di un deposito
    UPDATE_DEPOSITO_ERROR,
    // usata per segnalare un'eccezione in fase di aggiornamento dello stato per il DESK
    UPDATE_STATO_DESK_ERROR,
    // errore su sezioni nulle in sessione
    SEZIONI_NULL,
    // usata per indicare che l'utente non è autorizzato a firmare
    UNAUTHORIZED_SIGN,
    // usata per segnalare che l'utente non è autorizzato alla pubblicazione
    UNAUTHORIZED_PUBLISH,
    // usata per segnalare che non è possibile cercare per stato PUBBLICATO per il tipo di provvedimento selezionato
    SEARCH_PUBBLICATO_NOT_ALLOWED,
    // usata per segnalare che non è possibile cercare per MINUTA ACCETTATA per il tipo di provvedimento selezionato
    SEARCH_MINUTA_ACCETTATA_NOT_ALLOWED,
    // usata per indicare che il deposito non è nuovo e non può essere modificato
    DEPOSITO_NOT_NEW,
    // usata per segnalare un errore durante il recupero dello storico del provvedimento
    STORICO_PROVV_ERROR,
    // usata per indicare la presenza di errori durante la procedura di export xlsx delle udienze
    EXPORT_UDIENZE_ERROR,
    // errore generico nel monitoraggio
    MONITORAGGIO_GENERIC_ERROR,
    // usata per indicare errori durante le ricerche nel monitoraggio
    MONITORAGGIO_SEARCH_ERROR,
    // usata per indicare errori durante la ricerca del fascicolo in monitoraggio
    SEARCH_FASCICOLO_ERROR,
    // usata per indicare un errore generico nei servizi dei ricorsi
    RICORSI_GENERIC_ERROR,
    // usata per indicare la presenza di errori durante la ricerca dei ricorsi
    SEARCH_RICORSI_ERROR,
    // usata per indicare la presenza di errori durante la ricerca dei reati dei ricorsi
    REATI_RICORSO_ERROR,
    // usata per indicare la presenza di errori durante la ricerca delle parti dei ricorsi
    PARTI_RICORSO_ERROR,
    // errore generico nei servizi delle anagrafiche
    ANAGRAFICHE_GENERIC_ERROR,
    // usata per segnalare la presenza di un errore durante la ricerca dei magistrati
    FIND_MAGISTRATI_ERROR,
    // errore generico nei servizi degli atti
    ATTI_GENERIC_ERROR,
    // usata per indicare che l'atto non è stato ancora accettato
    ATTO_NOT_ACCEPTED,
    // usata per indicare la presenza di errori durante la richiesta di download degli atti
    DOWNLOAD_ATTO_ERROR,
    // usata per segnalare un errore generico legato all'accesso dell'utente
    AUTHENTICATION_GENERIC_ERROR,
    // usata per indicare che l'account associato all'utente non è più valido
    ACCOUNT_NOT_VALID,
    // usata per segnalare errori durante l'inizializzazione di una sessione
    INIT_SESSION_ERROR,
    // usata per segnalare che l'utente non è abilitato alle funzioni del CSP
    USER_UNAUTH_CSP,
    // usata per indicare che l'utente è abilitato in sola lettura
    USER_READ_ONLY,
    // usata per indicare che l'utente con il quale si sta accettando non è lo stesso che ha preso in carico il deposito
    USER_DEPOSITO_DIFF,
    // errore per ricorsi non trovati
    RICORSO_NOT_FOUND,
    // errore per provvedimento non trovato
    PROVV_NOT_FOUND,
    // usata per segnalare che il deposito non è stato ancora lavorato
    DEPOSITO_NON_LAVORATO,
    // usata per indicare che il provvedimento non è pubblicabile
    PROVV_NON_PUBB,
    // usata per segnalare che il provvedimento non può essere ritrasmesso perché lo stato non è compatibile
    RITRASMISSIONE_PROVV_ERROR,
    // usata per indicare la presenza di errori durante la pubblicazione (il deposito risulta comunque accettato)
    PUBBLICAZIONE_ERROR,
    // usata per indicare che il tipo di sentenza è diverso rispetto a quello presente sul SIC
    TIPO_PROVV_NOT_MATCHING,
    // errore durante la ricerca del provvedimento
    PROVV_SEARCH_ERROR,
    // indica che il tipo di minuta non è valido è che deve essere inserito l'esito udienza dal SIC
    TIPO_MINUTA_INVALID,
    // per indicare la presenza di errori durante la conversione della data minuta
    MINUTA_CONV_ERROR,
    // usata per segnalare errori durante l'export dei dati dei depositi
    EXPORT_DEPOSITI_ERROR,
    // per indicare gli errori durante la ricerca dei riuniti
    RIUNITI_RICORSO_ERROR,
    // Errore Firma Itagile
    // Utente non presente: contattare il CED
    USER_NOT_PRESENT_SIGN_ERROR,
    // Errore Firma Itagile
    // Password errata
    INCORRECT_PASSWORD_SIGN_ERROR,
    // Errore Firma Itagile
    // Pin errato
    INCORRECT_PIN_SIGN_ERROR,
    // Errore Firma Itagile
    // Utente bloccato
    USER_BLOCKED_SIGN_ERROR,
    // Errore Firma Itagile
    // token jwt non valido o non corretto
    // Autenticazione ADN fallita. Riprovare dopo aver eseguito il logout e in caso di nuovo errore contattare il CED
    INVALID_JWT_TOKEN_SIGN_ERROR,
    // per indicare gli errori durante la lettura versione del file application.properties
    VERSION_UTIL_GENERIC_ERROR,
    // usato per segnalare che il deposito è già stato preso in carico da un altro utente
    DEPOSITO_GIA_ASSEGNATO;

}
