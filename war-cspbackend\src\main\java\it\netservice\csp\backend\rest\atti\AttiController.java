
package it.netservice.csp.backend.rest.atti;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.Serializable;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.annotation.security.RolesAllowed;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.StreamingOutput;

import org.apache.log4j.Logger;

import it.netservice.common.rest.backend.CriteriaBuilderHelper;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.elaborazioneDepositi.service.AttiCriteriaParameters;
import it.netservice.csp.backend.elaborazioneDepositi.service.DepositoService;
import it.netservice.csp.backend.excpetion.AttiExcpetion;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.ext.service.ServiceBuilder;
import it.netservice.csp.backend.rest.AbstractCspBackendController;
import it.netservice.csp.backend.rest.auth.SecurityUtil;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.csp.backend.ws.atti.ContentInfo;
import it.netservice.csp.backend.ws.atti.IdAtto;
import it.netservice.csp.backend.ws.atti.IdFascicolo;
import it.netservice.csp.backend.ws.atti.ServiziAttoInformatico;
import it.netservice.penale.model.csp.Atto;
import it.netservice.penale.model.sic.criteria.csp.DepositiViewCriteriaParameters;
import it.netservice.penale.model.sic.csp.DepositoView;

/**
 * Servizi per la consultazione degli atti.
 *
 * <AUTHOR>
 */
@RolesAllowed({ "CSPCLIENT" })
@Path("/atti")
public class AttiController extends AbstractCspBackendController {

    private static final Logger LOGGER = Logger.getLogger(AttiController.class);

    @Inject
    private ServiceBuilder serviceBuilder;

    @Inject
    private SecurityUtil util;

    @Inject
    private DepositoService depositoService;

    @GET
    @Path("/download")
    @Produces(MediaType.MEDIA_TYPE_WILDCARD)
    public Response download(@Context HttpServletRequest context, @QueryParam("idCat") String idCat,
            @QueryParam("originale") boolean originale) throws CspBackendException {
        if (idCat == null) {
            throw new CspBackendException("Riferimento contenuto non corretto. idCat null", RestErrorCodeEnum.IDCAT_DEPOSITO_NULL, 433);
        }
        LOGGER.info("Richiesta download degli atti con idCat:" + idCat);
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            try {
                LOGGER.info("Recupero informazioni atto con idCat:" + idCat);
                CriteriaBuilderHelper<Atto> criteriaBuilder = new CriteriaBuilderHelper<>(Atto.class);
                AttiCriteriaParameters param = new AttiCriteriaParameters();
                param.setIdCat(idCat);
                Atto atto = criteriaBuilder.uniqueResult(entityManager, param);
                String nrg = atto.getNrg();
                // util.checkUtenzaRicorsoView(context, entityManager, nrg);
            } catch (NoResultException ex) {
                // l'atto non è ancora stato accettato
            }
            LOGGER.info("Recupero atto con idCat:" + idCat + " completato. Preparazione file in corso...");
            ServiziAttoInformatico service = serviceBuilder.getAttoInformaticoService();
            IdAtto idAtto = new IdAtto();
            IdFascicolo fascicolo = new IdFascicolo();
            fascicolo.setRegistro(System.getProperty("gestorelocale.registro"));
            fascicolo.setUfficio(System.getProperty("gestorelocale.ufficio"));
            idAtto.setId(idCat);
            idAtto.setFascicolo(fascicolo);

            ContentInfo contentInfo = service.profiloAtto(idAtto);
            String fileName = contentInfo.getFilename();
            String contentType = decodeContentType(fileName);
            contentType = contentType == null ? contentInfo.getMimeType() : contentType;
            if (!originale && "p7m".equalsIgnoreCase(fileName.substring(fileName.length() - 3))) {
                fileName = fileName.substring(0, fileName.length() - 4);
            }
            String contentDispositionHeader = String.format("attachment; filename=\"%s\"", fileName);
            final byte[] content = service.download(idAtto, originale);

            StreamingOutput stream = new StreamingOutput() {
                @Override
                public void write(OutputStream output) throws IOException {
                    output.write(content);
                }
            };

            LOGGER.info("Preparazione file atto con idCat:" + idCat + " completata");
            return Response.ok(stream).header("Content-Disposition", contentDispositionHeader).header("Content-Type", contentType).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.info("Errore durante il download dell'atto. idCat:" + idCat);
            throw new AttiExcpetion("Errore durante il download dell'atto", RestErrorCodeEnum.DOWNLOAD_ATTO_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @POST
    @Path("/downloadFilesBusta")
    @Produces(MediaType.MEDIA_TYPE_WILDCARD)
    public Response downloadFilesBusta(@Context HttpServletRequest context, DownloadFileBustaInput input) throws CspBackendException {
        if (input == null) {
            throw new CspBackendException("Input non fornito", RestErrorCodeEnum.IDCAT_DEPOSITO_NULL, 433);
        }
        if (input.getDaScaricare().isEmpty()) {
            LOGGER.warn("Nessun file da scaricare. Lista vuota");
            return Response.ok().build();
        }
        LOGGER.info("Richiesta download dei seguenti atti:" + input.daScaricare.toString());
        EntityManager entityManager = emfCaricamento.createEntityManager();
        SicPenaleRepository repo = new SicPenaleRepository(entityManager);
        try {
            final ByteArrayOutputStream zipOutputStream = new ByteArrayOutputStream();
            try (ZipOutputStream zip = new ZipOutputStream(zipOutputStream)) {
                for (String idCat : input.getDaScaricare()) {
                    try {
                        LOGGER.info("Recupero informazioni atto con idCat:" + idCat);
                        CriteriaBuilderHelper<Atto> criteriaBuilder = new CriteriaBuilderHelper<>(Atto.class);
                        AttiCriteriaParameters param = new AttiCriteriaParameters();
                        param.setIdCat(idCat);
                        Atto atto = criteriaBuilder.uniqueResult(entityManager, param);
                        String nrg = atto.getNrg();
                        // util.checkUtenzaRicorsoView(context, entityManager, nrg);
                    } catch (NoResultException ex) {
                        // l'atto non è ancora stato accettato
                    }
                    LOGGER.info("Recupero atto con idCat:" + idCat + " completato. Preparazione file in corso...");
                    ServiziAttoInformatico service = serviceBuilder.getAttoInformaticoService();
                    IdAtto idAtto = new IdAtto();
                    IdFascicolo fascicolo = new IdFascicolo();
                    fascicolo.setRegistro(System.getProperty("gestorelocale.registro"));
                    fascicolo.setUfficio(System.getProperty("gestorelocale.ufficio"));
                    idAtto.setId(idCat);
                    idAtto.setFascicolo(fascicolo);

                    ContentInfo contentInfo = service.profiloAtto(idAtto);
                    String fileName = contentInfo.getFilename();
                    String extention = fileName.substring(fileName.length() - 3).toLowerCase();
                    if ("p7m".equalsIgnoreCase(extention)) {
                        fileName = fileName.substring(0, fileName.length() - 4);
                    }
                    final byte[] content = service.download(idAtto, false);

                    ZipEntry entry = new ZipEntry(fileName);
                    zip.putNextEntry(entry);
                    zip.write(content);
                    zip.closeEntry();
                }
            }
            StreamingOutput stream = new StreamingOutput() {
                @Override
                public void write(OutputStream output) throws IOException {
                    zipOutputStream.writeTo(output);
                }
            };
            CriteriaBuilderHelper<DepositoView> criteriaBuilder = new CriteriaBuilderHelper<>(DepositoView.class);
            DepositiViewCriteriaParameters param = new DepositiViewCriteriaParameters();
            param.setIdCat(input.getIdCatBusta());
            DepositoView deposito = criteriaBuilder.uniqueResult(entityManager, param);
            String nomeZip = "fileBusta_" + deposito.getNumeroRicorso() + "_" + deposito.getAnnoRicorso();

            LOGGER.info("Preparazione file zip completata");
            return Response.ok(stream).header("Content-Disposition", "attachment; filename=\"" + nomeZip + ".zip\"")
                    .header("Content-Type", "application/zip").build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.info("Errore durante il download delgli atti");
            throw new AttiExcpetion("Errore durante il download delgli atti", RestErrorCodeEnum.DOWNLOAD_ATTO_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    private String decodeContentType(String fileName) {
        String extension = fileName.substring(fileName.length() - 3).toLowerCase();
        switch (extension) {
            case "pdf":
                return "application/pdf";
            case "xml":
                return "application/xml";
            case "p7m":
                return "application/x-pkcs7-mime";
            default:
                return null;
        }
    }

    private static class DownloadFileBustaInput implements Serializable {

        private long idCatBusta;

        private List<String> daScaricare;

        public DownloadFileBustaInput() {
        }

        public DownloadFileBustaInput(List<String> daScaricare, long idCatBusta) {
            this.daScaricare = daScaricare;
            this.idCatBusta = idCatBusta;
        }

        public long getIdCatBusta() {
            return idCatBusta;
        }

        public void setIdCatBusta(long idCatBusta) {
            this.idCatBusta = idCatBusta;
        }

        public List<String> getDaScaricare() {
            return daScaricare;
        }

        public void setDaScaricare(List<String> daScaricare) {
            this.daScaricare = daScaricare;
        }
    }
    /*
     * @GET public Response list(@Context HttpServletRequest context, @QueryParam("nrg") String nrg) { EntityManager entityManager =
     * emfCaricamento.createEntityManager(); boolean isCsc = context.getUserPrincipal() != null &&
     * "cscclient".equals(context.getUserPrincipal().getName()); try { if (nrg == null) { throw new CspBackendException("nrg null"); } if
     * (context.getSession().getAttribute("sentenzaMinuta") == null) { throw new UnauthorizedException("Sessione scaduta o assente"); }
     * util.checkUtenzaRicorsoView(context, entityManager, nrg); CriteriaBuilderHelper<Atto> criteriaBuilder = new
     * CriteriaBuilderHelper<>(Atto.class); AttiCriteriaParameters param = new AttiCriteriaParameters(); param.setNrg(nrg); if
     * (context.getUserPrincipal() != null && "spgclient".equals(context.getUserPrincipal().getName())) { param.setTipiToExclude(new
     * HashSet<>(Arrays.asList("MINUTAORDINANZA", "MINUTAORDINANZAINTERLOCUTORIA", "MINUTASENTENZA", "DECRETOVARIAZIONEMATERIA",
     * "DECRETOINVIOATTIADALTRASOTTOSEZIONE", "PROPOSTA"))); } if (isCsc) { param.setTipiToExclude(new
     * HashSet<>(Arrays.asList("MEMORIACONVISTO", "REQUISITORIACONVISTO", "VISTO", "REQUISITORIA", "ATTOGENERICOPG", "ASSEGNAZIONEPG"))); }
     * List<Atto> resultList = criteriaBuilder.resultList(entityManager, param); if (!(boolean)
     * context.getSession().getAttribute("sentenzaMinuta")) { Iterator<Atto> it = resultList.iterator(); while (it.hasNext()) { Atto atto =
     * it.next(); if (atto.getTipo().toLowerCase().contains("minuta")) { it.remove(); } } } if (isCsc) {
     * resultList.addAll(loadAttiRestituitiPG(entityManager, nrg)); resultList.addAll(loadAttiCartaceiPG(entityManager, nrg)); }
     * Collections.sort(resultList); return Response.ok(resultList).build(); } catch (Throwable e) { return
     * ServerErrorHandler.getResponse(LOGGER, e); } finally { entityManager.close(); } }
     *
     * @GET
     *
     * @Path("/attiRicorso") public Response listAttiRicorso(@Context HttpServletRequest context, @QueryParam("nrg") String nrg) {
     * EntityManager entityManager = emfCaricamento.createEntityManager(); try { if (nrg == null) { throw new
     * CspBackendException("nrg null"); } CriteriaBuilderHelper<Atto> criteriaBuilder = new CriteriaBuilderHelper<>(Atto.class);
     * AttiCriteriaParameters param = new AttiCriteriaParameters(); param.setNrg(nrg); List<Atto> resultList =
     * criteriaBuilder.resultList(entityManager, param); Collections.sort(resultList); return Response.ok(resultList).build(); } catch
     * (Throwable e) { return ServerErrorHandler.getResponse(LOGGER, e); } finally { entityManager.close(); } }
     *
     * @GET
     *
     * @Path("/attiSic") public Response listAttiSic(@Context HttpServletRequest context, @QueryParam("nrg") String nrg) { class
     * AttiSicCriteriaParameters extends CriteriaParametersBackend<ViewListAttoSic> {
     *
     * @SearchParameter(operator = Operator.EQUAL) private String nrg;
     *
     * public AttiSicCriteriaParameters(String nrg) { this.nrg = nrg; }
     *
     * public String getNrg() { return nrg; }
     *
     * public void setNrg(String nrg) { this.nrg = nrg; }
     *
     * } EntityManager entityManager = emfCaricamento.createEntityManager(); try { if (nrg == null) { throw new
     * CspBackendException("nrg null"); } CriteriaBuilderHelper<ViewListAttoSic> criteriaBuilder = new
     * CriteriaBuilderHelper<>(ViewListAttoSic.class); AttiSicCriteriaParameters param = new AttiSicCriteriaParameters(nrg);
     * List<ViewListAttoSic> resultList = criteriaBuilder.resultList(entityManager, param); return Response.ok(resultList).build(); } catch
     * (Throwable e) { return ServerErrorHandler.getResponse(LOGGER, e); } finally { entityManager.close(); } }
     *
     * @POST
     *
     * @Path("/update")
     *
     * @Produces(MediaType.MEDIA_TYPE_WILDCARD) public Response updateAtto(@Context HttpServletRequest context, @QueryParam("idCat") String
     * idCat,
     *
     * @QueryParam("tipoCarteRegolamentari") String tipoCarteRegolamentari) throws CspBackendException { if (idCat == null) { throw new
     * CspBackendException("Riferimento contenuto non corretto. idCat null"); } EntityManager entityManager =
     * emfCaricamento.createEntityManager(); EntityTransaction transaction = entityManager.getTransaction(); transaction.begin(); try {
     * CriteriaBuilderHelper<Atto> criteriaBuilder = new CriteriaBuilderHelper<>(Atto.class); AttiCriteriaParameters param = new
     * AttiCriteriaParameters(); param.setIdCat(idCat); Atto atto = criteriaBuilder.uniqueResult(entityManager, param);
     *
     * atto.setTipoCarteRegolamentari(tipoCarteRegolamentari); transaction.commit(); return Response.ok().build(); } catch (Throwable e) {
     * if (transaction.isActive()) { transaction.rollback(); } return ServerErrorHandler.getResponse(LOGGER, e); } finally {
     * entityManager.close(); } }
     *
     * @POST
     *
     * @Path("/delete")
     *
     * @Produces(MediaType.MEDIA_TYPE_WILDCARD) public Response delete(@Context HttpServletRequest context, @QueryParam("idCat") String
     * idCat) throws CspBackendException { if (idCat == null) { throw new
     * CspBackendException("Riferimento contenuto non corretto. idCat null"); } EntityManager entityManager =
     * emfCaricamento.createEntityManager(); EntityTransaction transaction = entityManager.getTransaction(); transaction.begin(); try {
     * CriteriaBuilderHelper<Atto> criteriaBuilder = new CriteriaBuilderHelper<>(Atto.class); AttiCriteriaParameters param = new
     * AttiCriteriaParameters(); param.setIdCat(idCat); Atto atto = criteriaBuilder.uniqueResult(entityManager, param); String tipoAtto =
     * atto.getTipo(); atto.setCancellato(true); entityManager.merge(atto); CriteriaBuilderHelper<AttoSic> criteriaBuilderSic = new
     * CriteriaBuilderHelper<>(AttoSic.class); class AttiSicCriteriaParameters extends CriteriaParametersBackend<AttoSic> {
     *
     * @SearchParameter(operator = Operator.EQUAL) private Long idCat;
     *
     * public AttiSicCriteriaParameters(Long idCat) { this.idCat = idCat; }
     *
     * public Long getIdCat() { return idCat; }
     *
     * public void setIdCat(Long idCat) { this.idCat = idCat; }
     *
     * } if (!atto.getTelematico()) { AttiSicCriteriaParameters paramSic = new AttiSicCriteriaParameters(atto.getIdCat()); AttoSic attoSic =
     * criteriaBuilderSic.uniqueResult(entityManager, paramSic); entityManager.remove(attoSic); }
     *
     * SicCivileRepository repo = new SicCivileRepository(entityManager); RicorsoSic ricorso =
     * repo.getRicorsoByNrg(Long.valueOf(atto.getNrg())); BigDecimal idSezione = repo.getIdSezione(ricorso.getSezione()); String operatore =
     * (String) context.getSession().getAttribute("idOperatore"); Utente operator = repo.findUtenteByUsername(operatore); String note =
     * "ELIMINAZIONE DOCUMENTO DI " + it.netservice.sic.model.desk.Atto.TipoAtto.valueOf(tipoAtto).getDescrizione();
     * repo.createStoriaEliminaDocumento(operator, ricorso.getNrg(), note, null, idSezione.longValue());
     *
     * transaction.commit(); return Response.ok().build(); } catch (Throwable e) { if (transaction.isActive()) { transaction.rollback(); }
     * return ServerErrorHandler.getResponse(LOGGER, e); } finally { entityManager.close(); } }
     *
     * @POST
     *
     * @Path("/upload")
     *
     * @Consumes("multipart/form-data") public Response uploadFile(@Context HttpServletRequest context, MultipartFormDataInput input) throws
     * CspBackendException, IOException { EntityManager entityManager = emfCaricamento.createEntityManager(); EntityTransaction transaction
     * = entityManager.getTransaction(); transaction.begin(); try { SecurityUtil.checkUtenzaReadOnly(context); String operatore = (String)
     * context.getSession().getAttribute("idOperatore"); Map<String, List<InputPart>> uploadForm = input.getFormDataMap(); String nrg =
     * uploadForm.get("nrg").get(0).getBodyAsString(); util.checkUtenzaRicorsoView(context, entityManager, nrg); InputPart inputPart =
     * uploadForm.get("file").get(0);
     *
     * MultivaluedMap<String, String> header = inputPart.getHeaders(); String fileName = getFileName(header); // controlla sintassi nome
     * file String nomeFile = fileName.substring(0, fileName.lastIndexOf('.')); String extFile =
     * fileName.substring(fileName.lastIndexOf('.'), fileName.length()); nomeFile = controllaSintassiNomeFile(nomeFile + extFile); //
     * convert the uploaded file to inputstream InputStream inputStream = inputPart.getBody(InputStream.class, null); String idRicorso =
     * uploadForm.get("nrg").get(0).getBodyAsString(); String tipologia = uploadForm.get("tipologia").get(0).getBodyAsString(); String note
     * = uploadForm.get("note").get(0).getBodyAsString(); String dataDeposito = uploadForm.get("dataDeposito") != null ?
     * uploadForm.get("dataDeposito").get(0).getBodyAsString() : null; String sospensione = uploadForm.get("sospensione") != null ?
     * uploadForm.get("sospensione").get(0).getBodyAsString() : null; String dataAccettazione = uploadForm.get("dataAccettazione") != null ?
     * uploadForm.get("dataAccettazione").get(0) .getBodyAsString() : null; if (note != null && !"".equals(note)) { note += ". "; } String
     * depositanteId = uploadForm.get("depositanteId") != null ? uploadForm.get("depositanteId").get(0) .getBodyAsString() : null;
     * AttoDaAcquisire attoDaAcquisire = new AttoDaAcquisire(idRicorso, nomeFile, new DataHandler(new InputStreamDataSource(inputStream)));
     * it.netservice.csc.backend.ws.gestioneatti.types.IdAtto idAtto = serviceBuilder.getGestioneAttiService()
     * .acquisisciAtto(attoDaAcquisire);
     *
     * Atto atto = new Atto(); atto.setIdCat(Long.valueOf(idAtto.getId())); atto.setNrg(idRicorso); atto.setNomeFile(nomeFile);
     * atto.setTipo(tipologia); atto.setTipoCarteRegolamentari(tipologia); atto.setTipoGl("ATTO"); atto.setNote(note);
     * atto.setAutore(operatore); atto.setTelematico(false); atto.setCancellato(false); if ("IstanzaOpposizione380bis".equals(tipologia)) {
     * atto.setData(Long.valueOf(dataDeposito)); }
     *
     * // se sto caricando un file pdf, salvo anche il numero di pagine if (nomeFile.endsWith(".pdf")) { try {
     *
     * PdfReader reader = new PdfReader(inputPart.getBody(InputStream.class, null)); int numpages = reader.getNumberOfPages();
     * reader.close(); atto.setNumPagine(numpages); } catch (Throwable ex) { // se il file è corrotto si carica comunque, settando numero
     * pagine però a -1 LOGGER.error("Non e' stato possibile contare le pagine.", ex); atto.setNumPagine(-1); } } atto =
     * entityManager.merge(atto);
     *
     * if ((uploadForm.get("isIstanzaVisibilita").get(0).getBodyAsString()).equals("SI")) { String dataInizioVisibilita =
     * uploadForm.get("dataInizioVisibilita").get(0).getBodyAsString(); String dataFineVisibilita =
     * uploadForm.get("dataFineVisibilita").get(0).getBodyAsString(); Long dataInizio = new Long(dataInizioVisibilita); Long dataFine = new
     * Long(dataFineVisibilita); if (!DateUtils.isValidPeriod(dataInizio, dataFine)) { throw new
     * CommonRestWarning("Data inizio visibilità non può essere maggiore di data fine visibilità"); } String codiceFiscaleParte =
     * uploadForm.get("parteCF").get(0).getBodyAsString(); String codiceFiscaleAvvocato =
     * uploadForm.get("avvocatoCF").get(0).getBodyAsString(); String nomeAvvocato = uploadForm.get("nomeAvvocato").get(0).getBodyAsString();
     * VisibilitaTemporanea visTemp = new VisibilitaTemporanea(); visTemp.setNrg(new Long(nrg));
     * visTemp.setDataInizioVisibilita(DateUtils.atStartOfDay(dataInizio).getTime());
     * visTemp.setDataFineVisibilita(DateUtils.atStartOfDay(dataFine).getTime()); visTemp.setCodiceFiscaleParte(codiceFiscaleParte);
     * visTemp.setCodiceFiscaleAvvocato(codiceFiscaleAvvocato); visTemp.setNomeAvvocato(nomeAvvocato); entityManager.persist(visTemp); }
     * Query query = entityManager
     * .createNativeQuery("update civile_t_ricorso set idcatricorso = ? where nrg = ? and idcatricorso is not null"); query.setParameter(1,
     * Long.valueOf(idAtto.getFascicolo().getIdRepository())); query.setParameter(2, Long.valueOf(idRicorso)); query.executeUpdate();
     *
     * SicCivileRepository repo = new SicCivileRepository(entityManager); RicorsoSic ricorso = repo.getRicorsoByNrg(Long.valueOf(nrg)); if
     * ("IstanzaOpposizione380bis".equals(tipologia)) { BigDecimal idSezione = repo.getIdSezione(ricorso.getSezione()); Utente operator =
     * repo.findUtenteByUsername(operatore); repo.createStoriaIstanzaOpposizione380bis(operator, ricorso.getNrg(), atto.getIdCat(),
     * idSezione.longValue(), new Date(Long.valueOf(dataDeposito))); } if ("Proposta380bis".equals(tipologia)) { BigDecimal idSezione =
     * repo.getIdSezione(ricorso.getSezione()); Utente operator = repo.findUtenteByUsername(operatore);
     * repo.accettaPropostaAccelerata(ricorso.getNrg(), false, dataDeposito, depositanteId, "true".equals(sospensione) ? true : false);
     * repo.createStoriaPropostaAccelerata(operator, ricorso.getNrg(), note, idSezione); }
     *
     * if ("NotificaPropostaDefinizioneAnticipata".equals(tipologia)) { BigDecimal idSezione = repo.getIdSezione(ricorso.getSezione());
     * Utente operator = repo.findUtenteByUsername(operatore); SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
     * String dataInvio = dateFormat.format(new Date(new Long(uploadForm.get("dataInvio").get(0).getBodyAsString()))); String dataConsegna =
     * dateFormat.format(new Date(new Long(uploadForm.get("dataConsegna").get(0).getBodyAsString()))); String recorrenteId =
     * uploadForm.get("ricorrenteId") != null ? uploadForm.get("ricorrenteId").get(0) .getBodyAsString() : null;
     *
     * repo.accettaNotificaProposta380(ricorso.getNrg(), dataInvio, dataConsegna, Long.valueOf(recorrenteId));
     * repo.createStoriaNotificaPropostaDefinizioneAnticipata(operator, ricorso.getNrg(), note, idSezione, new
     * Date(dateFormat.parse(dataInvio).getTime())); repo.createNotificaCsc(ricorso.getNrg() + "",
     * "NOTIFICA PROPOSTA DI DEFINIZIONE ANTICIPATA", note, new Date(dateFormat.parse(dataInvio).getTime()), "CSC-Client",
     * Comunicazione.TipoComunicazione.NON_TEL, atto.getIdCat() + "", recorrenteId); }
     *
     * if ("VerbaleUdienza".equals(tipologia)) { BigDecimal idSezione = repo.getIdSezione(ricorso.getSezione()); Utente operator =
     * repo.findUtenteByUsername(operatore); repo.createStoriaVerbaleUdienza(operator, ricorso.getNrg(), note, idSezione); } if
     * ("AttoRichiestaVisibilita".equals(tipologia)) { DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
     *
     * String start = dateFormat.format(new Date(new Long(uploadForm.get("dataInizioVisibilita").get(0).getBodyAsString()))); String end =
     * dateFormat.format(new Date(new Long(uploadForm.get("dataFineVisibilita").get(0).getBodyAsString()))); BigDecimal idSezione =
     * repo.getIdSezione(ricorso.getSezione()); Utente operator = repo.findUtenteByUsername(operatore); String storiaNote =
     * "CONCESSA VISIBILITA DAL " + start + " AL " + end + " A " + uploadForm.get("nomeAvvocato").get(0).getBodyAsString();
     * repo.createStoriaAttovis(operator, ricorso.getNrg(), null, storiaNote, idSezione.longValue()); } if
     * ("ConclusioniScrittePG".equals(tipologia)) { if (repo.isRicorsoFlussoSesta(new Long(idRicorso))) { repo.updateStatoSesta(new
     * Long(idRicorso), StatiFlussoSesta.DEPOSITATE_CONCLUSIONI_PG); } } else if (Arrays .asList("DepositoExArt372", "Memoria378",
     * "Memoria380bis", "Memoria380bis1", "Memoria380ter", "RinunciaRicorso") .contains(tipologia)) { Segnalazione s =
     * procura.createSegnalazioneDepositoAtto(entityManager, nrg, tipologia); if (s != null) { entityManager.merge(s); } }
     *
     * if (!Arrays.asList("Ricorso", "ControRicorso", "ControRicorsoIncidentale", "ProvvedimentoImpugnato").contains(tipologia)) {
     * insertAttoSuccessivo(context, atto, ricorso, repo); } repo.persistOrMerge(ricorso);
     *
     * transaction.commit();
     *
     * notificheDeskService.notificaDeskManuale(entityManager, ricorso, atto);
     *
     * if ("Proposta380bis".equals(tipologia)) { EntityTransaction trans = entityManager.getTransaction(); trans.begin();
     * repo.updateCachePropostaAccelerata(ricorso.getNrg()); trans.commit(); } return Response.status(200).entity(atto).build(); } catch
     * (Throwable e) { if (transaction.isActive()) { transaction.rollback(); } return ServerErrorHandler.getResponse(LOGGER, e); } finally {
     * entityManager.close(); } }
     *
     * public void insertAttoSuccessivo(HttpServletRequest context, Atto attoCaricato, RicorsoSic ricorso, SicCivileRepository repo) throws
     * Exception, Throwable { LOGGER.info("insertAttoSuccessivo caricato in CSC client inizio"); String operatore = (String)
     * context.getSession().getAttribute("idOperatore"); CivileFunzioni funzione = repo.findFunzione("CSC Client"); Utente utente =
     * repo.findUtenteByUsername(operatore); Date oggi = new Date(); AttoSic atto = new AttoSic(); atto.setOperatore(utente.getIdUtente());
     * atto.setOggi(oggi); atto.setIdFunzioneByCivileFunzioni(funzione); atto.setDataEffettIns(oggi); atto.setData(oggi); if (utente !=
     * null) { atto.setIdufficioeffetins(utente.getSezione().getIdParam()); }
     *
     * atto.setRicorso(ricorso);
     *
     * CivileParam paramAnagAtti = repo.getParam("NATURA_ATTI", "SU");
     *
     * CivileAnagAtti anagAtti; if ("".equals(attoCaricato.getTipo()) || attoCaricato.getTipo() == null) { anagAtti = repo.getAnagAtti("TM",
     * paramAnagAtti); } else { anagAtti = repo.getAnagAttiByTagXml(attoCaricato.getTipo(), paramAnagAtti); } if (anagAtti == null) {
     * anagAtti = repo.getAnagAttiByLikeTagXml(attoCaricato.getTipo(), paramAnagAtti); if (anagAtti == null) { anagAtti =
     * repo.getAnagAtti("TM", paramAnagAtti); } } atto.setIdAnagatti(anagAtti.getIdAnagAtti()); atto.setDovesta(repo.getIdParam("SEZIONI",
     * ricorso.getDoveSta())); atto.setTipoDeposito(repo.getIdParam("TIPODEP", "2")); atto.setNote(attoCaricato.getNote());
     * atto.setIdCat(attoCaricato.getIdCat()); ricorso.getAtti().add(atto);
     *
     * LOGGER.info("insertAttoSuccessivo caricato in CSC client fine"); }
     *
     * // // private methods //
     *
     *
     * private String getFileName(MultivaluedMap<String, String> header) { String[] contentDisposition =
     * header.getFirst("Content-Disposition").split(";"); for (String filename : contentDisposition) { if
     * ((filename.trim().startsWith("filename"))) { String[] name = filename.split("="); return name[1].trim().replaceAll("\"", ""); } }
     * return "unknown"; }
     *
     * private String controllaSintassiNomeFile(String nomeFile) { nomeFile = nomeFile.replaceAll("[*:/<>!?|]*", ""); String[] parts =
     * nomeFile.split("\\\\"); return parts[parts.length - 1]; }
     *
     * // Carica gli allegati restituiti dalla Procura Generale private List<Atto> loadAttiRestituitiPG(EntityManager em, String nrg) {
     * return em.createQuery(
     * "SELECT a FROM csc.Atto a WHERE a.nrg = :nrg AND a.cancellato = :cancellato AND EXISTS (SELECT 1 FROM AllegatiRestituzionePG ar WHERE ar.idCat = a.idCatBusta)"
     * , Atto.class).setParameter("nrg", nrg).setParameter("cancellato", false).getResultList(); }
     *
     * // Carica gli allegati non telematici dalla Procura Generale private List<Atto> loadAttiCartaceiPG(EntityManager em, String nrg) {
     * return em.createQuery(
     * "SELECT a FROM csc.Atto a WHERE a.nrg = :nrg AND a.cancellato = :cancellato AND upper(a.tipo) in ('MEMORIACONVISTO', 'REQUISITORIACONVISTO', 'VISTO', 'REQUISITORIA', 'ATTOGENERICOPG', 'ASSEGNAZIONEPG') AND a.telematico = false"
     * , Atto.class).setParameter("nrg", nrg).setParameter("cancellato", false).getResultList(); }
     */
}
