package it.netservice.csp.backend.accettazioneDepositi;

import java.io.ByteArrayInputStream;
import java.io.IOException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;

import org.apache.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import it.netservice.csp.backend.ext.service.ServiceBuilder;
import it.netservice.csp.backend.ws.atti.IdAtto;
import it.netservice.csp.backend.ws.atti.IdFascicolo;
import it.netservice.csp.backend.ws.atti.ServiziAttoInformatico;

/**
 *
 * <AUTHOR>
 */
public class RTParser {

    private Logger LOGGER = Logger.getLogger(RTParser.class);

    private final ServiceBuilder serviceBuilder;

    private String pagatore;
    private Float importo;
    private String codicePagamento;

    public RTParser(ServiceBuilder serviceBuilder) {
        this.serviceBuilder = serviceBuilder;
    }

    public void parse(Long idRT) throws IOException, ParserConfigurationException, SAXException {
        ServiziAttoInformatico service = serviceBuilder.getAttoInformaticoService();
        IdAtto idAtto = new IdAtto();
        IdFascicolo idFascicolo = new IdFascicolo();
        idFascicolo.setUfficio(System.getProperty("gestorelocale.ufficio"));
        idFascicolo.setRegistro(System.getProperty("gestorelocale.registro"));
        idAtto.setFascicolo(idFascicolo);
        idAtto.setId(String.valueOf(idRT));
        byte[] content = service.download(idAtto, false);
        DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
        dbFactory.setNamespaceAware(true);
        DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
        ByteArrayInputStream input = new ByteArrayInputStream(content);
        Document doc = dBuilder.parse(input);
        importo = Float.parseFloat(doc.getElementsByTagNameNS("*", "importoTotalePagato").item(0).getFirstChild().getTextContent());
        codicePagamento = doc.getElementsByTagNameNS("*", "identificativoUnivocoVersamento").item(0).getFirstChild().getTextContent();

        pagatore = "n.d.";
        try {
            Node s = getNode(doc.getFirstChild(), "soggettoPagatore");
            if (s != null) {
                Node iP = getNode(s, "identificativoUnivocoPagatore");
                if (iP != null) {
                    Node cf = getNode(iP, "codiceIdentificativoUnivoco");
                    if (cf != null) {
                        pagatore = cf.getTextContent();
                    }
                }
            }
        } catch (Throwable t) {
            LOGGER.error("Pagatore non risolto", t);
        }
    }

    private Node getNode(Node n, String name) {
        NodeList nl = n.getChildNodes();
        for (int i = 0; i < nl.getLength(); i++) {
            Node s = nl.item(i);
            if (name.equals(s.getLocalName())) {
                return s;
            }
        }
        return null;
    }

    public String getPagatore() {
        return pagatore;
    }

    public Float getImporto() {
        return importo;
    }

    public String getCodicePagamento() {
        return codicePagamento;
    }

}
