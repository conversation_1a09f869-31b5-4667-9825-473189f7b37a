<definitions name='WsServiziInterrogazioneInterni' targetNamespace='http://www.giustizia.it/serviziTelematici/reginde/interrogazioniInt' xmlns='http://schemas.xmlsoap.org/wsdl/' xmlns:ns1='http://www.giustizia.it/serviziTelematici/reginde/interrogazioniExt' xmlns:soap='http://schemas.xmlsoap.org/wsdl/soap/' xmlns:tns='http://www.giustizia.it/serviziTelematici/reginde/interrogazioniInt' xmlns:xsd='http://www.w3.org/2001/XMLSchema'>
    <types>
        <xs:schema xmlns:ns1="http://www.giustizia.it/serviziTelematici/reginde/interrogazioniExt"
                   xmlns:tns="http://www.giustizia.it/serviziTelematici/reginde/interrogazioniInt"
                   xmlns:xs="http://www.w3.org/2001/XMLSchema"
                   targetNamespace="http://www.giustizia.it/serviziTelematici/reginde/interrogazioniInt" version="1.0">
            <xs:import namespace="http://www.giustizia.it/serviziTelematici/reginde/interrogazioniExt"/>
            <xs:element name="SearchLimitException" type="tns:SearchLimitException"/>
            <xs:element name="ricercaEnteComplete" type="tns:ricercaEnteComplete"/>
            <xs:element name="ricercaEnteCompleteResponse" type="tns:ricercaEnteCompleteResponse"/>
            <xs:element name="ricercaSoggettoComplete" type="tns:ricercaSoggettoComplete"/>
            <xs:element name="ricercaSoggettoCompleteResponse" type="tns:ricercaSoggettoCompleteResponse"/>
            <xs:complexType name="ricercaEnteComplete">
                <xs:sequence>
                    <xs:element minOccurs="0" name="tipo" type="xs:string"/>
                    <xs:element minOccurs="0" name="descrizione" type="xs:string"/>
                    <xs:element minOccurs="0" name="codiceEnte" type="xs:string"/>
                    <xs:element minOccurs="0" name="codiceFiscale" type="xs:string"/>
                    <xs:element minOccurs="0" name="indirizzoPec" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ricercaEnteCompleteResponse">
                <xs:sequence>
                    <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="ns1:ruoloente"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="indAbilitato">
                <xs:sequence>
                    <xs:element minOccurs="0" name="id" type="xs:string"/>
                    <xs:element minOccurs="0" name="email" type="xs:string"/>
                    <xs:element minOccurs="0" name="codiceFiscale" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="referente">
                <xs:sequence>
                    <xs:element minOccurs="0" name="idReferente" type="xs:string"/>
                    <xs:element minOccurs="0" name="nome" type="xs:string"/>
                    <xs:element minOccurs="0" name="cognome" type="xs:string"/>
                    <xs:element minOccurs="0" name="dataInserimento" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="dataUltimaModifica" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="codiceFiscale" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="tipologieEnti">
                <xs:sequence>
                    <xs:element minOccurs="0" name="id" type="xs:string"/>
                    <xs:element minOccurs="0" name="descrizione" type="xs:string"/>
                    <xs:element minOccurs="0" name="tipo" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="SearchLimitException">
                <xs:sequence>
                    <xs:element minOccurs="0" name="message" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ricercaSoggettoComplete">
                <xs:sequence>
                    <xs:element minOccurs="0" name="cognome" type="xs:string"/>
                    <xs:element minOccurs="0" name="nome" type="xs:string"/>
                    <xs:element minOccurs="0" name="codiceFiscale" type="xs:string"/>
                    <xs:element minOccurs="0" name="indirizzo" type="xs:string"/>
                    <xs:element minOccurs="0" name="codiceEnte" type="xs:string"/>
                    <xs:element minOccurs="0" name="orderBy" type="xs:string"/>
                    <xs:element minOccurs="0" name="asc" type="xs:boolean"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ricercaSoggettoCompleteResponse">
                <xs:sequence>
                    <xs:element maxOccurs="unbounded" minOccurs="0" name="return" type="ns1:soggetto"/>
                </xs:sequence>
            </xs:complexType>
        </xs:schema>
        <xs:schema xmlns:ns1="http://www.giustizia.it/serviziTelematici/reginde/interrogazioniInt"
                   xmlns:tns="http://www.giustizia.it/serviziTelematici/reginde/interrogazioniExt"
                   xmlns:xs="http://www.w3.org/2001/XMLSchema"
                   targetNamespace="http://www.giustizia.it/serviziTelematici/reginde/interrogazioniExt" version="1.0">
            <xs:import namespace="http://www.giustizia.it/serviziTelematici/reginde/interrogazioniInt"/>
            <xs:complexType name="ruoloente">
                <xs:sequence>
                    <xs:element minOccurs="0" name="classe" type="xs:string"/>
                    <xs:element minOccurs="0" name="codiceFiscale" type="xs:string"/>
                    <xs:element minOccurs="0" name="codice" type="xs:string"/>
                    <xs:element minOccurs="0" name="descrizione" type="xs:string"/>
                    <xs:element minOccurs="0" name="id" type="xs:string"/>
                    <xs:element maxOccurs="unbounded" minOccurs="0" name="indirizziAbilitati" type="ns1:indAbilitato"/>
                    <xs:element name="pubblicaAmministrazione" type="xs:boolean"/>
                    <xs:element minOccurs="0" name="pec" type="xs:string"/>
                    <xs:element minOccurs="0" name="partitaIVA" type="xs:string"/>
                    <xs:element minOccurs="0" name="referente" type="ns1:referente"/>
                    <xs:element minOccurs="0" name="ruolo" type="xs:string"/>
                    <xs:element minOccurs="0" name="stato" type="xs:string"/>
                    <xs:element minOccurs="0" name="tipologia" type="ns1:tipologieEnti"/>
                    <xs:element name="visibile" type="xs:boolean"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="soggetto">
                <xs:sequence>
                    <xs:element maxOccurs="unbounded" minOccurs="0" name="ruoliente" type="tns:ruoloente"/>
                    <xs:element maxOccurs="unbounded" minOccurs="0" name="indirizzi" type="tns:indirizzo"/>
                    <xs:element minOccurs="0" name="soggetto" type="tns:soggetti"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="indirizzo">
                <xs:sequence>
                    <xs:element minOccurs="0" name="cap" type="xs:string"/>
                    <xs:element minOccurs="0" name="comune" type="xs:string"/>
                    <xs:element minOccurs="0" name="email" type="xs:string"/>
                    <xs:element minOccurs="0" name="fax" type="xs:string"/>
                    <xs:element minOccurs="0" name="indirizzo" type="xs:string"/>
                    <xs:element minOccurs="0" name="prov" type="xs:string"/>
                    <xs:element minOccurs="0" name="telefono" type="xs:string"/>
                    <xs:element minOccurs="0" name="tp_indirizzo" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="soggetti">
                <xs:sequence>
                    <xs:element minOccurs="0" name="codFisc" type="xs:string"/>
                    <xs:element minOccurs="0" name="cognome" type="xs:string"/>
                    <xs:element minOccurs="0" name="dataNascita" type="xs:dateTime"/>
                    <xs:element minOccurs="0" name="DataScadenza" type="xs:string"/>
                    <xs:element minOccurs="0" name="Email" type="xs:string"/>
                    <xs:element minOccurs="0" name="Genere" type="xs:string"/>
                    <xs:element minOccurs="0" name="IdCard" type="xs:string"/>
                    <xs:element minOccurs="0" name="idSoggetto" type="xs:string"/>
                    <xs:element minOccurs="0" name="Indirizzo" type="xs:string"/>
                    <xs:element minOccurs="0" name="IndirizzoDigitale" type="xs:string"/>
                    <xs:element minOccurs="0" name="IvaCode" type="xs:string"/>
                    <xs:element minOccurs="0" name="luogoNascita" type="xs:string"/>
                    <xs:element minOccurs="0" name="NazioneNascita" type="xs:string"/>
                    <xs:element minOccurs="0" name="nome" type="xs:string"/>
                    <xs:element minOccurs="0" name="NomeCompagnia" type="xs:string"/>
                    <xs:element minOccurs="0" name="NumeroCellulare" type="xs:string"/>
                    <xs:element minOccurs="0" name="pec" type="xs:string"/>
                    <xs:element minOccurs="0" name="provNascita" type="xs:string"/>
                    <xs:element minOccurs="0" name="SedeLegale" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
        </xs:schema>
    </types>
    <message name="SearchLimitException">
        <part element="tns:SearchLimitException" name="SearchLimitException"/>
    </message>
    <message name="WsServiziInterrogazioneInterni_ricercaEnteComplete">
        <part element="tns:ricercaEnteComplete" name="ricercaEnteComplete"/>
    </message>
    <message name="WsServiziInterrogazioneInterni_ricercaSoggettoCompleteResponse">
        <part element="tns:ricercaSoggettoCompleteResponse" name="ricercaSoggettoCompleteResponse"/>
    </message>
    <message name="WsServiziInterrogazioneInterni_ricercaSoggettoComplete">
        <part element="tns:ricercaSoggettoComplete" name="ricercaSoggettoComplete"/>
    </message>
    <message name="WsServiziInterrogazioneInterni_ricercaEnteCompleteResponse">
        <part element="tns:ricercaEnteCompleteResponse" name="ricercaEnteCompleteResponse"/>
    </message>
    <portType name="WsServiziInterrogazioneInterni">
        <operation name="ricercaEnteComplete" parameterOrder="ricercaEnteComplete">
            <input message="tns:WsServiziInterrogazioneInterni_ricercaEnteComplete"/>
            <output message="tns:WsServiziInterrogazioneInterni_ricercaEnteCompleteResponse"/>
            <fault message="tns:SearchLimitException" name="SearchLimitException"/>
        </operation>
        <operation name="ricercaSoggettoComplete" parameterOrder="ricercaSoggettoComplete">
            <input message="tns:WsServiziInterrogazioneInterni_ricercaSoggettoComplete"/>
            <output message="tns:WsServiziInterrogazioneInterni_ricercaSoggettoCompleteResponse"/>
            <fault message="tns:SearchLimitException" name="SearchLimitException"/>
        </operation>
    </portType>
    <binding name="WsServiziInterrogazioneInterniBinding" type="tns:WsServiziInterrogazioneInterni">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="ricercaEnteComplete">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="SearchLimitException">
                <soap:fault name="SearchLimitException" use="literal"/>
            </fault>
        </operation>
        <operation name="ricercaSoggettoComplete">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="SearchLimitException">
                <soap:fault name="SearchLimitException" use="literal"/>
            </fault>
        </operation>
    </binding>
    <service name="WsServiziInterrogazioneInterni">
        <port binding="tns:WsServiziInterrogazioneInterniBinding" name="ServiziInterrogazioneInterniBeanPort">
            <soap:address location="http://pst.netserv.it/ServiziInterrogazioneRegindeExt/ServiziInterrogazioneInterni"/>
        </port>
    </service>
</definitions>