package it.netservice.csp.backend.excpetion;

import it.netservice.csp.backend.dto.ErrorTypeEnum;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.penale.model.sic.ElaborazioneDeposito;

/**
 *
 * <AUTHOR>
 */
public class CreateElaborazioneException extends ElaborazioneException {

    private ElaborazioneDeposito elaborazioneDeposito;

    public CreateElaborazioneException(ElaborazioneDeposito elaborazioneDeposito, String message) {
        super(message);
        this.elaborazioneDeposito = elaborazioneDeposito;
    }

    public CreateElaborazioneException(ElaborazioneDeposito elaborazioneDeposito, String message, RestErrorCodeEnum errorCode) {
        super(message, errorCode);
        this.elaborazioneDeposito = elaborazioneDeposito;
    }

    public CreateElaborazioneException(ElaborazioneDeposito elaborazioneDeposito, String message, RestErrorCodeEnum errorCode,
            ErrorTypeEnum errorType) {
        super(message, errorCode, errorType);
        this.elaborazioneDeposito = elaborazioneDeposito;
    }

    public CreateElaborazioneException(ElaborazioneDeposito elaborazioneDeposito, String message, RestErrorCodeEnum errorCode,
            Throwable cause) {
        super(message, errorCode, cause);
        this.elaborazioneDeposito = elaborazioneDeposito;
    }

    public CreateElaborazioneException(ElaborazioneDeposito elaborazioneDeposito, String message, RestErrorCodeEnum errorCode,
            Throwable cause, ErrorTypeEnum errorType) {
        super(message, errorCode, cause, errorType);
        this.elaborazioneDeposito = elaborazioneDeposito;
    }

    public CreateElaborazioneException(ElaborazioneDeposito elaborazioneDeposito, String message, RestErrorCodeEnum errorCode,
            String reason) {
        super(message, errorCode, reason);
        this.elaborazioneDeposito = elaborazioneDeposito;
    }

    public CreateElaborazioneException(ElaborazioneDeposito elaborazioneDeposito, String message, RestErrorCodeEnum errorCode,
            String reason, ErrorTypeEnum errorType) {
        super(message, errorCode, reason, errorType);
        this.elaborazioneDeposito = elaborazioneDeposito;
    }

    public CreateElaborazioneException(ElaborazioneDeposito elaborazioneDeposito, String message, RestErrorCodeEnum errorCode,
            Throwable cause, String reason) {
        super(message, errorCode, reason, cause);
        this.elaborazioneDeposito = elaborazioneDeposito;
    }

    public CreateElaborazioneException(ElaborazioneDeposito elaborazioneDeposito, String message, RestErrorCodeEnum errorCode,
            Throwable cause, String reason, ErrorTypeEnum errorType) {
        super(message, errorCode, reason, cause, errorType);
        this.elaborazioneDeposito = elaborazioneDeposito;
    }

    public ElaborazioneDeposito getElaborazioneDeposito() {
        return elaborazioneDeposito;
    }

}
