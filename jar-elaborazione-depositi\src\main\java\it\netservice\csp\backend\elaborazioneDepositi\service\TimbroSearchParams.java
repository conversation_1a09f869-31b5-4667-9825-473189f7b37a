package it.netservice.csp.backend.elaborazioneDepositi.service;

import it.netservice.common.rest.backend.CriteriaParametersBackend;
import it.netservice.penale.model.common.dettaglioricorso.Timbro;

/**
 *
 * <AUTHOR>
 */
public class TimbroSearchParams extends CriteriaParametersBackend<Timbro> {

    @SearchParameter(operator = Operator.EQUAL)
    String id = null;
    @SearchParameter(operator = Operator.EQUAL)
    Long nrg = null;
    @SearchParameter(operator = Operator.EQUAL)
    Long idUdienza = null;

    TimbroSearchParams(String id) {
        this.id = id;
    }

    public TimbroSearchParams(Long idUdienza, Long nrg) {
        this.idUdienza = idUdienza;
        this.nrg = nrg;
    }
}
