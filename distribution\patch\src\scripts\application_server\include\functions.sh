#
#	Funzioni di utilita' per l'installazione della patch
# $Id: functions.sh,v 1.1 2010/11/04 05:43:01 nicola.gaggi Exp $
#

# Copia del file
copy() {
	local fname
	fname=$2/$1

	if [ -d $1 ] ; then
		install --owner=$JBOSS_USER --group=$JBOSS_GROUP -d ${fname}
		pushd $1 > /dev/null
		for item in *; do copy $item ${fname}; done
		popd > /dev/null
	else
		install --owner=$JBOSS_USER --group=$JBOSS_GROUP $1 $2
	fi
}

# installazione singolo item
installItem() {
	echo "install $1 to $2" | tee -a $LOG_FILE
	copy $1 $2
}

# installazione
installDir() {
	if [ -d $1 ] && [ "$(ls -A $1)" ]; then
		pushd $1 > /dev/null
		for item in *; do installItem $item $2; done
    popd > /dev/null
	fi
}
