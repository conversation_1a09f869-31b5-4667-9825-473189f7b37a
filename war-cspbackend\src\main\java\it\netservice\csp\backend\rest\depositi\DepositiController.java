package it.netservice.csp.backend.rest.depositi;

import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.rmi.RemoteException;
import java.util.*;

import javax.activation.DataHandler;
import javax.annotation.security.RolesAllowed;
import javax.inject.Inject;
import javax.mail.util.ByteArrayDataSource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.xml.rpc.ServiceException;

import org.apache.log4j.Logger;
import org.jboss.resteasy.plugins.providers.multipart.InputPart;
import org.jboss.resteasy.plugins.providers.multipart.MultipartFormDataInput;

import com.auth0.jwt.interfaces.DecodedJWT;

import it.netservice.common.InputStreamDataSource;
import it.netservice.common.rest.PageInfo;
import it.netservice.common.rest.PagedResponse;
import it.netservice.common.rest.backend.CriteriaBuilderHelper;
import it.netservice.common.rest.backend.InputPayloadBackend;
import it.netservice.csp.backend.depositi.ContentDeposito;
import it.netservice.csp.backend.depositi.Esito;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.elaborazioneDepositi.service.DepositoService;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.DepositoException;
import it.netservice.csp.backend.excpetion.ElaborazioneException;
import it.netservice.csp.backend.ext.service.ServiceBuilder;
import it.netservice.csp.backend.pubblicazioneDepositi.PubblicazioneService;
import it.netservice.csp.backend.rest.AbstractCspBackendController;
import it.netservice.csp.backend.rest.auth.SecurityUtil;
import it.netservice.csp.backend.rest.auth.TokenManager;
import it.netservice.csp.backend.service.depositi.DepositiService;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.csp.backend.sic.service.repository.SicRepositoryException;
import it.netservice.csp.backend.ws.depositi.ContentSummaryType;
import it.netservice.csp.backend.ws.depositi.EnvSummaryType;
import it.netservice.csp.backend.ws.depositi.EventType;
import it.netservice.csp.backend.ws.depositi.LogEsiti;
import it.netservice.csp.backend.ws.depositoConsole.ServizioDepositoConsole;
import it.netservice.penale.model.common.UtentePenale;
import it.netservice.penale.model.common.dettaglioricorso.ProvvedimentoPenale;
import it.netservice.penale.model.common.dettaglioricorso.Timbro;
import it.netservice.penale.model.enums.StatoProvvedimento;
import it.netservice.penale.model.sic.Deposito;
import it.netservice.penale.model.sic.PenaleRicercaRiunitiView;
import it.netservice.penale.model.sic.Utente;
import it.netservice.penale.model.sic.criteria.csp.DepositiViewCriteriaParameters;
import it.netservice.penale.model.sic.criteria.csp.PenaleEsitoCriteriaParameters;
import it.netservice.penale.model.sic.csp.DepositoView;
import it.netservice.penale.model.sic.csp.PenaleEsito;

/**
 * Servizi di ricerca ricorsi per il magistrato penale.
 */
@RolesAllowed({ "CSPCLIENT" })
@Path("/depositi")
public class DepositiController extends AbstractCspBackendController {

    private static final Logger LOGGER = Logger.getLogger(DepositiController.class);

    private static final String INOLTRO_TEMPLATE = "<!DOCTYPE InfoInoltro SYSTEM 'http://schemi.processotelematico.giustizia.it/Schemi/InfoInoltro.dtd'>\n"
            + "<InfoInoltro>\n" + "    <IdMsgPdA>\n" + "        <CodicePdA />\n" + "        <Anno />\n"
            + "        <IdMsg />\n"
            + "    </IdMsgPdA>\n" + "    <Mittente ruolo=\"_RUOLO_\">\n"
            + "        <CodiceFiscale>_CF_</CodiceFiscale>\n"
            + "    </Mittente>\n" + "    <Destinatario>\n" + "        <CodiceUG>_CODUFF_</CodiceUG>\n"
            + "    </Destinatario>\n"
            + "    <IdMsgMitt>_MSGID_</IdMsgMitt>\n" + "</InfoInoltro>";

    @Inject
    private ServiceBuilder serviceBuilder;

    @Inject
    private DepositoService depositoService;

    @Inject
    private DepositiService depositiService;

    @Inject
    private SecurityUtil util;

    @Inject
    private TokenManager tokenManager;
    @Inject
    PubblicazioneService pubblicazioneService;

    @GET
    public Response getDeposito(@Context HttpServletRequest context, @QueryParam("idDeposito") Long idDeposito)
            throws CspBackendException, SicRepositoryException {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        SicPenaleRepository repo = new SicPenaleRepository(entityManager);
        try {
            if (idDeposito == null) {
                throw new CspBackendException("Riferimento deposito non corretto. idDeposito null",
                        RestErrorCodeEnum.ID_DEPOSITO_NULL,
                        433);
            }
            CriteriaBuilderHelper<DepositoView> criteriaBuilder = new CriteriaBuilderHelper<>(DepositoView.class);
            DepositiViewCriteriaParameters param = new DepositiViewCriteriaParameters();
            param.setIdCat(idDeposito);
            DepositoView deposito = criteriaBuilder.uniqueResult(entityManager, param);

            util.logComandoDeposito(util.getOperatore(tokenManager.verifyToken(context.getHeader("x-auth-token"))),
                    "get deposito",
                    idDeposito);
            util.checkSessionToValuedInToken(context);
            if (deposito.getStato().name().equals(StatoProvvedimento.ACCETTATO.name())
                    || deposito.getStato().name().equals(StatoProvvedimento.RIFIUTATO.name())) {
                ProvvedimentoPenale provvedimentoPenale = repo.findProvvedimentoByIdCat(deposito.getIdCat());
                // provvedimentoPenale.getIdProvv()
                if (provvedimentoPenale != null) {
                    UtentePenale utentePenale = null;
                    if (provvedimentoPenale.getStatoProvvedimento().equals(StatoProvvedimento.BUSTA_RIFIUTATA)) {
                        utentePenale = repo.findUtenteModificaByIdProvvedimento(provvedimentoPenale.getIdProvv(),
                                StatoProvvedimento.BUSTA_RIFIUTATA);
                        if (utentePenale != null) {
                            deposito.setUtenteInCarico(utentePenale.getNome() + " " + utentePenale.getCognome());
                            deposito.setDataRifiuto(provvedimentoPenale.getDataUltimaModifica().getTime());
                        }
                    } else if (provvedimentoPenale.getStatoProvvedimento()
                            .equals(StatoProvvedimento.MINUTA_ACCETTATA)) {
                        utentePenale = repo.findUtenteModificaByIdProvvedimento(provvedimentoPenale.getIdProvv(),
                                StatoProvvedimento.MINUTA_ACCETTATA);
                        if (utentePenale != null) {
                            deposito.setUtenteInCarico(utentePenale.getNome() + " " + utentePenale.getCognome());
                        }
                    } else if (provvedimentoPenale.getStatoProvvedimento().equals(StatoProvvedimento.PUBBLICATA)) {
                        utentePenale = repo.findUtenteModificaByIdProvvedimento(provvedimentoPenale.getIdProvv(),
                                StatoProvvedimento.PUBBLICATA);
                        if (utentePenale != null) {
                            deposito.setUtenteInCarico(utentePenale.getNome() + " " + utentePenale.getCognome());
                        }
                    }
                }
            }
            boolean oscuramentoSic = calcolareOscuramentoSicComplessivo(repo, entityManager,
                    deposito.getIdRicorsoUdienza());
            boolean depositoLavoratoSic = depositoService.isDepositoLavoratoDaSIC(entityManager, deposito.getTipo(),
                    deposito.getIdCat());
            deposito.setOscuramentoSic(oscuramentoSic ? 1L : deposito.getOscuramentoSic());
            deposito.setOscuramentoDeskCsp(calculateOscuramentoDeskCsp(entityManager, deposito));
            if (depositoLavoratoSic)
                deposito.setAnomalie(deposito.getAnomalie() + "-1");
            LOGGER.info("Ricerca del deposito " + idDeposito + " completata");
            return Response.ok(deposito).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la ricerca del deposito. idDeposito:" + idDeposito);
            throw new DepositoException("Errore durante la ricerca del deposito",
                    RestErrorCodeEnum.DEPOSITO_SEARCH_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    private Long calculateOscuramentoDeskCsp(EntityManager entityManager, DepositoView deposito)
            throws ElaborazioneException {
        Timbro timbroByNrgAndIdUienza = pubblicazioneService.findTimbroByNrgAndIdUienza(entityManager,
                deposito.getIdUdienza(),
                deposito.getNrg());
        if (timbroByNrgAndIdUienza != null) {
            String tipooscuramento = timbroByNrgAndIdUienza.getTipooscuramento();
            Long oscuramentoDeskCsp = deposito.getOscuramentoDeskCsp();
            if (!(tipooscuramento == null || tipooscuramento.equalsIgnoreCase("Nessuno"))) {
                oscuramentoDeskCsp = 1L;
            }
            return oscuramentoDeskCsp;
        }
        return deposito != null ? deposito.getOscuramentoDeskCsp() : 0L;
    }

    @POST
    @Path("/esporta")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.MEDIA_TYPE_WILDCARD)
    public Response esportaDepositi(@Context HttpServletRequest context,
            InputPayloadBackend<DepositiViewCriteriaParameters> inputPayload)
            throws CspBackendException, SicRepositoryException {
        LOGGER.info("Avvio recupero lista depositi");
        EntityManager entityManager = emfCaricamento.createEntityManager();
        SicPenaleRepository repo = new SicPenaleRepository(entityManager);
        try {
            tokenManager.verifyToken(context.getHeader("x-auth-token"));
            CriteriaBuilderHelper<DepositoView> criteriaBuilder = new CriteriaBuilderHelper<>(DepositoView.class);

            addCriteriaForDepositiSearchList(inputPayload);
            List<DepositoView> depositi = criteriaBuilder.resultList(entityManager,
                    inputPayload.getCriteriaParameters());

            if (depositi != null && inputPayload.getCriteriaParameters() != null
                    && inputPayload.getCriteriaParameters().getAnomalia() != null) {
                List<DepositoView> depositiTemp = new ArrayList<>();
                for (DepositoView deposito : depositi) {
                    if (deposito.getStato().name().equals(StatoProvvedimento.RIFIUTATO.name())) {
                        ProvvedimentoPenale provvedimentoPenale = repo.findProvvedimentoByIdCat(deposito.getIdCat());
                        if (provvedimentoPenale != null) {
                            UtentePenale utentePenale = repo.findUtenteModificaByIdProvvedimento(
                                    provvedimentoPenale.getIdProvv(),
                                    StatoProvvedimento.BUSTA_RIFIUTATA);
                            if (utentePenale != null) {
                                deposito.setUtenteInCarico(utentePenale.getNome() + " " + utentePenale.getCognome());
                                deposito.setDataRifiuto(provvedimentoPenale.getDataUltimaModifica().getTime());
                            }
                        }
                    }
                    String[] parts = deposito.getAnomalie().split("-");
                    if (inputPayload.getCriteriaParameters().getAnomalia().equalsIgnoreCase("1")) {
                        if (parts[0] == null || parts[0].equalsIgnoreCase("0")) {
                            depositiTemp.add(deposito);
                        }
                    } else if (inputPayload.getCriteriaParameters().getAnomalia().equalsIgnoreCase("2")) {
                        if (parts[1] == null || parts[1].equalsIgnoreCase("0")) {
                            depositiTemp.add(deposito);
                        }
                    } else if (inputPayload.getCriteriaParameters().getAnomalia().equalsIgnoreCase("3")) {
                        if (parts[2] == null || parts[2].equalsIgnoreCase("0")) {
                            depositiTemp.add(deposito);
                        }
                    }
                }
                depositi.removeAll(depositiTemp);
            } else if (depositi != null) {
                for (DepositoView deposito : depositi) {
                    if (deposito.getStato().name().equals(StatoProvvedimento.RIFIUTATO.name())) {
                        ProvvedimentoPenale provvedimentoPenale = repo.findProvvedimentoByIdCat(deposito.getIdCat());
                        if (provvedimentoPenale != null) {
                            LOGGER.info("Id provvedimento penale ricercato:" + provvedimentoPenale.getIdProvv());
                            UtentePenale utentePenale = repo.findUtenteModificaByIdProvvedimento(
                                    provvedimentoPenale.getIdProvv(),
                                    StatoProvvedimento.BUSTA_RIFIUTATA);
                            if (utentePenale != null) {
                                deposito.setUtenteInCarico(utentePenale.getNome() + " " + utentePenale.getCognome());
                                deposito.setDataRifiuto(provvedimentoPenale.getDataUltimaModifica().getTime());
                            }
                        }
                    }
                }
            }
            if (depositi == null || depositi.isEmpty())
                LOGGER.info("Lista depositi vuota");
            else
                LOGGER.info("Recupero lista depositi completata. Totale elementi trovati:" + depositi.size());

            return depositiService.esportaDepositi(depositi);

        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante l'esportazione dei dati");
            throw new DepositoException("Errore durante l'esportazione dei dati",
                    RestErrorCodeEnum.EXPORT_DEPOSITI_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    public Response listDepositi(@Context HttpServletRequest context,
            InputPayloadBackend<DepositiViewCriteriaParameters> inputPayload)
            throws CspBackendException, SicRepositoryException {
        LOGGER.info("Avvio recupero lista depositi");
        EntityManager entityManager = emfCaricamento.createEntityManager();
        SicPenaleRepository repo = new SicPenaleRepository(entityManager);
        try {
            tokenManager.verifyToken(context.getHeader("x-auth-token"));
            CriteriaBuilderHelper<DepositoView> criteriaBuilder = new CriteriaBuilderHelper<>(DepositoView.class);

            // Controllo se nei criteri di ordinamento viene passato numeroRicorso: in tal
            // caso l'ordinamento deve essere fatto anche per
            // annoRicorso
            List<PageInfo.Order> orderList = inputPayload.getPageInfo().getOrderList();
            inputPayload.getPageInfo().setOrderList(checkOrderList(orderList));

            addCriteriaForDepositiSearchList(inputPayload);
            PagedResponse pagedResponse = criteriaBuilder.resultListPaged(entityManager, inputPayload);
            List<DepositoView> depositi = (List<DepositoView>) pagedResponse.getContent();

            if (depositi != null && inputPayload.getCriteriaParameters() != null) {
                // PERFORMANCE OPTIMIZATION: Batch load all data to avoid N+1 queries
                LOGGER.info("BATCH OPTIMIZATION: Starting batch processing for " + depositi.size() + " deposits");
                Map<Long, Boolean> oscuramentoSicMap = calcolareOscuramentoSicComplessivoBatch(repo, entityManager,
                        depositi);
                LOGGER.info("BATCH OPTIMIZATION: Completed oscuramento SIC batch");
                Map<Long, Boolean> depositiLavoratiSicMap = getDepositiLavoratiSicBatch(entityManager, depositi);
                LOGGER.info("BATCH OPTIMIZATION: Completed depositi lavorati SIC batch");
                Map<String, Long> oscuramentoDeskCspMap = getOscuramentoDeskCspBatch(entityManager, depositi);
                LOGGER.info("BATCH OPTIMIZATION: Completed oscuramento desk/csp batch");

                Iterator<DepositoView> iterator = depositi.iterator();
                while (iterator.hasNext()) {
                    DepositoView deposito = iterator.next();

                    // Logica per StatoProvvedimento.RIFIUTATO
                    if (deposito.getStato().name().equals(StatoProvvedimento.RIFIUTATO.name())) {
                        ProvvedimentoPenale provvedimentoPenale = repo.findProvvedimentoByIdCat(deposito.getIdCat());
                        if (provvedimentoPenale != null) {
                            UtentePenale utentePenale = repo.findUtenteModificaByIdProvvedimento(
                                    provvedimentoPenale.getIdProvv(),
                                    StatoProvvedimento.BUSTA_RIFIUTATA);
                            if (utentePenale != null) {
                                deposito.setUtenteInCarico(utentePenale.getNome() + " " + utentePenale.getCognome());
                                deposito.setDataRifiuto(provvedimentoPenale.getDataUltimaModifica().getTime());
                            }
                        }
                    }
                    // Use pre-loaded data instead of individual queries
                    Boolean oscuramentoSic = oscuramentoSicMap.get(deposito.getIdRicorsoUdienza());
                    deposito.setOscuramentoSic(
                            oscuramentoSic != null && oscuramentoSic ? 1L : deposito.getOscuramentoSic());

                    String depositoKey = deposito.getNrg() + "_" + deposito.getIdUdienza();
                    Long oscuramentoDeskCsp = oscuramentoDeskCspMap.get(depositoKey);
                    if (oscuramentoDeskCsp != null) {
                        deposito.setOscuramentoDeskCsp(oscuramentoDeskCsp);
                    }

                    Boolean depositoLavoratoSic = depositiLavoratiSicMap.get(deposito.getIdCat());
                    boolean isLavoratoSic = depositoLavoratoSic != null && depositoLavoratoSic;
                    if (isLavoratoSic)
                        deposito.setAnomalie(deposito.getAnomalie() + "-1");

                    // TODO da riverificare quando ci sarà l'accettazione massiva
                    // Esclusione dei depositi già lavorati da SIC quando lo stato è "NEW"
                    if (inputPayload.getCriteriaParameters().getStato() != null
                            && inputPayload.getCriteriaParameters().getStato().equals(Deposito.Stato.NEW)) {

                        // Verifica se il deposito è stato lavorato da SIC
                        if (isLavoratoSic) {
                            iterator.remove();
                            continue;
                        }
                    }

                    // Gestione delle anomalie
                    if (inputPayload.getCriteriaParameters().getAnomalia() != null) {
                        String[] parts = deposito.getAnomalie().split("-");
                        if (inputPayload.getCriteriaParameters().getAnomalia().equalsIgnoreCase("1")) {
                            if (parts[0] == null || parts[0].equalsIgnoreCase("0")) {
                                iterator.remove();
                            }
                        } else if (inputPayload.getCriteriaParameters().getAnomalia().equalsIgnoreCase("2")) {
                            if (parts.length > 1 && (parts[1] == null || parts[1].equalsIgnoreCase("0"))) {
                                iterator.remove();
                            }
                        } else if (inputPayload.getCriteriaParameters().getAnomalia().equalsIgnoreCase("3")) {
                            if (parts.length > 2 && (parts[2] == null || parts[2].equalsIgnoreCase("0"))) {
                                iterator.remove();
                            }
                        }
                    }
                }
            }

            if (depositi == null || depositi.isEmpty()) {
                LOGGER.info("Lista depositi vuota");
            } else {
                LOGGER.info("Recupero lista depositi completata. Totale elementi trovati: " + depositi.size());
            }

            return Response.ok(pagedResponse).build();

        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la ricerca dei depositi", e);
            throw new DepositoException("Errore durante la ricerca dei depositi",
                    RestErrorCodeEnum.DEPOSITO_SEARCH_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    private List<PageInfo.Order> checkOrderList(List<PageInfo.Order> originalOrderList) {
        List<PageInfo.Order> newOrderList = new ArrayList<>();

        for (PageInfo.Order currentOrder : originalOrderList) {
            if (currentOrder.getPropertyName().equals("numeroRicorso")) {
                PageInfo.Order orderAnnoRicorso = new PageInfo.Order();
                orderAnnoRicorso.setPropertyName("annoRicorso");
                orderAnnoRicorso.setAsc(currentOrder.isAsc());
                newOrderList.add(orderAnnoRicorso);
            }
            newOrderList.add(currentOrder);
        }
        return newOrderList;
    }

    /**
     * PERFORMANCE OPTIMIZATION: Batch method to calculate oscuramento for multiple
     * deposits
     * This replaces the N+1 query pattern with efficient batch queries
     */
    private Map<Long, Boolean> calcolareOscuramentoSicComplessivoBatch(SicPenaleRepository repo,
            EntityManager entityManager,
            List<DepositoView> depositi) throws SicRepositoryException {
        Map<Long, Boolean> result = new HashMap<>();

        if (depositi == null || depositi.isEmpty()) {
            return result;
        }

        // Collect all idRicorsoUdienza values
        Set<Long> allIdRicUdien = new HashSet<>();
        for (DepositoView deposito : depositi) {
            if (deposito.getIdRicorsoUdienza() != null) {
                allIdRicUdien.add(deposito.getIdRicorsoUdienza());
            }
        }

        if (allIdRicUdien.isEmpty()) {
            return result;
        }

        // Batch query to get all ricorso riuniti data
        List<PenaleRicercaRiunitiView> allRicorsoRiunitiViews = repo
                .getRicorsoRiunitoViewByIdRicUdienPadreBatch(new ArrayList<>(allIdRicUdien));

        // Group by parent ID
        Map<Long, List<PenaleRicercaRiunitiView>> riunitiByParent = new HashMap<>();
        for (PenaleRicercaRiunitiView riuniti : allRicorsoRiunitiViews) {
            Long parentId = riuniti.getIdRicUdienPadre();
            if (!riunitiByParent.containsKey(parentId)) {
                riunitiByParent.put(parentId, new ArrayList<PenaleRicercaRiunitiView>());
            }
            riunitiByParent.get(parentId).add(riuniti);
        }

        // Collect all child IDs for batch esiti query
        Set<Long> allChildIds = new HashSet<>();
        for (List<PenaleRicercaRiunitiView> children : riunitiByParent.values()) {
            for (PenaleRicercaRiunitiView child : children) {
                allChildIds.add(child.getIdRicUdienza());
            }
        }

        // Batch query for all esiti with privacy = 'SI'
        Map<Long, Boolean> esitiWithPrivacy = new HashMap<>();
        if (!allChildIds.isEmpty()) {
            esitiWithPrivacy = getEsitiWithPrivacyBatch(entityManager, new ArrayList<>(allChildIds));
        }

        // Calculate result for each deposit
        for (DepositoView deposito : depositi) {
            Long idRicUdien = deposito.getIdRicorsoUdienza();
            boolean hasOscuramento = false;

            List<PenaleRicercaRiunitiView> children = riunitiByParent.get(idRicUdien);
            if (children != null && !children.isEmpty()) {
                LOGGER.info("Calcolo dell'oscuramento del sic per idRicUdien:" + idRicUdien);
                for (PenaleRicercaRiunitiView child : children) {
                    if (Boolean.TRUE.equals(esitiWithPrivacy.get(child.getIdRicUdienza()))) {
                        hasOscuramento = true;
                        break;
                    }
                }
            }

            result.put(idRicUdien, hasOscuramento);
        }

        return result;
    }

    /**
     * Helper method to batch query esiti with privacy = 'SI'
     */
    private Map<Long, Boolean> getEsitiWithPrivacyBatch(EntityManager entityManager, List<Long> allChildIds) {
        Map<Long, Boolean> result = new HashMap<>();

        if (allChildIds.isEmpty()) {
            return result;
        }

        // Process in batches to avoid Oracle IN clause limit (1000 items)
        int batchSize = 500;
        for (int i = 0; i < allChildIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, allChildIds.size());
            List<Long> batch = allChildIds.subList(i, endIndex);

            InputPayloadBackend<PenaleEsitoCriteriaParameters> inputPayload = new InputPayloadBackend<>();
            PenaleEsitoCriteriaParameters criteriaParametersPenaleEsito = new PenaleEsitoCriteriaParameters();
            criteriaParametersPenaleEsito.setIsdRicorsoUdienza(batch);
            inputPayload.setCriteriaParameters(criteriaParametersPenaleEsito);

            PageInfo pageInfo = new PageInfo();
            PageInfo.Order order = new PageInfo.Order();
            order.setPropertyName("idEsito");
            order.setAsc(true);
            pageInfo.setOrder(order);
            pageInfo.setPageSize(1000);
            inputPayload.setPageInfo(pageInfo);

            CriteriaBuilderHelper<PenaleEsito> criteriaBuilderPenaleEsito = new CriteriaBuilderHelper<>(
                    PenaleEsito.class);
            PagedResponse<PenaleEsito> pagedResponse = criteriaBuilderPenaleEsito.resultListPaged(entityManager,
                    inputPayload);
            List<PenaleEsito> listaEsitiRiuniti = (List<PenaleEsito>) pagedResponse.getContent();

            for (PenaleEsito esitoRiunito : listaEsitiRiuniti) {
                Long idRicUdien = esitoRiunito.getIdRicorsoUdienza();
                if (esitoRiunito.getPrivacy() != null && esitoRiunito.getPrivacy().getSigla().equalsIgnoreCase("SI")) {
                    result.put(idRicUdien, true);
                } else if (!result.containsKey(idRicUdien)) {
                    result.put(idRicUdien, false);
                }
            }
        }

        return result;
    }

    /**
     * PERFORMANCE OPTIMIZATION: Batch method to check if deposits were processed by
     * SIC
     */
    private Map<Long, Boolean> getDepositiLavoratiSicBatch(EntityManager entityManager, List<DepositoView> depositi) {
        Map<Long, Boolean> result = new HashMap<>();

        if (depositi == null || depositi.isEmpty()) {
            return result;
        }

        // Collect all idCat values
        List<Long> allIdCats = new ArrayList<>();
        for (DepositoView deposito : depositi) {
            if (deposito.getIdCat() != null) {
                allIdCats.add(deposito.getIdCat());
            }
        }

        if (allIdCats.isEmpty()) {
            return result;
        }

        // Process in batches to avoid Oracle IN clause limit
        int batchSize = 500;
        for (int i = 0; i < allIdCats.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, allIdCats.size());
            List<Long> batch = allIdCats.subList(i, endIndex);

            // Build IN clause for batch query
            StringBuilder inClause = new StringBuilder();
            for (int j = 0; j < batch.size(); j++) {
                if (j > 0)
                    inClause.append(",");
                inClause.append(batch.get(j));
            }

            Query query = entityManager.createNativeQuery(
                    "SELECT pp.FK_IDCAT, " +
                            "(CASE WHEN pts.DATAMINUTA IS NOT NULL AND DEPOSITO_TELEMATICO = 0 THEN 'S' ELSE 'N' END) AS DEPOSITATA, "
                            +
                            "(CASE WHEN pts.DATAPUBBL IS NOT NULL AND DEPOSITO_TELEMATICO = 0 THEN 'S' ELSE 'N' END) AS PUBBLICATA, "
                            +
                            "pp.ID_UDIEN, pp.NRG " +
                            "FROM PENALE_T_RICUDIEN pr " +
                            "JOIN PENALE_ESITO pte ON (pr.ID_RICUDIEN = pte.ID_RICUDIEN) " +
                            "JOIN PENALE_ESITOSENT pes ON (pte.ID_ESITO = pes.ID_ESITO) " +
                            "JOIN PENALE_T_SENTENZA pts ON (pes.ID_SENT = pts.ID_SENT) " +
                            "JOIN PENALE_PROVVEDIMENTI pp ON (pr.nrg = pp.NRG and pr.ID_UDIEN = pp.ID_UDIEN) " +
                            "WHERE pp.FK_IDCAT IN (" + inClause + ")");

            List<Object[]> batchResults = query.getResultList();

            // Process results for each deposit type
            for (DepositoView deposito : depositi) {
                if (!batch.contains(deposito.getIdCat()))
                    continue;

                boolean isLavorato = false;
                for (Object[] row : batchResults) {
                    Long idCat = ((Number) row[0]).longValue();
                    if (!idCat.equals(deposito.getIdCat()))
                        continue;

                    String depositata = (String) row[1];
                    String pubblicata = (String) row[2];
                    String tipoDeposito = deposito.getTipo();

                    // Apply same logic as original isDepositoLavoratoDaSIC method
                    if ((tipoDeposito.equalsIgnoreCase("sentenza") || tipoDeposito.equalsIgnoreCase("ordinanza"))
                            && "S".equals(pubblicata)) {
                        isLavorato = true;
                        break;
                    }

                    if ((tipoDeposito.equalsIgnoreCase("MinutaOrdinanza")
                            || tipoDeposito.equalsIgnoreCase("MinutaSentenza"))
                            && "S".equals(depositata)) {
                        if ("S".equals(pubblicata)) {
                            isLavorato = true;
                            break;
                        }
                        // For MinutaOrdinanza/MinutaSentenza, we need additional checks
                        // For simplicity, we'll mark as processed if depositata = 'S'
                        isLavorato = true;
                        break;
                    }
                }

                result.put(deposito.getIdCat(), isLavorato);
            }
        }

        return result;
    }

    /**
     * PERFORMANCE OPTIMIZATION: Batch method to get oscuramento desk/csp data
     */
    private Map<String, Long> getOscuramentoDeskCspBatch(EntityManager entityManager, List<DepositoView> depositi) {
        Map<String, Long> result = new HashMap<>();

        if (depositi == null || depositi.isEmpty()) {
            return result;
        }

        // Collect all nrg/idUdienza pairs
        Set<String> nrgUdienzaPairs = new HashSet<>();
        for (DepositoView deposito : depositi) {
            if (deposito.getNrg() != null && deposito.getIdUdienza() != null) {
                nrgUdienzaPairs.add(deposito.getNrg() + "_" + deposito.getIdUdienza());
            }
        }

        if (nrgUdienzaPairs.isEmpty()) {
            return result;
        }

        // Build batch query for timbri
        StringBuilder whereClause = new StringBuilder();
        boolean first = true;
        for (DepositoView deposito : depositi) {
            if (deposito.getNrg() != null && deposito.getIdUdienza() != null) {
                if (!first)
                    whereClause.append(" OR ");
                whereClause.append("(timbro0_.nrg=").append(deposito.getNrg())
                        .append(" AND timbro0_.ID_UDIEN=").append(deposito.getIdUdienza()).append(")");
                first = false;
            }
        }

        if (whereClause.length() > 0) {
            Query query = entityManager.createNativeQuery(
                    "SELECT timbro0_.nrg, timbro0_.ID_UDIEN, timbro0_.tipooscuramento " +
                            "FROM CSPBACKEND_TIMBRIPUBB timbro0_ " +
                            "WHERE " + whereClause.toString() + " " +
                            "ORDER BY timbro0_.ID_UDIEN DESC");

            List<Object[]> timbriResults = query.getResultList();

            // Process timbri results
            for (Object[] row : timbriResults) {
                Long nrg = ((Number) row[0]).longValue();
                Long idUdienza = ((Number) row[1]).longValue();
                String tipooscuramento = (String) row[2];

                String key = nrg + "_" + idUdienza;

                // Apply same logic as calculateOscuramentoDeskCsp
                Long oscuramentoDeskCsp = 0L;
                if (tipooscuramento != null && !tipooscuramento.equalsIgnoreCase("Nessuno")) {
                    oscuramentoDeskCsp = 1L;
                }

                result.put(key, oscuramentoDeskCsp);
            }
        }

        return result;
    }

    private boolean calcolareOscuramentoSicComplessivo(SicPenaleRepository repo, EntityManager entityManager,
            Long idRicUdien)
            throws SicRepositoryException {
        LOGGER.warn("PERFORMANCE WARNING: Using non-optimized calcolareOscuramentoSicComplessivo for idRicUdien: "
                + idRicUdien);
        List<PenaleRicercaRiunitiView> ricorsoRiunitoViewByIdRicUdienPadre = repo
                .getRicorsoRiunitoViewByIdRicUdienPadre(idRicUdien);
        if (ricorsoRiunitoViewByIdRicUdienPadre != null && !ricorsoRiunitoViewByIdRicUdienPadre.isEmpty()) {
            LOGGER.info("Calcolo dell'oscuramento del sic per idRicUdien:" + idRicUdien);
            List<Long> idsRicUdienFigli = new ArrayList<>();
            for (PenaleRicercaRiunitiView riunitiFigli : ricorsoRiunitoViewByIdRicUdienPadre) {
                idsRicUdienFigli.add(riunitiFigli.getIdRicUdienza());
            }
            InputPayloadBackend<PenaleEsitoCriteriaParameters> inputPayload = new InputPayloadBackend<>();
            PenaleEsitoCriteriaParameters criteriaParametersPenaleEsito = new PenaleEsitoCriteriaParameters();
            criteriaParametersPenaleEsito.setIsdRicorsoUdienza(idsRicUdienFigli);
            inputPayload.setCriteriaParameters(criteriaParametersPenaleEsito);
            PageInfo pageInfo = new PageInfo();
            PageInfo.Order order = new PageInfo.Order();
            order.setPropertyName("idEsito");
            order.setAsc(true);
            pageInfo.setOrder(order);
            pageInfo.setPageSize(200);
            inputPayload.setPageInfo(pageInfo);
            CriteriaBuilderHelper<PenaleEsito> criteriaBuilderPenaleEsito = new CriteriaBuilderHelper<>(
                    PenaleEsito.class);
            PagedResponse<PenaleEsito> pagedResponse = criteriaBuilderPenaleEsito.resultListPaged(entityManager,
                    inputPayload);
            List<PenaleEsito> listaEsitiRiuniti = (List<PenaleEsito>) pagedResponse.getContent();
            for (PenaleEsito esitoRiunito : listaEsitiRiuniti) {
                if (esitoRiunito.getPrivacy() != null && esitoRiunito.getPrivacy().getSigla().equalsIgnoreCase("SI")) {
                    return true;
                }
            }
        }
        return false;
    }

    private static void addCriteriaForDepositiSearchList(
            InputPayloadBackend<DepositiViewCriteriaParameters> inputPayload)
            throws CspBackendException {
        if (inputPayload.getCriteriaParameters().getStato() != null
                && inputPayload.getCriteriaParameters().getStato().equals(Deposito.Stato.PUBBLICATO)) {
            if (inputPayload.getCriteriaParameters().getTipoProvvedimento() != null
                    && (inputPayload.getCriteriaParameters().getTipoProvvedimento().equalsIgnoreCase("MinutaSentenza")
                            || inputPayload.getCriteriaParameters().getTipoProvvedimento()
                                    .equalsIgnoreCase("MinutaOrdinanza"))) {
                throw new DepositoException(
                        "Non è possibile ricercare per stato PUBBLICATO con la tipologia di atto selezionata.",
                        RestErrorCodeEnum.SEARCH_PUBBLICATO_NOT_ALLOWED);
            }
            if (inputPayload.getCriteriaParameters().getTipoProvvedimento() == null
                    || (inputPayload.getCriteriaParameters().getTipoProvvedimento() != null
                            && inputPayload.getCriteriaParameters().getTipoProvvedimento().equalsIgnoreCase(""))) {
                List<String> tipi = new ArrayList<>();
                tipi.add("Sentenza");
                tipi.add("Ordinanza");
                inputPayload.getCriteriaParameters().setTipiProvvedimento(tipi);
            }
            inputPayload.getCriteriaParameters().setStato(Deposito.Stato.ACCETTATO);
        } else if (inputPayload.getCriteriaParameters().getStato() != null
                && inputPayload.getCriteriaParameters().getStato().equals(Deposito.Stato.ACCETTATO)) {
            if (inputPayload.getCriteriaParameters().getTipoProvvedimento() != null
                    && (inputPayload.getCriteriaParameters().getTipoProvvedimento().equalsIgnoreCase("Sentenza")
                            || inputPayload.getCriteriaParameters().getTipoProvvedimento()
                                    .equalsIgnoreCase("Ordinanza"))) {
                throw new CspBackendException(
                        "Non è possibile ricercare per stato MINUTA ACCETTATA con la tipologia di atto selezionata.",
                        RestErrorCodeEnum.SEARCH_MINUTA_ACCETTATA_NOT_ALLOWED, 433);
            }
            if (inputPayload.getCriteriaParameters().getTipoProvvedimento() == null
                    || (inputPayload.getCriteriaParameters().getTipoProvvedimento() != null
                            && inputPayload.getCriteriaParameters().getTipoProvvedimento().equalsIgnoreCase(""))) {
                List<String> tipi = new ArrayList<>();
                tipi.add("MinutaSentenza");
                tipi.add("MinutaOrdinanza");
                inputPayload.getCriteriaParameters().setTipiProvvedimento(tipi);
            }
        }
    }

    @GET
    @Path("/esiti")
    public Response esitiDeposito(@Context HttpServletRequest context, @QueryParam("idDeposito") String idDeposito)
            throws CspBackendException, MalformedURLException, ServiceException, RemoteException {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            if (idDeposito == null) {
                throw new CspBackendException("Riferimento deposito non corretto. idDeposito null",
                        RestErrorCodeEnum.ID_DEPOSITO_NULL,
                        433);
            }

            util.logComandoDeposito(util.getOperatore(tokenManager.verifyToken(context.getHeader("x-auth-token"))),
                    "get esiti deposito",
                    new Long(idDeposito));
            // la RG (?ma anche altri?) può accettare su altri ricorsi

            LogEsiti service = serviceBuilder.getLogEsitiService();
            EnvSummaryType[] events = service.querySummary(new String[] { idDeposito }).getEnvSummary();

            if (events == null || events.length == 0) {
                LOGGER.error("Deposito non trovato sul GL. idDeposito:" + idDeposito);
                throw new CspBackendException("Deposito non trovato sul GL", RestErrorCodeEnum.GL_DEPOSITO_NOT_FOUND,
                        433);
            }
            EnvSummaryType envSummaryType = events[0];

            List<ContentDeposito> list = new ArrayList<>();
            ContentDeposito contentDepositoBusta = new ContentDeposito();
            contentDepositoBusta.setCatId(idDeposito);
            contentDepositoBusta.setTipo("Busta");
            list.add(contentDepositoBusta);

            if (envSummaryType.getContents().getContentSummary() != null) {
                for (ContentSummaryType contentSummary : envSummaryType.getContents().getContentSummary()) {
                    ContentDeposito contentDepositoAllegato = new ContentDeposito();
                    contentDepositoAllegato.setCatId(contentSummary.getCatId());
                    contentDepositoAllegato.setTipo(ContentDeposito.getTipo(contentSummary.getDocType()));
                    contentDepositoAllegato.setNome(contentSummary.getName());
                    list.add(contentDepositoAllegato);
                }
            }
            for (ContentDeposito contentDeposito : list) {
                EventType[] result = service.queryCatIdEvents(contentDeposito.getCatId()).getEvent();
                List<Esito> esiti = new ArrayList<>(result == null ? 0 : result.length);
                if (result != null) {
                    for (EventType event : result) {
                        String livello = event.getEvtClass();
                        Esito esito = new Esito();
                        esito.setTipoContent(contentDeposito.getTipo());
                        esito.setLivello(livello);
                        esito.setCodice(event.getEvtCode());
                        esito.setDescrizione(event.getEvtDesc());
                        esito.setData(fixTime(event.getTimeStamp()).getTimeInMillis());
                        esiti.add(esito);
                    }
                }
                Collections.sort(esiti);
                contentDeposito.setEsiti(esiti);
            }
            LOGGER.info(
                    "Ricerca esiti per deposito:" + idDeposito + " completata. Totale elementi trovati:" + list.size());
            return Response.ok(list).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la ricerca degli esiti del deposito. idDeposito:" + idDeposito);
            throw new DepositoException("Errore durante la ricerca degli esiti del deposito",
                    RestErrorCodeEnum.DEPOSITO_SEARCH_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET
    @Path("/contenuti")
    public Response contenutiDeposito(@Context HttpServletRequest context, @QueryParam("idDeposito") String idDeposito)
            throws CspBackendException, MalformedURLException, ServiceException, RemoteException {
        EntityManager entityManager = emfCaricamento.createEntityManager();

        try {
            util.logComandoDeposito(util.getOperatore(tokenManager.verifyToken(context.getHeader("x-auth-token"))),
                    "get contenuti",
                    new Long(idDeposito));
            util.checkSessionToValuedInToken(context, entityManager, new Long(idDeposito));

            return Response.ok(DepositoService.getContentDeposito(serviceBuilder, idDeposito)).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la ricerca dei contenuti del deposito. idDeposito:" + idDeposito);
            throw new DepositoException("Errore durante la ricerca dei contenuti del deposito",
                    RestErrorCodeEnum.DEPOSITO_SEARCH_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @POST
    @Path("/upload")
    @Consumes("multipart/form-data")
    public Response uploadDeposito(@Context HttpServletRequest context, MultipartFormDataInput input)
            throws IOException, CspBackendException {
        LOGGER.info("Richiesta caricamento busta");
        Map<String, List<InputPart>> uploadForm = input.getFormDataMap();
        String ruolo = uploadForm.get("ruolo").get(0).getBodyAsString();
        String cfMittente = uploadForm.get("codiceFiscale").get(0).getBodyAsString();
        String codUff = uploadForm.get("codiceUfficio").get(0).getBodyAsString();
        String msgId = uploadForm.get("idMessaggio").get(0).getBodyAsString();
        InputPart inputPart = uploadForm.get("file").get(0);
        try {

            DataHandler infoInoltro = generaInfoInoltro(ruolo, cfMittente, codUff, msgId);

            InputStream inputStream = inputPart.getBody(InputStream.class, null);
            DataHandler atto = new DataHandler(new InputStreamDataSource(inputStream));

            ServizioDepositoConsole depositoConsole = serviceBuilder.getDepositoConsoleService();
            String idBusta = depositoConsole.depositoConsole(infoInoltro, atto, null);
            LOGGER.info("Busta " + idBusta + " caricata correttamente.");
            return Response.ok("Busta " + idBusta + " caricata correttamente.").build();
        } catch (Exception e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante il caricamento dei depositi");
            throw new DepositoException("Errore durante il caricamento dei depositi",
                    RestErrorCodeEnum.DEPOSITO_GENERIC_ERROR, e);
        }
    }

    @GET
    @Path("/contaInCarico")
    public Response contaInCarico(@Context HttpServletRequest context)
            throws CspBackendException, SicRepositoryException {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            String operatore = (String) context.getSession().getAttribute("idOperatore");
            LOGGER.info("Conta depositi presi in carico per operatore " + operatore);
            SicPenaleRepository repo = new SicPenaleRepository(entityManager);
            Utente utente = repo.findUtenteByUsername(operatore);
            int inCarico = depositoService.countDepositiInCarico(entityManager, utente.getIdUtente());
            LOGGER.info("Totale depositi presi in carico per operatore " + operatore + ": " + inCarico);
            return Response.ok(inCarico).build();
        } catch (Exception e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la ricerca dei depositi presi in carico");
            throw new DepositoException("Errore durante la ricerca dei depositi presi in carico",
                    RestErrorCodeEnum.DEPOSITO_SEARCH_ERROR,
                    e);
        } finally {
            entityManager.close();
        }
    }

    @GET
    @Path("/presaInCarico")
    public Response presaInCarico(@Context HttpServletRequest context, @QueryParam("idDeposito") Long idDeposito)
            throws Exception {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            if (idDeposito == null) {
                throw new CspBackendException("Riferimento deposito non corretto. idDeposito null",
                        RestErrorCodeEnum.ID_DEPOSITO_NULL,
                        433);
            }

            DecodedJWT decodeJwt = tokenManager.verifyToken(context.getHeader("x-auth-token"));
            String operatore = util.getOperatore(decodeJwt);
            util.logComandoDeposito(operatore, "presa in carico", idDeposito);
            util.checkSessionToValuedInToken(context, entityManager, idDeposito);

            SicPenaleRepository repo = new SicPenaleRepository(entityManager);
            Utente utente = repo.findUtenteByUsername(operatore);
            if (utente == null) {
                throw new CspBackendException("Utente non trovato nella base dati", RestErrorCodeEnum.USER_NOT_FOUND,
                        433);
            }
            // conteggio non ci siano altri depositi in carico all' operatore
            /*
             * int inCarico = depositoService.countDepositiInCarico(entityManager,
             * utente.getIdUtente()); if (inCarico != 0) {
             * depositoService.rimuoviInCarico(entityManager, utente.getIdUtente()); }
             */
            depositoService.prendiInCarico(entityManager, idDeposito, utente.getIdUtente());

            LOGGER.info("Deposito " + idDeposito + " preso in carico correttamente da operatore " + operatore);
            return Response.ok("Deposito " + idDeposito + " preso in carico correttamente.").build();
        } catch (Exception e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la presa in carico del deposito. idDeposito:" + idDeposito);
            throw new DepositoException("Errore durante la presa in carico",
                    RestErrorCodeEnum.PRESA_IN_CARICO_GENERIC_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @POST
    @Path("/presaInCaricoMultipla")
    @Consumes(MediaType.APPLICATION_JSON)
    public Response presaInCaricoMultipla(@Context HttpServletRequest context,
            DepositiPrendiInCaricoMultiplaRequest request)
            throws Exception {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            if (request == null || request.getIdDepositi() == null || request.getIdDepositi().isEmpty()) {
                throw new CspBackendException("Lista depositi non valida", RestErrorCodeEnum.ID_DEPOSITO_NULL, 433);
            }

            DecodedJWT decodeJwt = tokenManager.verifyToken(context.getHeader("x-auth-token"));
            String operatore = util.getOperatore(decodeJwt);
            LOGGER.info("Richiesta presa in carico multipla di " + request.getIdDepositi().size()
                    + " depositi da operatore " + operatore);

            // Verifica sessione per ogni deposito
            for (Long idDeposito : request.getIdDepositi()) {
                util.checkSessionToValuedInToken(context, entityManager, idDeposito);
            }

            SicPenaleRepository repo = new SicPenaleRepository(entityManager);
            Utente utente = repo.findUtenteByUsername(operatore);
            if (utente == null) {
                throw new CspBackendException("Utente non trovato nella base dati", RestErrorCodeEnum.USER_NOT_FOUND,
                        433);
            }

            depositoService.prendiInCaricoMultipli(entityManager, request.getIdDepositi(), utente.getIdUtente());

            LOGGER.info(request.getIdDepositi().size() + " depositi presi in carico correttamente da operatore "
                    + operatore);
            return Response.ok("Depositi presi in carico correttamente.").build();
        } catch (Exception e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            throw e;
        } finally {
            entityManager.close();
        }
    }

    @GET
    @Path("/primoCronologico")
    public Response primoCronologico(@Context HttpServletRequest context) throws CspBackendException {
        LOGGER.info("Ricerca del primo deposito a livello cronologico");
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            InputPayloadBackend<DepositiViewCriteriaParameters> inputPayload = new InputPayloadBackend<>();
            inputPayload.setCriteriaParameters(new DepositiViewCriteriaParameters());
            CriteriaBuilderHelper<DepositoView> criteriaBuilder = new CriteriaBuilderHelper<>(DepositoView.class);
            // rispettare criteri visibilità per essere usabile in generale

            HashSet<Deposito.Stato> stati = new HashSet<>();
            stati.add(Deposito.Stato.NEW);
            inputPayload.getCriteriaParameters().setStati(stati);

            // skippare i bollini neri non lavorabili
            inputPayload.getCriteriaParameters().setAnomalie("%-%-0");

            // mettere in ordine cronologico crescente
            List<PageInfo.Order> ordersList = new ArrayList<>();
            PageInfo.Order o = new PageInfo.Order();
            o.setPropertyName("dataDeposito");
            o.setAsc(true);
            ordersList.add(o);

            DepositoView primoCronologico = criteriaBuilder.firstResult(entityManager, ordersList,
                    inputPayload.getCriteriaParameters());

            if (primoCronologico != null) {
                LOGGER.info("Ricerca conclusa. Deposito trovato:" + primoCronologico.getIdCat());
                return Response.ok(primoCronologico.getIdCat()).build();
            } else {
                LOGGER.info("Ricerca conclusa. Nessun deposito trovato");
                return Response.ok(-1).build();
            }
        } catch (Exception e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la ricerca del primo deposito");
            throw new DepositoException("Errore durante la ricerca del primo deposito",
                    RestErrorCodeEnum.DEPOSITO_SEARCH_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET
    @Path("/modificaTipoDeposito")
    public Response modificaTipoDeposito(@Context HttpServletRequest context, @QueryParam("idDeposito") Long idDeposito,
            @QueryParam("tipoDeposito") String tipoDeposito) throws Exception {
        LOGGER.info("Richiesta modifica per deposito " + idDeposito);
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            if (idDeposito == null) {
                throw new CspBackendException("Riferimento deposito non corretto. idDeposito null",
                        RestErrorCodeEnum.ID_DEPOSITO_NULL,
                        433);
            }
            util.logComandoDeposito(util.getOperatore(tokenManager.verifyToken(context.getHeader("x-auth-token"))),
                    "modifica tipo",
                    idDeposito);
            util.checkSessionToValuedInToken(context, entityManager, idDeposito);

            depositoService.modificaTipoDeposito(entityManager, idDeposito, tipoDeposito);
            LOGGER.info("Deposito " + idDeposito + " modificato di tipo correttamente");
            return Response.ok("Deposito " + idDeposito + " modificato di tipo correttamente.").build();
        } catch (Exception e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la modifica al tipo di deposito. idDeposito:" + idDeposito);
            throw new DepositoException("Errore durante la modifica al tipo di deposito",
                    RestErrorCodeEnum.DEPOSITO_GENERIC_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET
    @Path("/utenteInCarico")
    public Response utenteInCarico(@Context HttpServletRequest context, @QueryParam("idDeposito") Long idDeposito)
            throws CspBackendException {
        // EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            if (idDeposito == null) {
                throw new CspBackendException("Riferimento deposito non corretto. idDeposito null",
                        RestErrorCodeEnum.ID_DEPOSITO_NULL,
                        433);
            }
            // CriteriaBuilderHelper<DepositoView> criteriaBuilder = new
            // CriteriaBuilderHelper<>(DepositoView.class);
            // DepositiViewCriteriaParameters param = new DepositiViewCriteriaParameters();
            // param.setId(idDeposito);
            // DepositoView deposito = criteriaBuilder.uniqueResult(entityManager, param);
            //
            // util.logComandoDeposito(context, "utente in carico", idDeposito);
            // util.checkUtenzaDepositoView(context, deposito);
            //
            // if (deposito.getIdUtente() == null) {
            // return Response.ok("").build();
            // } else {
            // Utente utente = entityManager.find(Utente.class, deposito.getIdUtente());
            // return Response.ok(utente.getNome() + " " + utente.getCognome()).build();
            // }
            return Response.ok().build();

        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la ricerca");
            throw new DepositoException("Errore durante la ricerca", RestErrorCodeEnum.DEPOSITO_SEARCH_ERROR, e);
        } finally {
            // entityManager.close();
        }
    }

    @GET
    @Path("/depositoInCarico")
    public Response depositoInCarico(@Context HttpServletRequest context) throws CspBackendException {
        LOGGER.info("Ricerca depositi in carico");
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            String operatore = util.getOperatore(tokenManager.verifyToken(context.getHeader("x-auth-token")));
            SicPenaleRepository repo = new SicPenaleRepository(entityManager);
            Utente utente = repo.findUtenteByUsername(operatore);
            Long idInCarico = depositoService.getIdDepositoInCarico(entityManager, utente.getIdUtente());
            LOGGER.info("Depositi trovati in carico per utente:" + utente.getIdUtente() + ": " + idInCarico);
            return Response.ok(idInCarico).build();
        } catch (Exception e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la ricerca dei depositi");
            throw new DepositoException("Errore durante la ricerca dei depositi",
                    RestErrorCodeEnum.DEPOSITO_SEARCH_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    private DataHandler generaInfoInoltro(String ruolo, String cfMittente, String codUff, String msgId)
            throws IOException {
        String infoInoltro = INOLTRO_TEMPLATE.replaceAll("_RUOLO_", ruolo).replaceAll("_CF_", cfMittente)
                .replaceAll("_CODUFF_", codUff)
                .replaceAll("_MSGID_", msgId);
        return new DataHandler(new ByteArrayDataSource(infoInoltro, "application/xml"));
    }

}
