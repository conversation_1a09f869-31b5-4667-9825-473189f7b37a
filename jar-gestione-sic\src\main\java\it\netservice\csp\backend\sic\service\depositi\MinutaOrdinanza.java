package it.netservice.csp.backend.sic.service.depositi;

import java.text.ParseException;
import java.util.Date;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import it.netservice.csp.backend.common.Utils;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.CspCommonRestWarningException;
import it.netservice.csp.backend.excpetion.GestioneSicException;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.penale.model.common.dettaglioricorso.SentenzaDettaglio;
import it.netservice.penale.model.common.dettaglioricorso.UdienzaRicorso;
import it.netservice.penale.model.csp.ElaborazioneProvvedimento;
import it.netservice.penale.model.enums.TipoProvvedimentoMagistrato;
import it.netservice.penale.model.sic.PenaleParam;

/**
 * <AUTHOR>
 */
public class MinutaOrdinanza extends DepositoAbstract<ElaborazioneProvvedimento, SicPenaleRepository> {

    public MinutaOrdinanza(SicPenaleRepository repo, String operatore) throws GestioneSicException {
        super(repo, operatore);
    }

    @Override
    public void accetta(ElaborazioneProvvedimento elabDep) throws CspBackendException {
        String tipoProvv = elabDep.getTipoProvvedimento().name();
        try {
            insertAttoSuccessivo(elabDep, tipoProvv);
            accettaMinutaOrdinanza(elabDep);
        } catch (CspBackendException e) {
            throw e;
        } catch (Throwable t) {
            LOGGER.error("Errore nel salvataggio dei dati del ricorso. idElabDep" + elabDep.getId());
            throw new GestioneSicException("Errore nel salvataggio dei dati del ricorso", RestErrorCodeEnum.RICORSO_SAVE_ERROR, t);
        }
    }

    private void accettaMinutaOrdinanza(ElaborazioneProvvedimento elabDep) throws CspBackendException {
        if (elabDep == null) {
            return;
        }
        // LOGGING NRG/ID_UDIEN - Inizio accettaMinutaOrdinanza
        LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] accettaMinutaOrdinanza - START - IDCAT: %d", elabDep.getDeposito().getIdCat()));

        try {
            Long idRicUdien = null;
            // LOGGING NRG/ID_UDIEN - Controllo ricorso e UdienzaRicorso
            if (ricorso == null) {
                LOGGER.warn(String.format(
                        "[LOGGING NRG/ID_UDIEN] accettaMinutaOrdinanza - 'ricorso' field is NULL. IDCAT: %d. Impossibile determinare idRicUdien.",
                        elabDep.getDeposito().getIdCat()));
            } else {
                LOGGER.info(String.format(
                        "[LOGGING NRG/ID_UDIEN] accettaMinutaOrdinanza - 'ricorso' field NRG: %s. IDCAT: %d. Controllo UdienzaRicorso...",
                        ricorso.getNrg(), elabDep.getDeposito().getIdCat()));
                if (ricorso.getUdienzaRicorso() == null) {
                    LOGGER.warn(
                            String.format("[LOGGING NRG/ID_UDIEN] accettaMinutaOrdinanza - ricorso.getUdienzaRicorso() is NULL. IDCAT: %d",
                                    elabDep.getDeposito().getIdCat()));
                } else {
                    LOGGER.info(
                            String.format("[LOGGING NRG/ID_UDIEN] accettaMinutaOrdinanza - ricorso.getUdienzaRicorso() size: %d. IDCAT: %d",
                                    ricorso.getUdienzaRicorso().size(), elabDep.getDeposito().getIdCat()));
                    if (ricorso.getUdienzaRicorso().isEmpty()) {
                        LOGGER.warn(String.format(
                                "[LOGGING NRG/ID_UDIEN] accettaMinutaOrdinanza - ricorso.getUdienzaRicorso() is EMPTY. IDCAT: %d",
                                elabDep.getDeposito().getIdCat()));
                    }
                }
            }

            if (ricorso != null && ricorso.getUdienzaRicorso() != null) {
                // controllo se il ricorsoUdienza e solo uno, se non è solo una la vado a prelevare il ricorsoUdienza corretto con lastUdie
                // = 1 perche è l'ultimo lavorato
                if (ricorso.getUdienzaRicorso().size() == 1) {
                    UdienzaRicorso ur = ricorso.getUdienzaRicorso().iterator().next();
                    idRicUdien = ur.getIdUdieRicorso();
                    LOGGER.info(String.format(
                            "[LOGGING NRG/ID_UDIEN] accettaMinutaOrdinanza - Trovata una singola UdienzaRicorso. idUdieRicorso: %d, lastUdie: %b. IDCAT: %d",
                            idRicUdien, ur.isLastUdie(), elabDep.getDeposito().getIdCat()));
                } else if (!ricorso.getUdienzaRicorso().isEmpty()) {
                    LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] accettaMinutaOrdinanza - Itero su %d UdienzaRicorso. IDCAT: %d",
                            ricorso.getUdienzaRicorso().size(), elabDep.getDeposito().getIdCat()));
                    for (UdienzaRicorso udienzaRicorso : ricorso.getUdienzaRicorso()) {
                        LOGGER.info(String.format(
                                "[LOGGING NRG/ID_UDIEN] accettaMinutaOrdinanza - Controllo UdienzaRicorso: idUdieRicorso: %d, lastUdie: %b, Udienza ID: %d. IDCAT: %d",
                                udienzaRicorso.getIdUdieRicorso(), udienzaRicorso.isLastUdie(),
                                udienzaRicorso.getUdienza() != null ? udienzaRicorso.getUdienza().getId() : null,
                                elabDep.getDeposito().getIdCat()));
                        if (udienzaRicorso.isLastUdie()) {
                            idRicUdien = udienzaRicorso.getIdUdieRicorso();
                            LOGGER.info(String.format(
                                    "[LOGGING NRG/ID_UDIEN] accettaMinutaOrdinanza - Trovata UdienzaRicorso con lastUdie=true. idRicUdien impostato a: %d. IDCAT: %d",
                                    idRicUdien, elabDep.getDeposito().getIdCat()));
                            break;
                            // TODO potrebbe essere miglirato se non c'è nessun lastUdien a 1 allora potrei prendere quello con
                            // idRicordoUdien maggiore
                            // ho il ricorso principale se si tratta di un riunito
                        }
                    }
                }
            }
            // se non trovo nessun IdRicorsoUdienza mi spacco non a senso di esistere un deposito denza il ricordo udienza
            if (idRicUdien == null) {
                LOGGER.warn(String.format(
                        "[LOGGING NRG/ID_UDIEN] accettaMinutaOrdinanza - idRicUdien e' NULL dopo il ciclo di ricerca o ricorso/UdienzaRicorso era null. IDCAT: %d. Lancio CspCommonRestWarningException.",
                        elabDep.getDeposito().getIdCat()));
                throw new CspCommonRestWarningException("Nessuna sentenza trovata per il deposito selezionato.",
                        RestErrorCodeEnum.UPDATE_SENTENZA_ERROR);
            }
            // LOGGING NRG/ID_UDIEN - idRicUdien determinato (non nullo a questo punto)
            LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] accettaMinutaOrdinanza - idRicUdien determinato (non nullo): %d. IDCAT: %d",
                    idRicUdien, elabDep.getDeposito().getIdCat()));
            LOGGER.info(String.format(
                    "[LOGGING NRG/ID_UDIEN] accettaMinutaOrdinanza - Udienza ID (idRicUdien) che dovrebbe essere associato a IDCAT %d in PENALE_PROVVEDIMENTI (tramite PENALE_T_SENTENZA): %d",
                    elabDep.getDeposito().getIdCat(), idRicUdien));
            LOGGER.info(String.format(
                    "[LOGGING NRG/ID_UDIEN] accettaMinutaOrdinanza - Verifica scrittura PENALE_PROVVEDIMENTI: Nessuna scrittura esplicita a PENALE_PROVVEDIMENTI trovata in questo metodo. Aggiornamento primario su PENALE_T_SENTENZA. IDCAT: %d",
                    elabDep.getDeposito().getIdCat()));

            // mi prendo il dettaglio sentenza e controllo se la dataMinuta è valorizzata, allora lascio la stessa data minuta
            // altrimenti inserisco la dataMinuta corretta
            SentenzaDettaglio sentenzaDettaglio = findSentenzaDettaglioByIdUdienzaRicorso(idRicUdien); // Questo metodo ha già i suoi log
            if (sentenzaDettaglio == null) {
                LOGGER.warn(String.format(
                        "[LOGGING NRG/ID_UDIEN] accettaMinutaOrdinanza - findSentenzaDettaglioByIdUdienzaRicorso ha restituito NULL per idRicUdien: %d. IDCAT: %d",
                        idRicUdien, elabDep.getDeposito().getIdCat()));
                // Potrebbe essere necessario gestire questo caso se la logica successiva assume che sentenzaDettaglio non sia null
            } else {
                LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] accettaMinutaOrdinanza - SentenzaDettaglio trovato. ID: %s. IDCAT: %d",
                        sentenzaDettaglio.getId(), elabDep.getDeposito().getIdCat()));
            }
            Date dataMinuta = sentenzaDettaglio != null && sentenzaDettaglio.getDataMinuta() != null ? sentenzaDettaglio
                    .getDataMinuta() : new Date(elabDep.getDeposito().getDataDeposito());
            sentenzaDettaglio.setDataMinuta(Utils.convertDateTimeToDate(dataMinuta));
            sentenzaDettaglio.setIdFunzione(38L);
            sentenzaDettaglio.setDepositoTelamatico(1);
            repo.merge(sentenzaDettaglio);

            // in questo oggetti o solo il tipoSent dalla penale Param andrebbe inserito il join all'interno delle DetaglioPenaleParam
            PenaleParam tipoSentenza = repo.findEntity(PenaleParam.class, sentenzaDettaglio.getTiposent());
            if ((tipoSentenza == null || tipoSentenza.getSigla() == null || "SE".equals(tipoSentenza) || "OR".equals(tipoSentenza))
                    && !TipoProvvedimentoMagistrato.MinutaOrdinanza.equals(elabDep.getTipoProvvedimento())) {
                LOGGER.error(
                        "Il provvedimento che si sta cercando di pubblicare è differente rispetto a quanto scaricato sul SIC. idSentenza:"
                                + sentenzaDettaglio.getId() + ", tipo provvediemento SIC:" + tipoSentenza + ", tipo provvedimento:"
                                + elabDep.getTipoVerificato());
                throw new CspCommonRestWarningException(
                        "Il provvedimento che si sta cercando di accettare è differente rispetto a quanto scaricato sul SIC.",
                        RestErrorCodeEnum.TIPO_PROVV_NOT_MATCHING);
            }
        } catch (ParseException e) {
            LOGGER.error("Errore nella conversione della data minuta. idCat:" + elabDep.getDeposito().getIdCat(), e);
            throw new CspCommonRestWarningException("Errore nella conversione della data minuta", RestErrorCodeEnum.MINUTA_CONV_ERROR,
                    e.getMessage(), e);
        }
    }

    @Override
    protected void rifiuta(ElaborazioneProvvedimento elabDep) throws CspBackendException {
    }

    public SentenzaDettaglio findSentenzaDettaglioByIdUdienzaRicorso(Long idUdienzaRicorso) {
        LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] findSentenzaDettaglioByIdUdienzaRicorso - START - idUdienzaRicorso: %d",
                idUdienzaRicorso));
        if (idUdienzaRicorso == null) {
            LOGGER.warn(
                    "[LOGGING NRG/ID_UDIEN] findSentenzaDettaglioByIdUdienzaRicorso - idUdienzaRicorso ricevuto e' NULL. Restituisco null.");
            return null;
        }
        try {
            String sql = "SELECT sd.* FROM PENALE_T_SENTENZA sd " + "JOIN PENALE_T_ESITOSENT es ON sd.ID_SENT = es.ID_SENT "
                    + "JOIN PENALE_T_ESITO e ON es.ID_ESITO = e.ID_ESITO " + "JOIN PENALE_T_RICUDIEN ur ON e.ID_RICUDIEN = ur.ID_RICUDIEN "
                    + "WHERE ur.ID_RICUDIEN = :idUdienzaRicorso";

            Query query = repo.getEntityManager().createNativeQuery(sql, SentenzaDettaglio.class);
            query.setParameter("idUdienzaRicorso", idUdienzaRicorso);

            SentenzaDettaglio sentenza = (SentenzaDettaglio) query.getSingleResult();
            LOGGER.info(String.format(
                    "[LOGGING NRG/ID_UDIEN] findSentenzaDettaglioByIdUdienzaRicorso - SentenzaDettaglio Trovato. ID: %s per idUdienzaRicorso: %d",
                    sentenza != null ? sentenza.getId() : "null", idUdienzaRicorso));
            return sentenza;
        } catch (NoResultException nre) {
            LOGGER.warn(String.format(
                    "[LOGGING NRG/ID_UDIEN] findSentenzaDettaglioByIdUdienzaRicorso - NoResultException per idUdienzaRicorso: %d. Restituisco null.",
                    idUdienzaRicorso));
            return null;
        } catch (Exception e) {
            LOGGER.error(String.format(
                    "[LOGGING NRG/ID_UDIEN] findSentenzaDettaglioByIdUdienzaRicorso - Eccezione durante la ricerca per idUdienzaRicorso: %d",
                    idUdienzaRicorso), e);
            throw e; // Re-throw originale per non alterare il flusso di errore
        }
    }
}
