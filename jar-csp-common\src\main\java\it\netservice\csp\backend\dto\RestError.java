package it.netservice.csp.backend.dto;

public class RestError {
    RestErrorCodeEnum errorCode;
    String reason;
    String message;
    int status;
    ErrorTypeEnum errorType;

    public ErrorTypeEnum getErrorType() {
        return errorType;
    }

    public void setErrorType(ErrorTypeEnum errorType) {
        this.errorType = errorType;
    }

    public RestError(String message, RestErrorCodeEnum errorCode, ErrorTypeEnum errorType) {
        this.errorCode = errorCode;
        this.message = message;
        this.errorType = errorType;
    }

    public RestError(String message, RestErrorCodeEnum errorCode, String reason, ErrorTypeEnum errorType) {
        this.errorCode = errorCode;
        this.message = message;
        this.reason = reason;
        this.errorType = errorType;
    }

    public RestErrorCodeEnum getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(RestErrorCodeEnum errorCode) {
        this.errorCode = errorCode;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
