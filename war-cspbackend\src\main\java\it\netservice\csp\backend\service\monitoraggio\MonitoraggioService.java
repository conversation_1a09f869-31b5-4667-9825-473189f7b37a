package it.netservice.csp.backend.service.monitoraggio;

import static org.apache.poi.ss.usermodel.IndexedColors.BLACK;
import static org.apache.poi.ss.usermodel.IndexedColors.GREY_40_PERCENT;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.StreamingOutput;

import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import it.netservice.common.rest.PagedResponse;
import it.netservice.common.rest.backend.CriteriaBuilderHelper;
import it.netservice.common.rest.backend.InputPayloadBackend;
import it.netservice.csp.backend.common.Constats;
import it.netservice.csp.backend.common.Utils;
import it.netservice.csp.backend.dto.*;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.MonitoraggioException;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.csp.backend.utils.ExcelGenerator;
import it.netservice.penale.model.common.dettaglioricorso.ProvvedimentoNotePenale;
import it.netservice.penale.model.common.dettaglioricorso.ProvvedimentoPenale;
import it.netservice.penale.model.common.dettaglioricorso.SentenzaDettaglio;
import it.netservice.penale.model.common.dettaglioricorso.StoricoProvvedimento;
import it.netservice.penale.model.enums.StatoInserimentoNote;
import it.netservice.penale.model.enums.StatoProvvedimento;
import it.netservice.penale.model.sic.PenaleRicercaRiunitiView;
import it.netservice.penale.model.sic.PenaleUdienzeView;
import it.netservice.penale.model.sic.SicPenaleEntity;
import it.netservice.penale.model.sic.criteria.csp.PenaleUdienzeViewCriteriaParameters;
import it.netservice.penale.model.sic.csp.EstensoriSentenzaView;

/**
 * The MonitoraggioService class is responsible for handling the monitoring service functionality.
 */
public class MonitoraggioService {
    private static final Logger LOGGER = Logger.getLogger(MonitoraggioService.class);

    private static final String contentDispositionHeader = String.format("attachment; filename=\"%s\"", "esportazione.xlsx");

    private final String fontFamily = "Arial";
    private final short fontSizeTableResult = 10;
    private final short fontSizeTableCriteria = 8;
    private final short getRowHeightTableCriteria = 300;
    private final short rowHeightTableResult = 500;
    private final short borderSizeTableCriteria = 1;
    private final short borderSizeTableResult = 1;

    public MonitoraggioService() {
    }

    public PenaleUdienzeViewDto ricercaUdienze(InputPayloadBackend<PenaleUdienzeViewCriteriaParameters> inputPayload,
            EntityManager entityManager) throws CspBackendException {
        PenaleUdienzeViewDto penaleUdienzeViewDto = new PenaleUdienzeViewDto();
        PagedResponse<PenaleUdienzeView> pagedResponse;
        if (inputPayload.getCriteriaParameters().getCognomeParte() != null) {
            pagedResponse = findUdienzaByParte(inputPayload, penaleUdienzeViewDto, entityManager);
            if (pagedResponse == null)
                return penaleUdienzeViewDto;
        } else {
            // L'ordinamento per nrgreale o per numeroSezionale richiede l'ordinamento per anno e numero
            if (inputPayload.getPageInfo().getOrder().getPropertyName().equals("nrg")) {
                inputPayload.getPageInfo().getOrder().setPropertyName("annoRicorso,numeroRicorso");
            } else if (inputPayload.getPageInfo().getOrder().getPropertyName().equals("numeroSezionale")) {
                inputPayload.getPageInfo().getOrder().setPropertyName("annoSezionale,numeroSezionale");
            }
            // Occorre rimuovere dalla ricerca i riuniti: il campo riunito deve essere null o diverso da true
            inputPayload.getCriteriaParameters().setNoRiunito(true);

            CriteriaBuilderHelper<PenaleUdienzeView> criteriaBuilder = new CriteriaBuilderHelper<>(PenaleUdienzeView.class);
            pagedResponse = criteriaBuilder.resultListPaged(entityManager, inputPayload);
            // Nel caso l'ordine era stato modificato in numero,anno, reiserisco nrg/numeroSezionale come propertyName
            if (pagedResponse.getPageInfo().getOrder().getPropertyName().equals("annoRicorso,numeroRicorso")) {
                pagedResponse.getPageInfo().getOrder().setPropertyName("nrg");
            } else if (inputPayload.getPageInfo().getOrder().getPropertyName().equals("annoSezionale,numeroSezionale")) {
                inputPayload.getPageInfo().getOrder().setPropertyName("numeroSezionale");
            }
        }
        List<PenaleUdienzeView> udienze = (List<PenaleUdienzeView>) pagedResponse.getContent();
        if (udienze != null && udienze.size() > 0) {
            LOGGER.info("Udienze trovate: " + udienze.size());
            Integer counter = udienze.size();
            for (PenaleUdienzeView udienza : udienze) {
                if (udienza != null) {
                    Date dataUdienza = udienza.getDataUdienza();
                    dataUdienza.setHours(12);
                    dataUdienza.setMinutes(0);
                    dataUdienza.setMinutes(0);

                    if (udienza.getDataPubblicazione() == null) {
                        Date firstTime = new Date();
                        firstTime.setHours(12);
                        firstTime.setMinutes(0);
                        firstTime.setSeconds(0);

                        long differenzaInMillisecondi = firstTime.getTime() - dataUdienza.getTime();
                        long totaleGiorni = TimeUnit.MILLISECONDS.toDays(differenzaInMillisecondi);
                        udienza.setTotaleGiorni(String.valueOf(totaleGiorni));
                    } else {
                        Date dataPubblicazione = udienza.getDataPubblicazione();
                        dataPubblicazione.setHours(12);
                        dataPubblicazione.setMinutes(0);
                        dataPubblicazione.setMinutes(0);

                        long differenzaInMillisecondi = dataPubblicazione.getTime() - dataUdienza.getTime();
                        long totaleGiorni = TimeUnit.MILLISECONDS.toDays(differenzaInMillisecondi);
                        udienza.setTotaleGiorni(String.valueOf(totaleGiorni));
                    }
                    if (udienza.getRicercaRiunitiView() != null && udienza.getRicercaRiunitiView().getIdRicUdienPadre() != null) {
                        Map<String, Integer> mapAnnoNumero = Utils
                                .convertNrgRealeToAnnoAndNumero(udienza.getRicercaRiunitiView().getNrgRealePadre());
                        if (!mapAnnoNumero.isEmpty()) {
                            PenaleRicercaRiunitiView ricercaRiunitiView = udienza.getRicercaRiunitiView();
                            ricercaRiunitiView.setAnnoPadre(mapAnnoNumero.get(Constats.KEY_ANNO));
                            ricercaRiunitiView.setNumeroPadre(mapAnnoNumero.get(Constats.KEY_NUMERO));
                        }
                    }
                    if (udienza.getIdSentenza() == null) {
                        counter = counter - 1;
                    } else {
                        aggiornaStatoDaSic(entityManager, udienza);
                        if (udienza.getRicercaRiunitiView() != null && Utils.isTrue(udienza.getRicercaRiunitiView().getRiunito())) {
                            udienza.setStato("-");
                        }
                    }
                }
            }
            if (counter == 0) {
                penaleUdienzeViewDto.setMessage("Non sono presenti udienze chiuse per i criteri di ricerca impostati");
            }
        } else {
            if (inputPayload.getCriteriaParameters().getDataUdienzaDa() != null
                    && inputPayload.getCriteriaParameters().getNumeroRicorsoDa() == null) {
                penaleUdienzeViewDto.setMessage("Non ci sono udienze per i criteri di ricerca impostati");
            } else if (inputPayload.getCriteriaParameters().getNumeroRicorsoDa() != null
                    && inputPayload.getCriteriaParameters().getDataUdienzaDa() == null) {
                penaleUdienzeViewDto.setMessage("Non ci sono ricorsi per i criteri di ricerca impostati");
            } else {
                penaleUdienzeViewDto.setMessage("Non esistono risultati per i criteri di ricerca impostati");
            }
        }
        penaleUdienzeViewDto.setResponse(pagedResponse);
        LOGGER.info("Ricerca udienze completata. " + penaleUdienzeViewDto.getMessage() != null ? penaleUdienzeViewDto.getMessage() : "");
        return penaleUdienzeViewDto;
    }

    public Response esportaRisultatiRicercaSelezionati(InputPayloadBackend<PenaleUdienzeViewCriteriaParameters> inputPayload,
            EntityManager entityManager) throws CspBackendException {
        List<PenaleUdienzeView> udienze = getUdienze(inputPayload, entityManager);
        if (udienze == null || udienze.isEmpty()) {
            LOGGER.warn("Nessuna lista da esportare. Lista udienze null");
            return Response.ok().build();
        }
        LOGGER.info("Avvio procedura di export delle udienze trovate. Totale udienze: " + (udienze != null ? udienze.size() : 0));
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            LOGGER.info("Totale udienze da esportare: " + udienze.size());
            esportaRicercaRisultatiGenerali(udienze, entityManager, workbook);
            File f = new File("esportazione.xlsx");
            f.deleteOnExit();
            FileOutputStream outputStream = new FileOutputStream(f);
            workbook.write(outputStream);
            final byte[] content = Files.readAllBytes(f.toPath());
            StreamingOutput s = new StreamingOutput() {
                @Override
                public void write(OutputStream output) throws IOException {
                    output.write(content);
                }
            };
            LOGGER.info("Procedura di export delle udienze completata");
            return Response.ok(s).header("Content-Disposition", contentDispositionHeader)
                    .header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet").build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            throw new MonitoraggioException("Errore durante la generazione dei file", RestErrorCodeEnum.EXPORT_UDIENZE_ERROR, e);
        }
    }

    private void esportaRicercaRisultatiGenerali(List<PenaleUdienzeView> udienze, EntityManager entityManager, XSSFWorkbook workbook)
            throws NoSuchFieldException, IllegalAccessException, CspBackendException {
        Sheet sheet = workbook.createSheet("Monitoraggio");
        workbook.setSheetName(workbook.getSheetIndex(sheet), "Esportazione");
        ExcelGenerator excelGeneratorGenerali = new ExcelGenerator(sheet, workbook, 0);
        setExcelTitle(excelGeneratorGenerali, "MONITORAGGIO UDIENZE");
        // excelGeneratorGenerali.generateSpacer(1);
        setCriteriImpostatiTableFromList(excelGeneratorGenerali);
        setRisultatiRicercaTable(excelGeneratorGenerali, udienze, entityManager);
        createFooter(excelGeneratorGenerali);
    }

    public Response esportaMonitoraggio(InputPayloadBackend<PenaleUdienzeViewCriteriaParameters> inputPayload, EntityManager entityManager)
            throws CspBackendException {
        List<PenaleUdienzeView> udienze = getUdienze(inputPayload, entityManager);
        if (udienze == null || udienze.isEmpty()) {
            LOGGER.warn("Nessuna lista da esportare. Lista udienze null");
            return Response.ok().build();
        }
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            List<PenaleUdienzeDto> udienzeDto = new ArrayList<PenaleUdienzeDto>();
            for (PenaleUdienzeView udienza : udienze) {
                PenaleUdienzeDto penaleUdienzeDto = new PenaleUdienzeDto();
                Map<String, Date> resultList = getDateFascicolo(udienza.getIdProvvedimento(), String.valueOf(udienza.getIdSentenza()),
                        entityManager);
                penaleUdienzeDto.setDataUdienza(udienza.getDataUdienza());
                String dtUdienza = udienza.getDataUdienza() != null ? new SimpleDateFormat("dd/MM/yyyy")
                        .format(udienza.getDataUdienza()) : "--";
                penaleUdienzeDto.setUdienza(
                        udienza.getSezione() + " , " + dtUdienza + " , " + udienza.getTipoUdienza() + " , " + udienza.getCollegio());
                penaleUdienzeDto.setPresidente(udienza.getPresidente());
                penaleUdienzeDto.setRelatore(udienza.getRelatore());
                penaleUdienzeDto.setEstensore(udienza.getEstensore());
                penaleUdienzeDto.setDetParti(udienza.getDetParti());
                penaleUdienzeDto.setNumeroRicorso(udienza.getNumeroRicorso());
                penaleUdienzeDto.setAnnoRicorso(udienza.getAnnoRicorso());
                penaleUdienzeDto.setDataMinuta(udienza.getDataMinuta());
                penaleUdienzeDto.setDataMessaVisionePresidente(checkValueMap(resultList, "MESSA_DISPOSIZIONE_PRE"));
                penaleUdienzeDto.setDataRichiestaUltimaModifica(checkValueMap(resultList, "ULTIMA_MODIFICA"));
                penaleUdienzeDto.setDataDepositoUltimaMinutaModificata(checkValueMap(resultList, "ULTIMA_MINUTA_MODIFICA"));
                penaleUdienzeDto.setDataDepositoProvvedimento(checkValueMap(resultList, "DEPOSITO_PROVV"));
                penaleUdienzeDto.setDataPubblicazione(checkValueMap(resultList, "PUBBLICATA"));
                penaleUdienzeDto.setNraccg(udienza.getNraccg());
                penaleUdienzeDto.setStato(udienza.getStato());
                if (udienza.getDataPubblicazione() == null) {
                    long differenzaInMillisecondi = new Date().getTime() - udienza.getDataUdienza().getTime();
                    long totaleGiorni = TimeUnit.MILLISECONDS.toDays(differenzaInMillisecondi);
                    penaleUdienzeDto.setTotaleGiorni(String.valueOf(totaleGiorni));
                } else {
                    long differenzaInMillisecondi = udienza.getDataPubblicazione().getTime() - udienza.getDataUdienza().getTime();
                    long totaleGiorni = TimeUnit.MILLISECONDS.toDays(differenzaInMillisecondi);
                    penaleUdienzeDto.setTotaleGiorni(String.valueOf(totaleGiorni));
                }
                udienzeDto.add(penaleUdienzeDto);
            }

            esportaMonitoraggio(udienzeDto, entityManager, workbook);
            File f = new File("esportazione-monitoraggio.xlsx");
            f.deleteOnExit();
            FileOutputStream outputStream = new FileOutputStream(f);
            workbook.write(outputStream);
            final byte[] content = Files.readAllBytes(f.toPath());
            StreamingOutput s = new StreamingOutput() {
                @Override
                public void write(OutputStream output) throws IOException {
                    output.write(content);
                }
            };
            return Response.ok(s).header("Content-Disposition", contentDispositionHeader)
                    .header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet").build();
        } catch (Throwable e) {
            throw new MonitoraggioException("Errore durante la generazione dei file", RestErrorCodeEnum.EXPORT_UDIENZE_ERROR, e);
        }
    }

    private PagedResponse<PenaleUdienzeView> findUdienzaByParte(InputPayloadBackend<PenaleUdienzeViewCriteriaParameters> inputPayload,
            PenaleUdienzeViewDto penaleUdienzeViewDto, EntityManager entityManager) {
        PagedResponse<PenaleUdienzeView> pagedResponse = new PagedResponse<>();
        List<RicercaRicorsiKeysDto> listNrgAndIdRicudienByParte = getNrgsAndRicudienByParte(inputPayload, pagedResponse, entityManager);
        if (listNrgAndIdRicudienByParte == null || listNrgAndIdRicudienByParte.isEmpty()) {
            penaleUdienzeViewDto.setMessage("Non esistono risultati per i criteri di ricerca impostati");
            pagedResponse.setContent(new ArrayList<PenaleUdienzeView>());
            pagedResponse.setPageInfo(inputPayload.getPageInfo());
            penaleUdienzeViewDto.setResponse(pagedResponse);
            return null;
        }
        List<PenaleUdienzeView> udienze = findUdienzeByNrgAndRicudienList(listNrgAndIdRicudienByParte, entityManager);
        pagedResponse.setContent(udienze);
        return pagedResponse;
    }

    private List<RicercaRicorsiKeysDto> getNrgsAndRicudienByParte(InputPayloadBackend<PenaleUdienzeViewCriteriaParameters> inputPayload,
            PagedResponse<PenaleUdienzeView> pagedResponse, EntityManager entityManager) {
        RicercaPartiParams ricercaPartiParams = new RicercaPartiParams(inputPayload.getCriteriaParameters().getCognomeParte());
        ricercaPartiParams.setSezione(inputPayload.getCriteriaParameters().getSezione());
        ricercaPartiParams.setDataUdDa(inputPayload.getCriteriaParameters().getDataUdienzaDa());
        ricercaPartiParams.setDataUdA(inputPayload.getCriteriaParameters().getDataUdienzaA());
        ricercaPartiParams.setTipoUd(inputPayload.getCriteriaParameters().getTipoUdienza());
        ricercaPartiParams.setCollegio(inputPayload.getCriteriaParameters().getCollegio());
        ricercaPartiParams.setCfPresidente(inputPayload.getCriteriaParameters().getCfPresidente());
        ricercaPartiParams.setCfEstensore(inputPayload.getCriteriaParameters().getCfEstensore());
        String nrgrealeDa = (inputPayload.getCriteriaParameters().getNumeroRicorsoDa() != null
                && inputPayload.getCriteriaParameters().getAnnoRicorsoDa() != null) ? SicPenaleEntity.formatNrgReale(
                        String.valueOf(inputPayload.getCriteriaParameters().getNumeroRicorsoDa()),
                        String.valueOf(inputPayload.getCriteriaParameters().getAnnoRicorsoDa()), null) : null;
        ricercaPartiParams.setNrgrealeDa(nrgrealeDa);
        String nrgrealeA = (inputPayload.getCriteriaParameters().getNumeroRicorsoA() != null
                && inputPayload.getCriteriaParameters().getAnnoRicorsoA() != null) ? SicPenaleEntity.formatNrgReale(
                        String.valueOf(inputPayload.getCriteriaParameters().getNumeroRicorsoA()),
                        String.valueOf(inputPayload.getCriteriaParameters().getAnnoRicorsoA()), null) : null;
        ricercaPartiParams.setNrgrealeA(nrgrealeA);
        ricercaPartiParams.setDataMinutaDa(inputPayload.getCriteriaParameters().getDataMinutaDa());
        ricercaPartiParams.setDataMinutaA(inputPayload.getCriteriaParameters().getDataMinutaA());
        ricercaPartiParams.setDataPubblicazioneDa(inputPayload.getCriteriaParameters().getDataPubblicazioneDa());
        ricercaPartiParams.setDataPubblicazioneA(inputPayload.getCriteriaParameters().getDataPubblicazioneA());
        ricercaPartiParams.setRemoveRiuniti(true);
        ricercaPartiParams.setOrderBy(inputPayload.getPageInfo().getOrder().getPropertyName());
        ricercaPartiParams.setOrder(inputPayload.getPageInfo().getOrder().isAsc() ? "ASC" : "DESC");
        int firstResult = inputPayload.getPageInfo().getPageNumber() * inputPayload.getPageInfo().getPageSize();

        SicPenaleRepository repo = new SicPenaleRepository(entityManager);
        List<RicercaRicorsiKeysDto> result = repo.getRicorsiByCognomeParte(ricercaPartiParams, firstResult,
                inputPayload.getPageInfo().getPageSize());
        if (result != null && !result.isEmpty()) {
            int pageSize = inputPayload.getPageInfo().getPageSize();
            long totalRecords = repo.calculateTotRecordsRicorsiByParte(ricercaPartiParams);
            int totalPages = (int) ((totalRecords + pageSize - 1) / pageSize);
            inputPayload.getPageInfo().setTotPages(totalPages);
            inputPayload.getPageInfo().setTotRecordsCount(totalRecords);
            pagedResponse.setPageInfo(inputPayload.getPageInfo());
        }

        return result;
    }

    private List<PenaleUdienzeView> findUdienzeByNrgAndRicudienList(List<RicercaRicorsiKeysDto> listNrgAndIdRicudienByParte,
            EntityManager entityManager) {
        List<Long> nrgList = new ArrayList<>();
        for (RicercaRicorsiKeysDto nrgAndRicudien : listNrgAndIdRicudienByParte) {
            nrgList.add(nrgAndRicudien.getNrg());
        }
        SicPenaleRepository repo = new SicPenaleRepository(entityManager);
        return repo.getUdienzeByNrgList(nrgList);
    }

    private List<PenaleUdienzeView> getUdienze(InputPayloadBackend<PenaleUdienzeViewCriteriaParameters> inputPayload,
            EntityManager entityManager) throws CspBackendException {
        CriteriaBuilderHelper<PenaleUdienzeView> criteriaBuilder = new CriteriaBuilderHelper<>(PenaleUdienzeView.class);
        // inputPayload.getPageInfo().getOrder().setPropertyName(inputPayload.getPageInfo().getOrder().getPropertyName()
        // + ",nrg");
        List<PenaleUdienzeView> udienze = null;
        try {
            PagedResponse<PenaleUdienzeView> pagedResponse = criteriaBuilder.resultListPaged(entityManager, inputPayload);
            udienze = (List<PenaleUdienzeView>) pagedResponse.getContent();
            if (udienze != null) {
                for (PenaleUdienzeView udienza : udienze) {
                    Date dataUdienza = udienza.getDataUdienza();
                    dataUdienza.setHours(12);
                    dataUdienza.setMinutes(0);
                    dataUdienza.setMinutes(0);

                    if (udienza.getDataPubblicazione() == null) {
                        Date firstTime = new Date();
                        firstTime.setHours(12);
                        firstTime.setMinutes(0);
                        firstTime.setSeconds(0);

                        long differenzaInMillisecondi = firstTime.getTime() - dataUdienza.getTime();
                        long totaleGiorni = TimeUnit.MILLISECONDS.toDays(differenzaInMillisecondi);
                        udienza.setTotaleGiorni(String.valueOf(totaleGiorni));
                    } else {
                        Date dataPubblicazione = udienza.getDataPubblicazione();
                        dataPubblicazione.setHours(12);
                        dataPubblicazione.setMinutes(0);
                        dataPubblicazione.setMinutes(0);

                        long differenzaInMillisecondi = dataPubblicazione.getTime() - dataUdienza.getTime();
                        long totaleGiorni = TimeUnit.MILLISECONDS.toDays(differenzaInMillisecondi);
                        udienza.setTotaleGiorni(String.valueOf(totaleGiorni));
                    }
                    aggiornaStatoDaSic(entityManager, udienza);
                }
            }
        } catch (Exception e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            throw e;
        }
        return udienze;
    }

    private static void aggiornaStatoDaSic(EntityManager entityManager, PenaleUdienzeView udienza) {
        SentenzaDettaglio sentenzaDettaglio = getSentenzaDettaglio(entityManager, udienza.getIdSentenza() + "");
        if (sentenzaDettaglio.getDataPubbl() != null && sentenzaDettaglio.getnRaccg() != null
                && sentenzaDettaglio.getPubblicazioneTelematico().equals(0)) {
            udienza.setStato(StatoProvvedimento.PUBBLICATO_SIC.name());
        } else if (sentenzaDettaglio.getDataPubbl() != null && !"pubblicata".equalsIgnoreCase(udienza.getStato())) {
            udienza.setStato(StatoProvvedimento.PROVV_DEPOSITATO_SIC.name());
        } else if (sentenzaDettaglio.getDataMinuta() != null && sentenzaDettaglio.getDepositoTelamatico().equals(0)) {
            udienza.setStato(StatoProvvedimento.MINUTA_DEPOSITATA_SIC.name());
        }
    }

    private Date checkValueMap(Map<String, Date> resultList, String key) {
        if (resultList != null && resultList.containsKey(key)) {
            return resultList.get(key);
        }
        return null;
    }

    private void esportaMonitoraggio(List<PenaleUdienzeDto> udienzeDto, EntityManager entityManager, XSSFWorkbook workbook)
            throws NoSuchFieldException, IllegalAccessException, CspBackendException {
        Sheet sheet = workbook.createSheet("Monitoraggio");
        ExcelGenerator excelGeneratorDettaglio = new ExcelGenerator(sheet, workbook, 0);
        setExcelTitle(excelGeneratorDettaglio, "MONITORAGGIO NAVETTA");
        // excelGeneratorDettaglio.generateSpacer(1);
        setCriteriImpostatiTableFromListMonitoraggio(excelGeneratorDettaglio);
        setRisultatiMonitoraggioTable(excelGeneratorDettaglio, udienzeDto, entityManager);
        excelGeneratorDettaglio.generateSpacer(1);
        createFooter(excelGeneratorDettaglio);
    }

    private void setCriteriImpostatiTableFromList(ExcelGenerator excelGenerator) {
        excelGenerator.setFont(fontFamily, GREY_40_PERCENT.getIndex(), fontSizeTableCriteria, true);
        excelGenerator.createCellStyle(IndexedColors.WHITE.getIndex(), (short) 1, borderSizeTableCriteria);
        // excelGenerator.generateSimpleRow("Criteri Impostati");
        excelGenerator.setFont(fontFamily, BLACK.getIndex(), fontSizeTableCriteria, false);
        excelGenerator.createCellStyle(IndexedColors.WHITE.getIndex(), (short) 1, borderSizeTableCriteria);
        // List<String>[] tableSearchParameters =
        // createTableFromSelezioneMonitoraggio(selezioneMonitoraggio);
        // excelGenerator.generateTableFromColumns(tableSearchParameters[0].size(),
        // getRowHeightTableCriteria, tableSearchParameters);
        excelGenerator.setFont(fontFamily, BLACK.getIndex(), fontSizeTableCriteria, true);
        excelGenerator.createCellStyle(IndexedColors.GREY_25_PERCENT.getIndex(), (short) 1, borderSizeTableCriteria);
        excelGenerator.generateTableFromRows(2, getRowHeightTableCriteria,
                Arrays.asList("Data estrazione", String.valueOf(new SimpleDateFormat("dd/MM/yyyy").format(new Date()))));
    }

    private void setCriteriImpostatiTableFromListMonitoraggio(ExcelGenerator excelGenerator) {
        excelGenerator.setFont(fontFamily, GREY_40_PERCENT.getIndex(), fontSizeTableCriteria, true);
        excelGenerator.createCellStyle(IndexedColors.WHITE.getIndex(), (short) 1, borderSizeTableCriteria);
        // excelGenerator.generateSimpleRow("Criteri Impostati");
        excelGenerator.setFont(fontFamily, BLACK.getIndex(), fontSizeTableCriteria, false);
        excelGenerator.createCellStyle(IndexedColors.WHITE.getIndex(), (short) 1, borderSizeTableCriteria);
        // List<String>[] tableSearchParameters =
        // createTableFromSelezioneMonitoraggio(selezioneMonitoraggio);
        // excelGenerator.generateTableFromColumns(tableSearchParameters[0].size(),
        // getRowHeightTableCriteria, tableSearchParameters);
        excelGenerator.setFont(fontFamily, BLACK.getIndex(), fontSizeTableCriteria, true);
        excelGenerator.createCellStyle(IndexedColors.GREY_25_PERCENT.getIndex(), (short) 1, borderSizeTableCriteria);
        excelGenerator.generateTableFromRows(2, getRowHeightTableCriteria,
                Arrays.asList("Data estrazione", String.valueOf(new SimpleDateFormat("dd/MM/yyyy").format(new Date()))));
    }

    private void setRisultatiMonitoraggioTable(ExcelGenerator excelGenerator, List<PenaleUdienzeDto> udienzeDto,
            EntityManager entityManager) throws NoSuchFieldException, IllegalAccessException, CspBackendException {
        excelGenerator.generateSpacer(1);
        excelGenerator.setFont(fontFamily, BLACK.getIndex(), (short) fontSizeTableResult, true);
        excelGenerator.createCellStyle(IndexedColors.WHITE.getIndex(), (short) 1, borderSizeTableResult);
        List<String> nomiColonne = Arrays.asList("Sezione", "Presidente", "Relatore", "Estensore", "Parti", "Nrg", "Data minuta",
                "Data messa in visione presidente", "Data richiesta ultima modifica", "Data deposito ultima minuta modificata",
                "Data deposito provvedimento", "Data pubblicazione", "Stato", "Num. Racc. Gen", "Totale giorni");
        excelGenerator.generateTableFromRows(nomiColonne.size(), rowHeightTableResult, nomiColonne);
        excelGenerator.setFont(fontFamily, BLACK.getIndex(), (short) 7, false);
        excelGenerator.createCellStyle(IndexedColors.WHITE.getIndex(), (short) 1, borderSizeTableResult);
        excelGenerator.generateTableFromRows(nomiColonne.size(), rowHeightTableResult,
                createTableMonitoraggioFromList(udienzeDto, entityManager));
    }

    private void setRisultatiRicercaTable(ExcelGenerator excelGenerator, List<PenaleUdienzeView> udienze, EntityManager entityManager)
            throws NoSuchFieldException, IllegalAccessException, CspBackendException {
        excelGenerator.generateSpacer(1);
        excelGenerator.setFont(fontFamily, BLACK.getIndex(), (short) fontSizeTableResult, true);
        excelGenerator.createCellStyle(IndexedColors.WHITE.getIndex(), (short) 1, borderSizeTableResult);
        List<String> nomiColonne = Arrays.asList("Sezione", "Data udienza", "Tipo", "Collegio", "Presidente", "Relatore", "Estensore",
                "Parti", "Nrg", "Sezionale", "Tipo provv", "Stato", "Num. Racc. Gen", "Totale giorni");
        excelGenerator.generateTableFromRows(nomiColonne.size(), rowHeightTableResult, nomiColonne);
        excelGenerator.setFont(fontFamily, BLACK.getIndex(), (short) 7, false);
        excelGenerator.createCellStyle(IndexedColors.WHITE.getIndex(), (short) 1, borderSizeTableResult);
        excelGenerator.generateTableFromRows(nomiColonne.size(), rowHeightTableResult,
                createTableRicercaRisultatiFromList(udienze, entityManager));
    }

    private String convertStateInString(StatoProvvedimento stato) {
        if (stato != null) {
            switch (stato) {
                // Bozza
                case BOZZA:
                    return "Bozza";
                case IN_BOZZA:
                    return "In Bozza";
                case BOZZA_PRESIDENTE:
                    return "Bozza dal Presidente";

                // Code
                case IN_CODE_FIRMA_REL:
                    return "In Coda di Firma del Relatore";
                case CODA_DI_FIRMA:
                    return "In Coda di Firma";

                // Chi lo ha iniato in cancelleria
                case INVIATO_IN_CANCELLERIA_RELATORE:
                    return "Inviato in Cancellaria dal Relatore";
                case INVIATO_IN_CANCEL_PRESIDENTE:
                    return "Inviato in Cancellaria dal Presidente";

                case ACCETTATO:
                    return "Accettato";
                case RIFIUTATO:
                    return "Rifiutato";
                case PUBBLICATA:
                    return "Pubblicato";

                case NEW:
                    return "Nuovo";
                case BUSTA_RIFIUTATA:
                    return "Busta Rifiutata";

                // Tipi di minute
                case MINUTA_ACCETTATA:
                    return "Minuta Accettata e inoltrata al Presidente";
                case MINUTA_DA_MODIFICARE:
                    return "Minuta Da Modificare";
                case MINUTA_MODIFICATA:
                    return "Minuta Modificata";
                case MINUTA_DEPOSITATA_SIC:
                    return "Minuta Depositata da Sic";
                case MINUTA_MODIFICATA_PRESIDENTE:
                    return "Minuta Modificata dal Presidente";
                case MINUTA_DEPOSITATA:
                    return "Minuta Depositata";

                case RIUNITO_CARTACEO:
                    return "Da redigere in cartaceo";

                // Sic
                case PROVV_DEPOSITATO_SIC:
                    return "Depositato dal Sic";
                case PUBBLICATO_SIC:
                    return "Pubblicato dal Sic";

                // Errore Publl.
                case ERRORE_DI_PUBBLICAZIONE:
                    return "Errore di Pubblicazione";

                default:
                    return "Stato Non definito";
            } // switch
        }
        return "";
    }// convertStateInString

    private List<String>[] createTableRicercaRisultatiFromList(List<PenaleUdienzeView> penaleUdienzeView, EntityManager entityManager)
            throws CspBackendException {
        List<String>[] table = new List[penaleUdienzeView.size()];
        for (int i = 0; i < penaleUdienzeView.size(); i++) {
            PenaleUdienzeView mnvTmp = penaleUdienzeView.get(i);
            StatoProvvedimento statoEnum = StatoProvvedimento.getEnumByString(mnvTmp.getStato());
            // check if stato is defined
            if (statoEnum == null) {
                LOGGER.warn("Stato non definito. stato:" + mnvTmp.getStato());
            }

            // Modifica per includere numero sezionale e anno sezionale
            String sezionale = mnvTmp.getNumeroSezionale() != null && mnvTmp.getAnnoSezionale() != null ? mnvTmp.getNumeroSezionale()
                    + " - " + mnvTmp.getAnnoSezionale() : null;

            String nrgreale = SicPenaleEntity.formatNrgReale(String.valueOf(mnvTmp.getNumeroRicorso()),
                    String.valueOf(mnvTmp.getAnnoRicorso()), null);
            String parti = generateParti(nrgreale, mnvTmp.getDetParti(), entityManager);

            table[i] = Arrays.asList(getStringIfNotNull(penaleUdienzeView.get(i).getSezione()),
                    getStringIfNotNull(new SimpleDateFormat("dd/MM/yyyy").format(mnvTmp.getDataUdienza())),
                    getStringIfNotNull(mnvTmp.getTipoUdienza() == null ? null : mnvTmp.getTipoUdienza()),
                    getStringIfNotNull(mnvTmp.getCollegio()), getStringIfNotNull(mnvTmp.getPresidente()),
                    getStringIfNotNull(mnvTmp.getRelatore()), getStringIfNotNull(mnvTmp.getEstensore()), getStringIfNotNull(parti),
                    getStringIfNotNull(mnvTmp.getNumeroRicorso() == null
                            || mnvTmp.getAnnoRicorso() == null ? null : mnvTmp.getNumeroRicorso() + "/" + mnvTmp.getAnnoRicorso()),
                    getStringIfNotNull(sezionale), getStringIfNotNull(mnvTmp.getTipoRicorso()),
                    getStringIfNotNull(convertStateInString(statoEnum)), getStringIfNotNull(mnvTmp.getNraccg()),
                    getStringIfNotNull(mnvTmp.getTotaleGiorni()));
        }
        return table;
    }

    private List<String>[] createTableMonitoraggioFromList(List<PenaleUdienzeDto> udienzeDto, EntityManager entityManager)
            throws CspBackendException {
        List<String>[] table = new List[udienzeDto.size()];
        for (int i = 0; i < udienzeDto.size(); i++) {
            PenaleUdienzeDto mnvTmp = udienzeDto.get(i);
            StatoProvvedimento statoEnum = StatoProvvedimento.getEnumByString(mnvTmp.getStato());
            // Like up check if stato is defined
            if (statoEnum == null) {
                LOGGER.warn("Stato non definito. stato:" + mnvTmp.getStato());
            }
            String nrgreale = SicPenaleEntity.formatNrgReale(String.valueOf(mnvTmp.getNumeroRicorso()),
                    String.valueOf(mnvTmp.getAnnoRicorso()), null);
            String parti = generateParti(nrgreale, mnvTmp.getDetParti(), entityManager);

            String dtMinuta = mnvTmp.getDataMinuta() != null ? new SimpleDateFormat("dd/MM/yyyy").format(mnvTmp.getDataMinuta()) : null;
            String dtMessaVisionePresidente = mnvTmp.getDataMessaVisionePresidente() != null ? new SimpleDateFormat("dd/MM/yyyy HH.mm")
                    .format(mnvTmp.getDataMessaVisionePresidente()) : null;
            String dtRichiestaUltimaModifica = mnvTmp.getDataRichiestaUltimaModifica() != null ? new SimpleDateFormat("dd/MM/yyyy HH.mm")
                    .format(mnvTmp.getDataRichiestaUltimaModifica()) : null;
            String dtRichiestaUltimaMinutaModifica = mnvTmp.getDataDepositoUltimaMinutaModificata() != null ? new SimpleDateFormat(
                    "dd/MM/yyyy HH.mm").format(mnvTmp.getDataDepositoUltimaMinutaModificata()) : null;
            String dtDepositoProvvedimento = mnvTmp.getDataDepositoProvvedimento() != null ? new SimpleDateFormat("dd/MM/yyyy HH.mm")
                    .format(mnvTmp.getDataDepositoProvvedimento()) : null;
            String nraccg = mnvTmp.getNraccg();
            String dtPubblicazione = mnvTmp.getDataPubblicazione() != null ? new SimpleDateFormat("dd/MM/yyyy")
                    .format(mnvTmp.getDataPubblicazione()) : null;

            table[i] = Arrays.asList(getStringIfNotNull(udienzeDto.get(i).getUdienza()), getStringIfNotNull(mnvTmp.getPresidente()),
                    getStringIfNotNull(mnvTmp.getRelatore()), getStringIfNotNull(mnvTmp.getEstensore()), getStringIfNotNull(parti),
                    getStringIfNotNull(mnvTmp.getNumeroRicorso() == null
                            || mnvTmp.getAnnoRicorso() == null ? null : mnvTmp.getNumeroRicorso() + "/" + mnvTmp.getAnnoRicorso()),
                    getStringIfNotNull(dtMinuta), getStringIfNotNull(dtMessaVisionePresidente),
                    getStringIfNotNull(dtRichiestaUltimaModifica), getStringIfNotNull(dtRichiestaUltimaMinutaModifica),
                    getStringIfNotNull(dtDepositoProvvedimento), getStringIfNotNull(dtPubblicazione),
                    getStringIfNotNull(convertStateInString(statoEnum)), getStringIfNotNull(nraccg),
                    getStringIfNotNull(mnvTmp.getTotaleGiorni()));
        }
        return table;
    }

    /**
     * Genera la stringa contenente le parti. Se le parti del ricorso sono più di 2, viene aggiunto oltre al testo con le parti (preso dal
     * campo devParti del parametro) il testo 'e altre'
     *
     * @param nrgreale
     *            nrgreale del fascicolo
     * @param parti
     *            contiene le informazioni delle parti da visualizzare
     * @param entityManager
     *            entity manager per esecuzione delle query
     * @return la stringa contenente il testo da generare nel file Excel
     */
    private String generateParti(String nrgreale, String parti, EntityManager entityManager) throws CspBackendException {
        SicPenaleRepository repo = new SicPenaleRepository(entityManager);
        int numParti = repo.countPartiByNrgReale(nrgreale);
        return parti + (numParti > 2 ? " e altre" : "");
    }

    private <T extends Serializable> String getStringIfNotNull(T value) {
        if (value == null)
            return "";
        return String.valueOf(value);
    }

    private void createFooter(ExcelGenerator excelGenerator) {
        excelGenerator.setDefaultStyle();
        excelGenerator.generateSimpleRow(
                "Nota: Il totale giorni indica la differenza tra la data odierna e la data dell'ultimo evento. Se il provvedimento è pubblicato il "
                        + "totale giorni indica la differenza tra la data di pubblicazione e la data udienza");
        excelGenerator.generateSimpleRow(
                "Nota: Gli eventi verificatesi tramite il SIC sono visibili nel monitoraggio della navetta attraverso gli stati: minuta depositata da SIC, provvedimento depositato da SIC, pubblicato da SIC");
    }

    private void setExcelTitle(ExcelGenerator excelGenerator, String title) {
        excelGenerator.setFont(fontFamily, BLACK.getIndex(), (short) 12, true);
        excelGenerator.createCellStyle(IndexedColors.WHITE.getIndex(), (short) 1, (short) 0);
        excelGenerator.generateSimpleRow(title);
    }

    public static List<StoricoProvvedimento> getStoricoProvvedimento(String idProvv, EntityManager entityManager)
            throws CspBackendException {
        if (idProvv == null) {
            throw new CspBackendException("Nessun provvedimento indicato. idProvv null", RestErrorCodeEnum.ID_PROVV_NULL, 433);
        }
        LOGGER.info("Recupero storico per provvedimento " + idProvv);
        try {
            Query query = entityManager.createNativeQuery(
                    "SELECT ppcs.* FROM PENALE_PROVV_CHANGE_STATUS ppcs WHERE ppcs.ID_PROVV IN (SELECT *"
                            + "   FROM TABLE(get_provvedimenti_collegati(:idProvv))) ORDER BY ppcs.DATE_CHANGE DESC",
                    StoricoProvvedimento.class);
            query.setParameter("idProvv", idProvv);
            List<StoricoProvvedimento> resultList = query.getResultList();
            for (Iterator<StoricoProvvedimento> iter = resultList.listIterator(); iter.hasNext();) {
                StoricoProvvedimento pro = iter.next();
                if (pro.getStato().equals(StatoProvvedimento.BOZZA) || pro.getStato().equals(StatoProvvedimento.IN_CODE_FIRMA_REL)
                        || pro.getStato().equals(StatoProvvedimento.CODA_DI_FIRMA)
                        || pro.getStato().equals(StatoProvvedimento.BOZZA_PRESIDENTE)) {
                    iter.remove();
                } else if (pro.getStato().equals(StatoProvvedimento.BUSTA_RIFIUTATA)) {
                    pro.setNote(getNoteProvvedimento(pro.getIdProvv(), null, entityManager));
                } else if (pro.getStato().equals(StatoProvvedimento.MINUTA_ACCETTATA)) {
                    pro.setNote(getNoteProvvedimento(pro.getIdProvv(), StatoInserimentoNote.MINUTA_ACCETTATA, entityManager));
                }
            }
            LOGGER.info("Recupero storico per idProvv:" + idProvv + "completato");
            return resultList;
        } catch (Exception e) {
            LOGGER.error("Errore durante il recupero dello storico del provvedimento. idProvv:" + idProvv);
            throw new MonitoraggioException("Errore durante il recupero dello storico del provvedimento",
                    RestErrorCodeEnum.STORICO_PROVV_ERROR, e);
        }
    }

    public static String getNoteProvvedimento(String idProvv, StatoInserimentoNote tipoInserimento, EntityManager entityManager)
            throws CspBackendException {
        if (idProvv == null) {
            throw new CspBackendException("Nessun provvedimento indicato. idProvv null", RestErrorCodeEnum.ID_PROVV_NULL, 433);
        }
        try {
            Query query = entityManager.createNativeQuery("SELECT * FROM PENALE_PROVV_NOTE ppn WHERE ppn.ID_PROVV = :idProvv",
                    ProvvedimentoNotePenale.class);
            query.setParameter("idProvv", idProvv);
            List<ProvvedimentoNotePenale> noteList = query.getResultList();
            if (noteList == null || noteList.isEmpty()) {
                return null;
            }

            // Se è stato indicato il tipo inserimento verrà restituita solo la nota col tipo indicato, altrimenti restituisce null
            if (tipoInserimento != null) {
                for (ProvvedimentoNotePenale nota : noteList) {
                    if (nota.getStatoInserimento() != null && nota.getStatoInserimento().equals(tipoInserimento))
                        return nota.getNote();
                }
                return null;
            }
            return noteList.get(0).getNote();
        } catch (Exception e) {
            LOGGER.error("Errore durante il recupero delle note del provvedimento. idProvv:" + idProvv);
            throw new MonitoraggioException("Errore durante il recupero delle note del provvedimento",
                    RestErrorCodeEnum.STORICO_PROVV_ERROR, e);
        }

    }

    public int fromSic(String nrg, EntityManager entityManager) {
        Query query = entityManager.createNativeQuery(
                "SELECT DEPOSITO_TELEMATICO,PUBBLICATO_TELEMATICO " + "FROM(SELECT ID_RICUDIEN,DEPOSITO_TELEMATICO,PUBBLICATO_TELEMATICO "
                        + "FROM V_RICERCA_PENALE_SENTENZA vrps) rps " + "JOIN PENALE_RICUDIEN pr ON PR.ID_RICUDIEN = RPS.ID_RICUDIEN "
                        + "JOIN(SELECT NRG, NRGREALE FROM PENALE_RICORSO WHERE NRGREALE = :nrg) " + "PRO ON PR.NRG = PRO.NRG");
        query.setParameter("nrg", nrg);
        Object[] singleResult = (Object[]) query.getSingleResult();
        int result = 0;
        int singleResult0 = ((BigDecimal) (singleResult[0])).intValue();
        int singleResult1 = ((BigDecimal) (singleResult[1])).intValue();
        result += singleResult0;
        result += singleResult1 * 2;
        return result;
    }

    public Map<BigDecimal, Integer> fromSicList(List<String> nrg, EntityManager entityManager) {
        Map<BigDecimal, Integer> result = new HashMap<>();
        StringBuilder nrgStr = new StringBuilder(nrg.get(0));
        if (nrg.size() > 1)
            for (int i = 1; i < nrg.size(); i++)
                nrgStr.append("','").append(nrg.get(i));
        Query query = entityManager.createNativeQuery("SELECT DEPOSITO_TELEMATICO,PUBBLICATO_TELEMATICO, NRGREALE "
                + "FROM(SELECT ID_RICUDIEN,DEPOSITO_TELEMATICO,PUBBLICATO_TELEMATICO " + "FROM V_RICERCA_PENALE_SENTENZA vrps) rps "
                + "JOIN PENALE_RICUDIEN pr ON PR.ID_RICUDIEN = RPS.ID_RICUDIEN " + "JOIN (SELECT NRG, NRGREALE FROM PENALE_RICORSO "
                + "WHERE NRGREALE IN ('" + nrgStr + "')) PRO ON PR.NRG = PRO.NRG");
        List<Object[]> resultList = query.getResultList();
        for (Object[] el : resultList) {
            int value = 0;
            int singleResult0 = ((BigDecimal) (el[0])).intValue();
            int singleResult1 = ((BigDecimal) (el[1])).intValue();
            BigDecimal nrgK = (BigDecimal) el[2];
            value += singleResult0;
            value += singleResult1 * 2;
            result.put(nrgK, value);
        }
        return result;
    }

    /**
     * Retrieves and processes historical provvedimento data to obtain relevant dates associated with the provvedimento.
     *
     * @param idProvv
     *            The ID of the provvedimento.
     * @param idSentenza
     *            The ID of the sentenza.
     * @param entityManager
     *            The EntityManager instance.
     * @return A map containing the relevant dates associated with the provvedimento.
     */
    public Map<String, Date> getDateFascicolo(String idProvv, String idSentenza, EntityManager entityManager) throws CspBackendException {
        LOGGER.info("Recupero date fascicolo per provvedimento " + idProvv + " e sentenza " + idSentenza);
        if (idProvv != null) {
            List<StoricoProvvedimento> storicoProvvedimento = getStoricoProvvedimento(idProvv, entityManager);
            List<StoricoProvvedimento> storicoProvvedimento2 = new ArrayList<StoricoProvvedimento>(storicoProvvedimento);

            String tipoProvv = getTipoProvvByIdProvv(entityManager, idProvv);

            Map<String, Date> statiMap = new HashMap<>();
            Collections.reverse(storicoProvvedimento2);

            for (StoricoProvvedimento sto : storicoProvvedimento) {
                if (sto.getPrevStato() != null
                        && (sto.getPrevStato().equals(StatoProvvedimento.MINUTA_DA_MODIFICARE)
                                || sto.getPrevStato().equals(StatoProvvedimento.MINUTA_MODIFICATA_PRESIDENTE))
                        && !statiMap.containsKey("ULTIMA_MINUTA_MODIFICA")) {
                    statiMap.put("ULTIMA_MINUTA_MODIFICA", sto.getOggi());
                }
                checkAndAddToMap(statiMap, sto, StatoProvvedimento.MINUTA_DA_MODIFICARE, "ULTIMA_MODIFICA");
                checkAndAddToMap(statiMap, sto, StatoProvvedimento.INVIATO_IN_CANCEL_PRESIDENTE, "DEPOSITO_PROVV");
                checkAndAddToMap(statiMap, sto, StatoProvvedimento.PUBBLICATA, "PUBBLICATA");

                // Gestione della data di deposito provvedimento in caso di utente Estensore/Presidente
                if (tipoProvv.equalsIgnoreCase("SENTENZA") || tipoProvv.equalsIgnoreCase("ORDINANZA")) {
                    checkAndAddToMap(statiMap, sto, StatoProvvedimento.INVIATO_IN_CANCELLERIA_RELATORE, "DEPOSITO_PROVV");
                }

            }
            try {
                checkAndAddToMapDataMinutaAndDataPubblicazione(statiMap, entityManager, idSentenza);
            } catch (Exception ex) {
                LOGGER.error(
                        "Errore durante il recupero delle informazioni del fascicolo. idProvv:" + idProvv + ", idSentenza:" + idSentenza);
                throw new MonitoraggioException("Errore durante il recupero delle informazioni del fascicolo",
                        RestErrorCodeEnum.SEARCH_FASCICOLO_ERROR, ex);
            }

            for (StoricoProvvedimento sto : storicoProvvedimento2) {
                if (sto.getStato().equals(StatoProvvedimento.INVIATO_IN_CANCELLERIA_RELATORE)
                        && !(tipoProvv.equalsIgnoreCase("SENTENZA") || tipoProvv.equalsIgnoreCase("ORDINANZA"))) {
                    Date dataMinutaSic = statiMap.get("DATA_MINUTA");
                    Date dataMinutaCsp = sto.getOggi();
                    dataMinutaCsp.setHours(0);
                    dataMinutaCsp.setMinutes(0);
                    dataMinutaCsp.setSeconds(0);
                    if ((dataMinutaSic == null && dataMinutaCsp != null)
                            || (dataMinutaSic != null && dataMinutaCsp != null && !dataMinutaSic.equals(dataMinutaCsp))) {
                        statiMap.put("DATA_MINUTA_CSP", dataMinutaCsp);
                    }
                }
                if (sto.getStato().equals(StatoProvvedimento.PUBBLICATA)
                        && statiMap.containsKey(StatoProvvedimento.PUBBLICATO_SIC.name())) {
                    Date dataPubblicazioneSIC = statiMap.get(StatoProvvedimento.PUBBLICATO_SIC.name());
                    Date dataPubblicazione = sto.getOggi();
                    dataPubblicazione.setHours(0);
                    dataPubblicazione.setMinutes(0);
                    dataPubblicazione.setSeconds(0);
                    if ((dataPubblicazione == null && dataPubblicazioneSIC != null) || (dataPubblicazione != null
                            && dataPubblicazioneSIC != null && !dataPubblicazione.equals(dataPubblicazioneSIC))) {
                        statiMap.put(StatoProvvedimento.PUBBLICATO_SIC.name(), dataPubblicazioneSIC);
                    } else {
                        statiMap.put(StatoProvvedimento.PUBBLICATO_SIC.name(), null);
                    }
                }
                checkAndAddToMap(statiMap, sto, StatoProvvedimento.MINUTA_ACCETTATA, "MESSA_DISPOSIZIONE_PRE");
            }
            LOGGER.info("Recupero date completato");
            return statiMap;
        }
        return null;
    }

    private static String getStatoRicorso(EntityManager entityManager, String idSentenza) {
        String queryStr = "SELECT get_stato_ricorso(NULL, ps.ID_SENT , NULL) stato_ricorso "
                + "FROM PENALE_T_SENTENZA ps WHERE ps.ID_SENT = :idSentenza";
        Query query = entityManager.createNativeQuery(queryStr);
        query.setParameter("idSentenza", idSentenza);
        return (String) (query.getSingleResult());
    }

    private static void checkAndAddToMapDataMinutaAndDataPubblicazione(Map<String, Date> statiMap, EntityManager entityManager,
            String idSentenza) {
        if (!statiMap.containsKey("DATA_MINUTA")) {
            SentenzaDettaglio sentenzaDettaglio = getSentenzaDettaglio(entityManager, idSentenza);

            if (sentenzaDettaglio != null && sentenzaDettaglio.getDataMinuta() != null) {
                statiMap.put("DATA_MINUTA", sentenzaDettaglio.getDataMinuta());
                if (sentenzaDettaglio.getDepositoTelamatico() == null || sentenzaDettaglio.getDepositoTelamatico() == 0) {
                    statiMap.put(StatoProvvedimento.MINUTA_DEPOSITATA_SIC.name(), sentenzaDettaglio.getDataMinuta());
                }
                if (sentenzaDettaglio.getPubblicazioneTelematico() == null || sentenzaDettaglio.getPubblicazioneTelematico() == 0) {
                    statiMap.put(StatoProvvedimento.PUBBLICATO_SIC.name(),
                            sentenzaDettaglio.getnRaccg() != null ? sentenzaDettaglio.getDataPubbl() : null);
                    if (!statiMap.containsKey("DEPOSITO_PROVV") && sentenzaDettaglio.getDataPubbl() != null) {
                        statiMap.put(StatoProvvedimento.PROVV_DEPOSITATO_SIC.name(), sentenzaDettaglio.getDataPubbl());
                    }
                }
            } else {
                statiMap.put("DATA_MINUTA", null);
                statiMap.put(StatoProvvedimento.PUBBLICATO_SIC.name(), null);
            }
        }
    }

    public static SentenzaDettaglio getSentenzaDettaglio(EntityManager entityManager, String idSentenza) {
        Query query = entityManager.createNativeQuery("SELECT PTS.* FROM MIGRA.PENALE_T_SENTENZA PTS WHERE ID_SENT = :idSentenza",
                SentenzaDettaglio.class);

        query.setParameter("idSentenza", idSentenza);
        List<SentenzaDettaglio> resultList = query.getResultList();
        return resultList != null && !resultList.isEmpty() ? resultList.get(0) : null;
    }

    private static void checkAndAddToMap(Map<String, Date> statiMap, StoricoProvvedimento sto, StatoProvvedimento checkStato, String key) {
        if (sto.getStato().equals(checkStato) && !statiMap.containsKey(key)) {
            statiMap.put(key, sto.getOggi());
        }
    }

    public static Map<String, String> getPresidenti(String siglaSezione, String dataUdienzaDa, String dataUdienzaA, String tipoUdienza,
            String collegio, String dataMinutaDa, String dataMinutaA, String dataPubblicazioneDa, String dataPubblicazioneA,
            String numeroRicorsoDa, String numeroRicorsoA, String annoRicorsoDa, String annoRicorsoA, EntityManager entityManager)
            throws MonitoraggioException {

        String queryString = " SELECT DISTINCT PA.CODICE_FISCALE AS CF, PA.COGNOME || ' ' || PA.NOME AS NOME"
                + " FROM PENALE_ANAGMAGIS PA LEFT JOIN PENALE_T_MAGIS PTM ON PTM.ID_ANAGMAGIS = PA.ID_ANAGMAGIS"
                + " LEFT JOIN PENALE_COLLEGIO PC ON PC.ID_MAGIS = PTM.ID_MAGIS"
                + " LEFT JOIN PENALE_T_UDIENZA PTU ON PTU.ID_UDIEN = PC.ID_UDIEN";

        if (numeroRicorsoDa != null && !numeroRicorsoDa.equalsIgnoreCase("") && annoRicorsoDa != null
                && !annoRicorsoDa.equalsIgnoreCase("")) {
            queryString = queryString + " LEFT JOIN PENALE_T_RICUDIEN RU ON RU.ID_UDIEN = PTU.ID_UDIEN"
                    + " LEFT JOIN PENALE_T_RICORSO R ON R.NRG = RU.NRG AND LASTUDIE = 1";
        }
        if (((dataMinutaDa != null && !dataMinutaDa.equalsIgnoreCase(""))
                || (dataPubblicazioneDa != null && !dataPubblicazioneDa.equalsIgnoreCase("")))
                && ((numeroRicorsoDa == null || numeroRicorsoDa.equalsIgnoreCase(""))
                        && (annoRicorsoDa == null || annoRicorsoDa.equalsIgnoreCase("")))) {
            queryString = queryString + " LEFT JOIN PENALE_T_RICUDIEN RU ON RU.ID_UDIEN = PTU.ID_UDIEN"
                    + " LEFT JOIN PENALE_T_RICORSO R ON R.NRG = RU.NRG AND LASTUDIE = 1"
                    + " LEFT JOIN PENALE_PROVVEDIMENTI P ON RU.NRG = P.NRG AND LAST_MODIFIED = 1"
                    + " LEFT JOIN PENALE_T_ESITO PE ON RU.ID_RICUDIEN = PE.ID_RICUDIEN"
                    + " LEFT JOIN PENALE_T_ESITOSENT PES ON PES.ID_ESITO = PE.ID_ESITO"
                    + " LEFT JOIN PENALE_T_SENTENZA PS ON PS.ID_SENT = PES.ID_SENT";
        }

        if (((dataMinutaDa != null && !dataMinutaDa.equalsIgnoreCase(""))
                || (dataPubblicazioneDa != null && !dataPubblicazioneDa.equalsIgnoreCase("")))
                && ((numeroRicorsoDa != null && !numeroRicorsoDa.equalsIgnoreCase("") && annoRicorsoDa != null
                        && !annoRicorsoDa.equalsIgnoreCase("")))) {
            queryString = queryString + " LEFT JOIN PENALE_PROVVEDIMENTI P ON RU.NRG = P.NRG AND LAST_MODIFIED = 1"
                    + " LEFT JOIN PENALE_T_ESITO PE ON RU.ID_RICUDIEN = PE.ID_RICUDIEN"
                    + " LEFT JOIN PENALE_T_ESITOSENT PES ON PES.ID_ESITO = PE.ID_ESITO"
                    + " LEFT JOIN PENALE_T_SENTENZA PS ON PS.ID_SENT = PES.ID_SENT";
        }

        queryString = queryString + " WHERE PC.TIPOMAG = 'PRE'"
                + " AND PTU.ID_SEZIONE = (SELECT ID_PARAM FROM PENALE_PARAM PP WHERE PP.SIGLA = '" + siglaSezione
                + "' AND PP.TIPOTAB = 'SEZIONI') AND PA.CODICE_FISCALE IS NOT NULL AND PA.COGNOME IS NOT NULL";

        if (tipoUdienza != null && !tipoUdienza.equalsIgnoreCase(""))
            queryString = queryString + " AND PTU.ID_TIPOUD = (SELECT ID_PARAM FROM PENALE_PARAM PP WHERE PP.SIGLA = '" + tipoUdienza
                    + "' AND PP.TIPOTAB = 'TIPOUDIENZA')";

        if (collegio != null && !collegio.equalsIgnoreCase(""))
            queryString = queryString + " AND PTU.ID_AULA = (SELECT ID_PARAM FROM PENALE_PARAM PP WHERE PP.SIGLA = '" + collegio
                    + "' AND PP.TIPOTAB = 'AULA')";

        if (dataUdienzaDa != null && !dataUdienzaDa.equalsIgnoreCase(""))
            queryString = queryString + " AND PTU.DATAUD >= TO_DATE('" + dataUdienzaDa + "', 'dd/MM/yyyy')";

        if (dataUdienzaA != null && !dataUdienzaA.equalsIgnoreCase(""))
            queryString = queryString + " AND PTU.DATAUD <= TO_DATE('" + dataUdienzaA + "', 'dd/MM/yyyy')";

        if (dataMinutaDa != null && !dataMinutaDa.equalsIgnoreCase(""))
            queryString = queryString + " AND PS.DATAMINUTA >= TO_DATE('" + dataMinutaDa + "', 'dd/MM/yyyy')";

        if (dataMinutaA != null && !dataMinutaA.equalsIgnoreCase(""))
            queryString = queryString + " AND PS.DATAMINUTA <= TO_DATE('" + dataMinutaA + "', 'dd/MM/yyyy')";

        if (dataPubblicazioneDa != null && !dataPubblicazioneDa.equalsIgnoreCase(""))
            queryString = queryString + " AND PS.DATAPUBBL >= TO_DATE('" + dataPubblicazioneDa + "', 'dd/MM/yyyy')";

        if (dataPubblicazioneA != null && !dataPubblicazioneA.equalsIgnoreCase(""))
            queryString = queryString + " AND PS.DATAPUBBL <= TO_DATE('" + dataPubblicazioneA + "', 'dd/MM/yyyy')";

        if ((numeroRicorsoDa != null && !numeroRicorsoDa.equalsIgnoreCase("") && annoRicorsoDa != null
                && !annoRicorsoDa.equalsIgnoreCase(""))
                && (numeroRicorsoA == null || numeroRicorsoA.equalsIgnoreCase("") && annoRicorsoA == null
                        || annoRicorsoA.equalsIgnoreCase(""))) {
            Integer lunghezzaRicorso = numeroRicorsoDa.length();
            Integer differenza = 6 - lunghezzaRicorso;
            /**
             * Il numero ricorso viene estrapolato dal campo db NRGREALE composto da 4 cifre per l'anno 6 cifre per l'nrg più altre 3
             * finali. Con l'algoritmo sottostante aggiungiamo l'anno e gli zeri iniziali per ottenere le 6 cifre presenti in tabella. Viene
             * utilizzato il LIKE perchè non sappiamo quali siano le cifre finali che compongono l'NRGREALE
             */
            if (differenza > 0) {
                for (int i = 0; i < differenza; i++) {
                    numeroRicorsoDa = "0" + numeroRicorsoDa;
                }
            }
            queryString = queryString + " AND R.NRGREALE LIKE '" + annoRicorsoDa + numeroRicorsoDa + "%'";
        }

        if ((numeroRicorsoDa != null && !numeroRicorsoDa.equalsIgnoreCase("") && annoRicorsoDa != null
                && !annoRicorsoDa.equalsIgnoreCase(""))
                && (numeroRicorsoA != null && !numeroRicorsoA.equalsIgnoreCase("") && annoRicorsoA != null
                        || !annoRicorsoA.equalsIgnoreCase(""))) {
            Integer differenzaTraRicorsi = Integer.parseInt(numeroRicorsoA) - Integer.parseInt(numeroRicorsoDa);
            Integer lunghezzaRicorso = numeroRicorsoDa.length();
            Integer differenza = 6 - lunghezzaRicorso;
            /**
             * Se l'utente inserisce un range di numeri ricorsi l'algoritmo sottostante inserisce la condizione con il like per ogni singolo
             * ricorso partendo dal numero ricorso più alto e sottraendolo di un valore lo stesso ad ogni querystring inserita per ottenere
             * tutti i ricorsi all'interno dell'intervallo.
             */
            if (differenza > 0) {
                for (int i = 0; i < differenza; i++) {
                    numeroRicorsoDa = "0" + numeroRicorsoDa;
                }
            }

            if (differenzaTraRicorsi > 0) {
                queryString = queryString + " AND (R.NRGREALE LIKE '" + annoRicorsoDa + numeroRicorsoDa + "%'";

                int i = Integer.parseInt(numeroRicorsoA);
                while (i != Integer.parseInt(numeroRicorsoDa)) {
                    String numeroRicorsoAtemp = String.valueOf(i);
                    Integer lunghezzaRicorsoA = numeroRicorsoAtemp.length();
                    Integer differenzaA = 6 - lunghezzaRicorsoA;
                    /**
                     * Viene sempre effettuata la differenza perchè in un range la lunghezza delle cifre che compongono il numero di ricorso
                     * può variare. Es. 50 -> 110 - 99 (2 cifre) / 100 (3 cifre)
                     */
                    if (differenza > 0) {
                        for (int x = 0; x < differenzaA; x++) {
                            numeroRicorsoAtemp = "0" + numeroRicorsoAtemp;
                        }
                    }
                    queryString = queryString + " OR R.NRGREALE LIKE '" + annoRicorsoA + numeroRicorsoAtemp + "%'";
                    i--;
                }
                queryString = queryString + ")";
            } else {
                queryString = queryString + " AND R.NRGREALE LIKE '" + annoRicorsoDa + numeroRicorsoDa + "%'";
            }
        }
        try {
            Query query = entityManager.createNativeQuery(queryString);

            // query.setParameter("siglaSezione", siglaSezione);
            List<Object> resultList = query.getResultList();
            Iterator itr = resultList.iterator();
            Map<String, String> presidentiList = new HashMap();
            while (itr.hasNext()) {
                Object[] obj = (Object[]) itr.next();
                String cf = String.valueOf(obj[0]);
                String nome = String.valueOf(String.valueOf(obj[1]));
                presidentiList.put(cf, nome);
            }
            return presidentiList;
        } catch (Exception e) {
            LOGGER.error("Errore durante la ricerca dei presidenti. Sezione:" + siglaSezione);
            throw new MonitoraggioException("Errore durante la ricerca dei presidenti", RestErrorCodeEnum.MONITORAGGIO_SEARCH_ERROR, e);
        }
    }

    public static Map<String, String> getEstensori(String siglaSezione, String dataUdienzaDa, String dataUdienzaA, String tipoUdienza,
            String collegio, String dataMinutaDa, String dataMinutaA, String dataPubblicazioneDa, String dataPubblicazioneA,
            String numeroRicorsoDa, String numeroRicorsoA, String annoRicorsoDa, String annoRicorsoA, EntityManager entityManager)
            throws MonitoraggioException {

        String queryString = " SELECT DISTINCT PA.CODICE_FISCALE AS CF, PA.COGNOME || ' ' || PA.NOME AS NOME"
                + " FROM PENALE_ANAGMAGIS PA LEFT JOIN PENALE_T_MAGIS PTM ON PTM.ID_ANAGMAGIS = PA.ID_ANAGMAGIS"
                + " LEFT JOIN PENALE_COLLEGIO PC ON PC.ID_MAGIS = PTM.ID_MAGIS"
                + " LEFT JOIN PENALE_T_UDIENZA PTU ON PTU.ID_UDIEN = PC.ID_UDIEN";

        if (numeroRicorsoDa != null && !numeroRicorsoDa.equalsIgnoreCase("") && annoRicorsoDa != null
                && !annoRicorsoDa.equalsIgnoreCase("")) {
            queryString = queryString + " LEFT JOIN PENALE_T_RICUDIEN RU ON RU.ID_UDIEN = PTU.ID_UDIEN"
                    + " LEFT JOIN PENALE_T_RICORSO R ON R.NRG = RU.NRG AND LASTUDIE = 1";
        }

        if (((dataMinutaDa != null && !dataMinutaDa.equalsIgnoreCase(""))
                || (dataPubblicazioneDa != null && !dataPubblicazioneDa.equalsIgnoreCase("")))
                && ((numeroRicorsoDa == null || numeroRicorsoDa.equalsIgnoreCase(""))
                        && (annoRicorsoDa == null || annoRicorsoDa.equalsIgnoreCase("")))) {
            queryString = queryString + " LEFT JOIN PENALE_T_RICUDIEN RU ON RU.ID_UDIEN = PTU.ID_UDIEN"
                    + " LEFT JOIN PENALE_T_RICORSO R ON R.NRG = RU.NRG AND LASTUDIE = 1"
                    + " LEFT JOIN PENALE_PROVVEDIMENTI P ON RU.NRG = P.NRG AND LAST_MODIFIED = 1"
                    + " LEFT JOIN PENALE_T_ESITO PE ON RU.ID_RICUDIEN = PE.ID_RICUDIEN"
                    + " LEFT JOIN PENALE_T_ESITOSENT PES ON PES.ID_ESITO = PE.ID_ESITO"
                    + " LEFT JOIN PENALE_T_SENTENZA PS ON PS.ID_SENT = PES.ID_SENT";
        }

        if (((dataMinutaDa != null && !dataMinutaDa.equalsIgnoreCase(""))
                || (dataPubblicazioneDa != null && !dataPubblicazioneDa.equalsIgnoreCase("")))
                && ((numeroRicorsoDa != null && !numeroRicorsoDa.equalsIgnoreCase("") && annoRicorsoDa != null
                        && !annoRicorsoDa.equalsIgnoreCase("")))) {
            queryString = queryString + " LEFT JOIN PENALE_PROVVEDIMENTI P ON RU.NRG = P.NRG AND LAST_MODIFIED = 1"
                    + " LEFT JOIN PENALE_T_ESITO PE ON RU.ID_RICUDIEN = PE.ID_RICUDIEN"
                    + " LEFT JOIN PENALE_T_ESITOSENT PES ON PES.ID_ESITO = PE.ID_ESITO"
                    + " LEFT JOIN PENALE_T_SENTENZA PS ON PS.ID_SENT = PES.ID_SENT";
        }

        queryString = queryString + " WHERE PC.TIPOMAG = 'CON'"
                + " AND PTU.ID_SEZIONE = (SELECT ID_PARAM FROM PENALE_PARAM PP WHERE PP.SIGLA = '" + siglaSezione
                + "' AND PP.TIPOTAB = 'SEZIONI') AND PA.CODICE_FISCALE IS NOT NULL AND PA.COGNOME IS NOT NULL";

        if (tipoUdienza != null && !tipoUdienza.equalsIgnoreCase(""))
            queryString = queryString + " AND PTU.ID_TIPOUD = (SELECT ID_PARAM FROM PENALE_PARAM PP WHERE PP.SIGLA = '" + tipoUdienza
                    + "' AND PP.TIPOTAB = 'TIPOUDIENZA')";

        if (collegio != null && !collegio.equalsIgnoreCase(""))
            queryString = queryString + " AND PTU.ID_AULA = (SELECT ID_PARAM FROM PENALE_PARAM PP WHERE PP.SIGLA = '" + collegio
                    + "' AND PP.TIPOTAB = 'AULA')";

        if (dataUdienzaDa != null && !dataUdienzaDa.equalsIgnoreCase(""))
            queryString = queryString + " AND PTU.DATAUD >= TO_DATE('" + dataUdienzaDa + "', 'dd/MM/yyyy')";

        if (dataUdienzaA != null && !dataUdienzaA.equalsIgnoreCase(""))
            queryString = queryString + " AND PTU.DATAUD <= TO_DATE('" + dataUdienzaA + "', 'dd/MM/yyyy')";

        if (dataMinutaDa != null && !dataMinutaDa.equalsIgnoreCase(""))
            queryString = queryString + " AND PS.DATAMINUTA >= TO_DATE('" + dataMinutaDa + "', 'dd/MM/yyyy')";

        if (dataMinutaA != null && !dataMinutaA.equalsIgnoreCase(""))
            queryString = queryString + " AND PS.DATAMINUTA <= TO_DATE('" + dataMinutaA + "', 'dd/MM/yyyy')";

        if (dataPubblicazioneDa != null && !dataPubblicazioneDa.equalsIgnoreCase(""))
            queryString = queryString + " AND PS.DATAPUBBL >= TO_DATE('" + dataPubblicazioneDa + "', 'dd/MM/yyyy')";

        if (dataPubblicazioneA != null && !dataPubblicazioneA.equalsIgnoreCase(""))
            queryString = queryString + " AND PS.DATAPUBBL <= TO_DATE('" + dataPubblicazioneA + "', 'dd/MM/yyyy')";

        if ((numeroRicorsoDa != null && !numeroRicorsoDa.equalsIgnoreCase("") && annoRicorsoDa != null
                && !annoRicorsoDa.equalsIgnoreCase(""))
                && (numeroRicorsoA == null || numeroRicorsoA.equalsIgnoreCase("") && annoRicorsoA == null
                        || annoRicorsoA.equalsIgnoreCase(""))) {
            /**
             * Il numero ricorso viene estrapolato dal campo db NRGREALE composto da 4 cifre per l'anno 6 cifre per l'nrg più altre 3
             * finali. Con l'algoritmo sottostante aggiungiamo l'anno e gli zeri iniziali per ottenere le 6 cifre presenti in tabella. Viene
             * utilizzato il LIKE perchè non sappiamo quali siano le cifre finali che compongono l'NRGREALE
             */
            Integer lunghezzaRicorso = numeroRicorsoDa.length();
            Integer differenza = 6 - lunghezzaRicorso;
            if (differenza > 0) {
                for (int i = 0; i < differenza; i++) {
                    numeroRicorsoDa = "0" + numeroRicorsoDa;
                }
            }
            queryString = queryString + " AND R.NRGREALE LIKE '" + annoRicorsoDa + numeroRicorsoDa + "%'";
        }

        if ((numeroRicorsoDa != null && !numeroRicorsoDa.equalsIgnoreCase("") && annoRicorsoDa != null
                && !annoRicorsoDa.equalsIgnoreCase(""))
                && (numeroRicorsoA != null && !numeroRicorsoA.equalsIgnoreCase("") && annoRicorsoA != null
                        || !annoRicorsoA.equalsIgnoreCase(""))) {
            Integer differenzaTraRicorsi = Integer.parseInt(numeroRicorsoA) - Integer.parseInt(numeroRicorsoDa);
            Integer lunghezzaRicorso = numeroRicorsoDa.length();
            Integer differenza = 6 - lunghezzaRicorso;
            if (differenza > 0) {
                for (int i = 0; i < differenza; i++) {
                    numeroRicorsoDa = "0" + numeroRicorsoDa;
                }
            }

            if (differenzaTraRicorsi > 0) {
                queryString = queryString + " AND (R.NRGREALE LIKE '" + annoRicorsoDa + numeroRicorsoDa + "%'";
                /**
                 * Se l'utente inserisce un range di numeri ricorsi l'algoritmo sottostante inserisce la condizione con il like per ogni
                 * singolo ricorso partendo dal numero ricorso più alto e sottraendolo di un valore lo stesso ad ogni querystring inserita
                 * per ottenere tutti i ricorsi all'interno dell'intervallo.
                 */
                int i = Integer.parseInt(numeroRicorsoA);
                while (i != Integer.parseInt(numeroRicorsoDa)) {
                    String numeroRicorsoAtemp = String.valueOf(i);
                    Integer lunghezzaRicorsoA = numeroRicorsoAtemp.length();
                    Integer differenzaA = 6 - lunghezzaRicorsoA;
                    /**
                     * Viene sempre effettuata la differenza perchè in un range la lunghezza delle cifre che compongono il numero di ricorso
                     * può variare. Es. 50 -> 110 - 99 (2 cifre) / 100 (3 cifre)
                     */
                    if (differenza > 0) {
                        for (int x = 0; x < differenzaA; x++) {
                            numeroRicorsoAtemp = "0" + numeroRicorsoAtemp;
                        }
                    }
                    queryString = queryString + " OR R.NRGREALE LIKE '" + annoRicorsoA + numeroRicorsoAtemp + "%'";
                    i--;
                }
                queryString = queryString + ")";
            } else {
                queryString = queryString + " AND R.NRGREALE LIKE '" + annoRicorsoDa + numeroRicorsoDa + "%'";
            }
        }
        try {
            Query query = entityManager.createNativeQuery(queryString);

            // query.setParameter("siglaSezione", siglaSezione);
            List<Object> resultList = query.getResultList();
            Iterator itr = resultList.iterator();
            Map<String, String> estensoriList = new HashMap();
            while (itr.hasNext()) {
                Object[] obj = (Object[]) itr.next();
                String cf = String.valueOf(obj[0]);
                String nome = String.valueOf(String.valueOf(obj[1]));
                estensoriList.put(cf, nome);
            }
            return estensoriList;
        } catch (Exception e) {
            LOGGER.error("Errore durante la ricerca degli estensori. Sezione:" + siglaSezione);
            throw new MonitoraggioException("Errore durante la ricerca degli estensori", RestErrorCodeEnum.MONITORAGGIO_SEARCH_ERROR, e);
        }
    }

    public static Map<String, String> getEstensoriByUdienza(String idUdienza, EntityManager entityManager) throws MonitoraggioException {
        Query query = entityManager.createNativeQuery(" SELECT DISTINCT PA.CODICE_FISCALE AS CF, PA.COGNOME || ' ' || PA.NOME AS NOME"
                + " FROM PENALE_ANAGMAGIS PA" + " LEFT JOIN PENALE_T_MAGIS PTM ON PTM.ID_ANAGMAGIS = PA.ID_ANAGMAGIS"
                + " LEFT JOIN PENALE_COLLEGIO PC ON PC.ID_MAGIS = PTM.ID_MAGIS"
                + " LEFT JOIN PENALE_T_UDIENZA PTU ON PTU.ID_UDIEN = PC.ID_UDIEN" + " WHERE PC.TIPOMAG = 'CON' AND PTU.ID_UDIEN ="
                + idUdienza + " AND PA.CODICE_FISCALE IS NOT NULL AND PA.COGNOME IS NOT NULL");
        // query.setParameter("idUdienza", idUdienza);
        try {
            List<Object> resultList = query.getResultList();
            Iterator itr = resultList.iterator();
            Map<String, String> estensoriList = new HashMap();
            while (itr.hasNext()) {
                Object[] obj = (Object[]) itr.next();
                String cf = String.valueOf(obj[0]);
                String nome = String.valueOf(String.valueOf(obj[1]));
                estensoriList.put(cf, nome);
            }
            return estensoriList;
        } catch (Exception e) {
            LOGGER.error("Errore durante la ricerca degli estensori. idUdienza:" + idUdienza);
            throw new MonitoraggioException("Errore durante la ricerca degli estensori", RestErrorCodeEnum.MONITORAGGIO_SEARCH_ERROR, e);
        }
    }

    public String getIdProvvByIdUdienAndNrg(EntityManager entityManager, Integer nrg, Integer idUdien) {
        Query query = entityManager.createNativeQuery(
                "SELECT * FROM PENALE_PROVVEDIMENTI pp WHERE NRG = :nrg AND ID_UDIEN = :IDUDIEN ORDER BY DATA_ULTIMA_MODIFICA DESC ",
                ProvvedimentoPenale.class);
        query.setParameter("nrg", nrg);
        query.setParameter("IDUDIEN", idUdien);
        List<ProvvedimentoPenale> result = query.getResultList();
        if (result != null && result.size() > 0) {
            return result.get(0).getIdProvv();
        }
        return null;
    }

    public String getIdProvvByIdCat(EntityManager entityManager, Integer idCat) {
        Query query = entityManager.createNativeQuery(
                "SELECT * FROM PENALE_PROVVEDIMENTI pp WHERE FK_IDCAT = :IDCAT ORDER BY DATA_ULTIMA_MODIFICA DESC ",
                ProvvedimentoPenale.class);
        query.setParameter("IDCAT", idCat);
        List<ProvvedimentoPenale> result = query.getResultList();
        if (result != null && result.size() > 0) {
            return result.get(0).getIdProvv();
        }
        return null;
    }

    public String getTipoProvvByIdProvv(EntityManager entityManager, String idProvv) {
        String queryStr = "SELECT TIPO FROM PENALE_PROVVEDIMENTI pp WHERE ID_PROVV = :IDPROVV";
        Query query = entityManager.createNativeQuery(queryStr);
        query.setParameter("IDPROVV", idProvv);
        return (String) (query.getSingleResult());
    }

    public static List<EstensoriSentenzaView> getEstensoriBySentenza(String idSentenza, EntityManager entityManager)
            throws MonitoraggioException {
        Query query1 = entityManager.createNativeQuery("SELECT PES.* FROM PENALE_ESTENSORI_SENTENZA PES WHERE ID_SENT = " + idSentenza,
                EstensoriSentenzaView.class);
        List<EstensoriSentenzaView> estensoriList = query1.getResultList();
        if (estensoriList.isEmpty()) {
            Query query2 = entityManager.createNativeQuery("SELECT DISTINCT"
                    + " (SELECT PA.CODICE_FISCALE FROM PENALE_ANAGMAGIS PA WHERE ID_ANAGMAGIS = (SELECT PTM.ID_ANAGMAGIS FROM MIGRA.PENALE_T_MAGIS PTM WHERE ID_MAGIS = PS.ID_ESTENSORE)) AS CODICE_FISCALE,"
                    + " (SELECT PA.COGNOME || ' ' || PA.NOME AS NOME FROM PENALE_ANAGMAGIS PA WHERE ID_ANAGMAGIS = (SELECT PTM.ID_ANAGMAGIS FROM MIGRA.PENALE_T_MAGIS PTM WHERE ID_MAGIS = PS.ID_ESTENSORE)) AS NOME,"
                    + " PS.ID_ESTENSORE" + " FROM PENALE_T_SENTENZA PS" + " WHERE PS.ID_SENT =" + idSentenza);
            // query.setParameter("idSentenza", idSentenza);
            try {
                List<Object> resultList = query2.getResultList();
                Iterator itr = resultList.iterator();
                while (itr.hasNext()) {
                    Object[] obj = (Object[]) itr.next();
                    String cf = obj[0] != null ? String.valueOf(obj[0]) : null;
                    String nome = obj[1] != null ? String.valueOf(String.valueOf(obj[1])) : null;
                    String idEstensore = obj[2] != null ? String.valueOf(String.valueOf(obj[2])) : null;
                    if (cf != null && !cf.equalsIgnoreCase("") && nome != null && !nome.equalsIgnoreCase("")) {
                        EstensoriSentenzaView estensore = new EstensoriSentenzaView();
                        estensore.setIdSentenza(Long.parseLong(idSentenza));
                        estensore.setIdEstensore(Long.parseLong(idEstensore));
                        estensore.setNome(nome);
                        estensore.setCodiceFiscale(cf);
                        estensoriList.add(estensore);
                    }
                }
            } catch (Exception e) {
                LOGGER.error("Errore durante la ricerca degli estensori. idSentenza:" + idSentenza);
                throw new MonitoraggioException("Errore durante la ricerca degli estensori", RestErrorCodeEnum.MONITORAGGIO_SEARCH_ERROR,
                        e);
            }
        }
        return estensoriList;
    }
}
