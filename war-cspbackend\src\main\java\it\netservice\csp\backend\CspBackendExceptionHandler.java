package it.netservice.csp.backend;

import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import javax.ws.rs.ext.Provider;

import org.apache.log4j.Logger;

import it.netservice.csp.backend.dto.RestError;
import it.netservice.csp.backend.excpetion.CspBackendException;

@Provider
public class CspBackendExceptionHandler implements ExceptionMapper<CspBackendException> {
    private static final Logger LOGGER = Logger.getLogger(CspBackendExceptionHandler.class);

    @Override
    public Response toResponse(CspBackendException ex) {
        RestError errorDto = new RestError(ex.getMessage(), ex.getErrorCode(), ex.getReason(), ex.getErrorType());
        errorDto.setStatus(ex.getStatus() != null ? ex.getStatus() : 500);
        // You can place whatever logic you need here
        LOGGER.error(errorDto, ex);
        return Response.status(errorDto.getStatus()).entity(errorDto).header("Access-Control-Allow-Origin", "*")
                .header("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS")
                .header("Access-Control-Expose-Headers", "Content-Type, Content-Disposition").header("Access-Control-Allow-Headers",
                        "Origin, X-Requested-With, Content-Type, Content-Disposition, Accept, Authorization, x-auth-token")
                .build();
    }
}
