/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package it.netservice.csp.backend.pubblicazioneDepositi;

import java.io.*;
import java.math.BigDecimal;
import java.util.*;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.persistence.*;

import org.apache.log4j.Logger;

import it.eng.csc.documentale.controller.GiustiziaController;
import it.eng.csc.documentale.model.wrapper.Autenticazione;
import it.eng.csc.documentale.model.wrapper.ResponseInserimentoDocumento;
import it.netservice.common.rest.PageInfo;
import it.netservice.common.rest.backend.CriteriaBuilderHelper;
import it.netservice.csp.backend.accettazioneDepositi.AccettazioneUtilService;
import it.netservice.csp.backend.accettazioneDepositi.FileSignedResult;
import it.netservice.csp.backend.dto.ErrorTypeEnum;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.elaborazioneDepositi.service.*;
import it.netservice.csp.backend.excpetion.*;
import it.netservice.csp.backend.ext.service.ServiceBuilder;
import it.netservice.csp.backend.sic.service.depositi.PubblicazioneUtil;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.csp.backend.ws.accettazione.ServiziAccettazione;
import it.netservice.csp.backend.ws.atti.IdAtto;
import it.netservice.csp.backend.ws.atti.IdFascicolo;
import it.netservice.csp.backend.ws.atti.ServiziAttoInformatico;
import it.netservice.penale.model.common.dettaglioricorso.ProvvedimentoPenale;
import it.netservice.penale.model.common.dettaglioricorso.Timbro;
import it.netservice.penale.model.common.dettaglioricorso.Udienza;
import it.netservice.penale.model.common.dettaglioricorso.UdienzaRicorso;
import it.netservice.penale.model.csp.Atto;
import it.netservice.penale.model.csp.ElaborazioneProvvedimento;
import it.netservice.penale.model.enums.StatoProvvedimento;
import it.netservice.penale.model.enums.TipoOrigine;
import it.netservice.penale.model.enums.TipoOscuramentoPubblica;
import it.netservice.penale.model.enums.TipoProvvedimentoMagistrato;
import it.netservice.penale.model.sic.Deposito;
import it.netservice.penale.model.sic.RicorsoSic;
import it.netservice.penale.model.sic.Sentenza;
import it.netservice.penale.model.sic.Utente;
import it.netservice.penale.model.sic.criteria.csp.DepositiViewCriteriaParameters;
import it.netservice.penale.model.sic.csp.DepositoView;

/**
 * <AUTHOR>
 */
@ApplicationScoped
public class PubblicazioneService {

    private static final Logger LOGGER = Logger.getLogger(PubblicazioneService.class);

    @PersistenceUnit(unitName = "sic-model-penale")
    protected EntityManagerFactory emfCaricamento;

    @Inject
    private UfficioCopieService ufficioCopieService;

    @Inject
    private ServiceBuilder serviceBuilder;

    @Inject
    private AccettazioneUtilService accettazioneUtilService;

    @Inject
    private ElaborazioneProvvedimentoService elaborazioneProvvedimentoService;

    public void pubblicaProvvedimento(ElaborazioneProvvedimento elabProv, Deposito deposito, String operatore, String numeroRicorso,
            String annoRicorso, FileSignedResult signedResult) throws Exception {

        LOGGER.info("Inizio pubblicazione deposito:" + deposito);

        EntityManager entityManagerCaricamento = emfCaricamento.createEntityManager();
        SicPenaleRepository repo = new SicPenaleRepository(entityManagerCaricamento);
        EntityTransaction transactionCaricamento = entityManagerCaricamento.getTransaction();
        transactionCaricamento.begin();

        try {
            Long nRaccg;
            Utente utenteOperatore = repo.findUtenteByUsername(operatore);
            RicorsoSic ricorso = repo.getRicorsoByElaborazioneDeposito(elabProv);
            BigDecimal idSezione = repo.getIdSezione(ricorso.getDoveSta());
            Calendar calendar = Calendar.getInstance();
            String anno = Integer.toString(calendar.get(Calendar.YEAR));
            Udienza udienza = getUdienzaLastDeposito(ricorso, entityManagerCaricamento, deposito.getIdCat());

            LOGGER.debug("Pubblicazione provvedimento. udienza:" + udienza + ", idCat:" + deposito.getIdCat());
            Sentenza sentenzaRicorso = null;
            if (elabProv.getTipoVerificato().equals(TipoProvvedimentoMagistrato.Ordinanza)
                    || elabProv.getTipoVerificato().equals(TipoProvvedimentoMagistrato.Sentenza)) {
                sentenzaRicorso = updateSentenza(elabProv, deposito, numeroRicorso, annoRicorso, repo, udienza, ricorso, utenteOperatore,
                        idSezione, anno, entityManagerCaricamento);
            } else {
                LOGGER.error("Impossibile accettare il deposito. Provvedimento non pubblicabile. udienza:" + udienza + ", idCat:"
                        + deposito.getIdCat());
                throw new CspCommonRestWarningException("Impossibile accettare il deposito. Provvedimento non pubblicabile.",
                        RestErrorCodeEnum.PROVV_NON_PUBB);
            }
            String idcatatto = null;
            try {
                idcatatto = salvataggioTimbri(elabProv, deposito, numeroRicorso, annoRicorso, signedResult, entityManagerCaricamento,
                        transactionCaricamento, udienza);
            } catch (Exception ex) {
                LOGGER.error(String.format(
                        "Errore durante il salvataggio dei timbri. idElabProvv:%s, idDeposito:%d, numeroRicorso:%s, annoRicorso:%s",
                        elabProv.getId(), deposito.getIdCat(), numeroRicorso, annoRicorso));
                throw new PubblicazioneException("Errore durante il salvataggio dei timbri.", RestErrorCodeEnum.TIMBRI_SAVE_ERROR, ex);
            }

            // aggiornamento stato deposito
            transactionCaricamento = entityManagerCaricamento.getTransaction();
            transactionCaricamento.begin();
            deposito.setStato(Deposito.Stato.ACCETTATO);
            try {
                entityManagerCaricamento.merge(deposito);
                Query query = entityManagerCaricamento.createNativeQuery(
                        "update PENALE_T_SENTENZA set PUBBLICATO_TELEMATICO = 1 where ID_SENT=" + sentenzaRicorso.getId());
                query.executeUpdate();
                transactionCaricamento.commit();
            } catch (Exception ex) {
                LOGGER.error("Errore durante aggiornamento stato deposito. idSent:" + sentenzaRicorso.getId());
                throw new PubblicazioneException("Errore durante aggiornamento stato deposito", RestErrorCodeEnum.UPDATE_DEPOSITO_ERROR,
                        ex);
            }

            LOGGER.info("Pubblicazione completata");
            accettazioneUtilService.aggiornaStatoDesk(repo, deposito, utenteOperatore.getIdUtente(), entityManagerCaricamento,
                    StatoProvvedimento.PUBBLICATA, StatoProvvedimento.INVIATO_IN_CANCEL_PRESIDENTE, null);
            LOGGER.info("Aggoiornato lo stato lato desk della pubblicazione");
        } catch (Throwable t) {
            if (transactionCaricamento.isActive()) {
                transactionCaricamento.rollback();
            }
            if (t instanceof CspBackendException) {
                throw (CspBackendException) t;
            }
            LOGGER.error(String.format(
                    "Errore durante la pubblicazione del provvedimento. idElabProvv:%s, idDeposito:%d, numeroRicorso:%s, annoRicorso:%s",
                    elabProv.getId(), deposito.getIdCat(), numeroRicorso, annoRicorso));
            throw new PubblicazioneException("Errore durante la pubblicazione del provvedimento",
                    RestErrorCodeEnum.PUBBLICAZIONE_GENERIC_ERROR, t);
        } finally {
            if (entityManagerCaricamento != null) {
                try {
                    entityManagerCaricamento.close();
                } catch (Exception ex) {
                    LOGGER.error("Errore chiusura entity manager", ex);
                }
            }
        }
    }

    public void inviaUCU(Long idDeposito, String operatore) throws CspBackendException {
        if (operatore == null) {
            throw new CspBackendException("Sessione scaduta o assente", RestErrorCodeEnum.SESSION_EXPIRED, 433);
        }

        EntityManager entityManagerMigra = emfCaricamento.createEntityManager();
        SicPenaleRepository repo = new SicPenaleRepository(entityManagerMigra);
        EntityTransaction transactionMigra = entityManagerMigra.getTransaction();
        transactionMigra.begin();

        try {
            // recupero dati necessari per l'invio verso UCU
            CriteriaBuilderHelper<DepositoView> criteriaBuilder = new CriteriaBuilderHelper<>(DepositoView.class);
            DepositiViewCriteriaParameters param = new DepositiViewCriteriaParameters();
            param.setIdCat(idDeposito);
            DepositoView deposito = criteriaBuilder.uniqueResult(entityManagerMigra, param);

            ElaborazioneProvvedimento elabProv = elaborazioneProvvedimentoService
                    .findElaborazioneProvvedimentoByIdDeposito(entityManagerMigra, idDeposito);

            RicorsoSic ricorso = repo.getRicorsoByElaborazioneDeposito(elabProv);

            Udienza udienza = getUdienzaLastDeposito(ricorso, entityManagerMigra, deposito.getIdCat());
            Sentenza sentenza = repo.getSetenzaByIdCatAndNrg(elabProv.getDeposito().getIdCat(), deposito.getNrg());
            if (sentenza == null) {
                throw new PubblicazioneException(
                        String.format("Sentenza non trovata. idUdienza:%d, nrg:%d", deposito.getIdUdienza(), deposito.getNrg()));
            }
            elabProv.setnRaccg(PubblicazioneUtil.formatNumero(sentenza.getnRaccg(), 10));
            elabProv.setIdProvvedimentoSic(sentenza.getId());

            Timbro timbro = findTimbroByNrgAndIdUienza(entityManagerMigra, deposito.getIdUdienza(), deposito.getNrg());
            if (timbro == null) {
                throw new PubblicazioneException(
                        String.format("Timbro non trovato. idUdienza:%d, nrg:%d", deposito.getIdUdienza(), deposito.getNrg()));
            }
            String[] splittedNRG = elabProv.getnRaccg().split("/");
            String sezioneUdienza = udienza.getSezione().getSigla();
            byte[] provvedimento = donwloadAtto(timbro.getIdcatatto());

            // inserimento nel PDOC
            String nomeFile = generateFilename(splittedNRG, sezioneUdienza, false);
            long idDocumentoPdoc = sendAttoToPDOC(provvedimento, nomeFile, elabProv);

            // se presente anche il file oscurato andrà inviato a PDOC
            String nomeFileOscurato = null;
            long idDocumentoPdocOscurato = -1L;
            if (PubblicazioneUtil.hasOscurato(elabProv.getOscuramentoDati()) && deposito.getOrigine().equals(TipoOrigine.SYSTEM)) {
                byte[] provvedimentoOscurato = donwloadAtto(timbro.getIdcatattooscurato());

                // inserimento nel PDOC
                nomeFileOscurato = generateFilename(splittedNRG, sezioneUdienza, true);
                idDocumentoPdocOscurato = sendAttoToPDOC(provvedimentoOscurato, nomeFileOscurato, elabProv);
            }

            ufficioCopieService.pubblicaUCU(elabProv, operatore, nomeFile, idDocumentoPdoc, nomeFileOscurato, idDocumentoPdocOscurato);
        } catch (Throwable ex) {
            if (transactionMigra.isActive()) {
                transactionMigra.rollback();
            }
            if (ex instanceof CspBackendException) {
                throw (CspBackendException) ex;
            }
            throw new PubblicazioneException(String.format("Errore durante l'invio verso ufficio copie. idDeposito:%d", idDeposito));
        } finally {
            if (entityManagerMigra != null) {
                try {
                    entityManagerMigra.close();
                } catch (Exception ex) {
                    LOGGER.error("Errore chiusura entity manager", ex);
                }
            }
        }
    }

    private Udienza getUdienzaLastDeposito(RicorsoSic ricorso, EntityManager entityManagerMigra, Long idDeposito) {
        Udienza udienza = null;
        if (ricorso.getUdienzaRicorso().size() == 1) {
            udienza = ricorso.getUdienzaRicorso().iterator().next().getUdienza();
        } else {
            for (UdienzaRicorso udienzaRicorso : ricorso.getUdienzaRicorso()) {
                if (udienzaRicorso.getUdienza() != null && udienzaRicorso.getUdienza().getData() != null
                        && isLastDeposito(entityManagerMigra, idDeposito, udienzaRicorso)) {
                    udienza = udienzaRicorso.getUdienza();
                }
            }
        }
        return udienza;
    }

    private byte[] donwloadAtto(String idCatAtto) {
        // recupero l'atto dal bea con coccardine e timbri
        ServiziAttoInformatico service = serviceBuilder.getAttoInformaticoService();
        IdAtto idAtto = new IdAtto();
        IdFascicolo fascicolo = new IdFascicolo();
        fascicolo.setRegistro(System.getProperty("gestorelocale.registro"));
        fascicolo.setUfficio(System.getProperty("gestorelocale.ufficio"));
        idAtto.setId(idCatAtto);
        idAtto.setFascicolo(fascicolo);
        LOGGER.info("Eseguo il download del nuovo atto con i timbri. idAtto:" + idAtto + ", registro:" + fascicolo.getRegistro());
        return service.download(idAtto, false);
    }

    private static String generateFilename(String[] splittedNRG, String sezioneUdienza, boolean oscurato) {
        return oscurato ? "P_" + splittedNRG[1] + "_" + splittedNRG[0] + "_" + sezioneUdienza + "_01_OSCURATO.pdf" : "P_" + splittedNRG[1]
                + "_" + splittedNRG[0] + "_" + sezioneUdienza + "_01.pdf";
    }

    private long sendAttoToPDOC(byte[] provvedimento, String nomeFile, ElaborazioneProvvedimento elabProv)
            throws IOException, PubblicazioneException {
        long idDocumentoPdoc = 0;
        String testString = System.getProperty("documentale.test");
        if (testString != null && "true".endsWith(testString.toLowerCase())) {
            idDocumentoPdoc = System.currentTimeMillis();
            File testFile = File.createTempFile("provvedimentoTest", ".pdf");
            try (OutputStream os = new FileOutputStream(testFile)) {
                os.write(provvedimento);
                LOGGER.info("Provvedimento di test:" + testFile.getCanonicalPath());
            }
            LOGGER.info("Salvo il file con i timbri in locale");
        } else {
            // Invio del documento al documentale PDOC
            LOGGER.info("Salvo il file con i timbri nel documentale PDOC");
            try {
                ByteArrayInputStream content = new ByteArrayInputStream(provvedimento);
                GiustiziaController controller = new GiustiziaController(getPubblicazioneProperties());
                Autenticazione auth = getAutenticazione();
                ResponseInserimentoDocumento responseInserimentoDocumento = controller.inserimentoDocumento(auth, content, "SENTENZAPDF",
                        "DOCUMENTOPDF", nomeFile, "", null);
                idDocumentoPdoc = responseInserimentoDocumento.getIdDocumentoInserito();
            } catch (Exception ex) {
                LOGGER.error("Errore durante salvataggio file con i timbri nel documetale PDOC. idElabProvv:" + elabProv.getId());
                throw new PubblicazioneException("Errore durante salvataggio file con i timbri nel documetale PDOC.",
                        RestErrorCodeEnum.TIMBRI_SAVE_ERROR, ex);
            }
        }
        return idDocumentoPdoc;
    }

    private Sentenza updateSentenza(ElaborazioneProvvedimento elabProv, Deposito deposito, String numeroRicorso, String annoRicorso,
            SicPenaleRepository repo, Udienza udienza, RicorsoSic ricorso, Utente utenteOperatore, BigDecimal idSezione, String anno,
            EntityManager entityManagerCaricamento) throws CspBackendException {
        LOGGER.info("Inizio aggiornamento sentenza. Deposito:" + deposito.getIdCat() + ", nrg:" + deposito.getNrg());

        Long nRaccg;
        Sentenza sentenzaRicorso;
        // logica specifica per sentenze e ordinanze
        if (elabProv.getOscuramentoDati() == null && elabProv.getTipoVerificato() == null && elabProv.getIdDispositivo() == null) {
            LOGGER.error(
                    "Impossibile accettare il deposito. Oscuramento dati / tipo verificato / dispositivo non valorizzati correttamente."
                            + " idElabProvv:" + elabProv.getId() + ", idDeposito:" + deposito.getIdCat() + ", numeroRicorso:"
                            + numeroRicorso + ", annoRicorso:" + annoRicorso);
            throw new GestioneSicException(
                    "Impossibile accettare il deposito. Oscuramento dati / tipo verificato / dispositivo non valorizzati correttamente.",
                    RestErrorCodeEnum.DATA_PROVV_NULL);
        }

        sentenzaRicorso = repo.getSetenzaByIdCatAndNrg(elabProv.getDeposito().getIdCat(), deposito.getNrg());

        if (sentenzaRicorso == null) {
            LOGGER.error("Impossibile accettare il deposito. Dati udienza non presenti." + " idElabProvv:" + elabProv.getId()
                    + ", idDeposito:" + deposito.getIdCat() + ", numeroRicorso:" + numeroRicorso + ", annoRicorso:" + annoRicorso);
            throw new GestioneSicException("Impossibile accettare il deposito. Dati udienza non presenti.",
                    RestErrorCodeEnum.DATI_UDIEN_NULL);
        }
        /* TODO */
        Long idProvvedimentoSic = sentenzaRicorso.getId();
        if (idProvvedimentoSic != null) {
            LOGGER.info("RECUPERATO DA SIC ID SENT MASTER:" + idProvvedimentoSic + " PER SENTENZA " + sentenzaRicorso.getId() + " UDIENZA:"
                    + udienza.getId());
        } else {
            idProvvedimentoSic = repo.getIdSentMaster().longValue(); /* TODO - correggere query */
            LOGGER.info("GENERATO SIC ID SENT MASTER:" + idProvvedimentoSic + " PER SENTENZA " + sentenzaRicorso.getId() + " UDIENZA:"
                    + udienza.getId());
        }

        nRaccg = sentenzaRicorso.getnRaccg();
        if (nRaccg != null) {
            LOGGER.info("RECUPERATO DA SIC NRAGGC:" + nRaccg + " PER SENTENZA " + sentenzaRicorso.getId() + " UDIENZA:"
                    + ricorso.getUdienzaRicorso());
        }

        if (nRaccg == null) {
            nRaccg = createNRaccGen(repo, utenteOperatore, ricorso, idSezione, anno, sentenzaRicorso);
            LOGGER.info("GENERATO NRAGGC:" + nRaccg + " PER SENTENZA:" + sentenzaRicorso.getId() + " UDIENZA:" + udienza.getId());
        }

        // effettuo la modifica o scrittura del dispositivo sentenza
        repo.upsertDispositivoUdienzaSic(sentenzaRicorso.getSentenza(), elabProv.getContributoUnificatoStabilita(),
                elabProv.getIdDispositivo(), idSezione.longValue(), PubblicazioneUtil.mapTipoSent(elabProv.getTipoVerificato()),
                PubblicazioneUtil.mapOscuramento(elabProv.getOscuramentoDati()), utenteOperatore);

        // effettuo la modifica della sentenza
        try {
            repo.updateSentenzaPubblica(sentenzaRicorso.getId(), idProvvedimentoSic, nRaccg, elabProv.getMotivazioneSemplificata(),
                    new Date(elabProv.getDeposito().getDataDeposito()), utenteOperatore);

            elabProv.setIdProvvedimentoSic(idProvvedimentoSic);
            elabProv.setnRaccg(PubblicazioneUtil.formatNumero(nRaccg, 10));

            elabProv.setNumeroSezionale(PubblicazioneUtil.formatNumero(sentenzaRicorso.getSentenza(), 9));

            elabProv.setSezioneUdienza(String.valueOf(udienza.getSezione()));

            entityManagerCaricamento.merge(elabProv);
            LOGGER.info("Fine aggiornamento Sentenza");
        } catch (Exception ex) {
            LOGGER.error("Errore durante aggiornamento della sentenza. idElabProvv:" + elabProv.getId() + ", idDeposito:"
                    + deposito.getIdCat() + ", numeroRicorso:" + numeroRicorso + ", annoRicorso:" + annoRicorso);
            throw new PubblicazioneException("Errore durante aggiornamento della sentenza.", RestErrorCodeEnum.UPDATE_SENTENZA_ERROR, ex);
        }
        // logica specifica per decreto estinzione
        return sentenzaRicorso;
    }

    /**
     * check se sono stati inseriti i timbri e se non sono stati inseriti gli inserisco TODO da capire se ritornare l'iddatto non oscurato o
     * quello uscurato da salvare nel ufficio copie
     *
     * @param elabProv
     * @param deposito
     * @param numeroRicorso
     * @param annoRicorso
     * @param signedResult
     * @param entityManagerCaricamento
     * @param transactionCaricamento
     * @param udienza
     * @return
     * @throws ElaborazioneException
     * @throws InterruptedException
     */
    private String salvataggioTimbri(ElaborazioneProvvedimento elabProv, Deposito deposito, String numeroRicorso, String annoRicorso,
            FileSignedResult signedResult, EntityManager entityManagerCaricamento, EntityTransaction transactionCaricamento,
            Udienza udienza) throws ElaborazioneException, PubblicazioneException {
        String idcatatto = null;
        String idCatOscurato = null;
        Timbro timbroByNrgAndIdUienza = findTimbroByNrgAndIdUienza(entityManagerCaricamento, udienza.getId(), deposito.getNrg());
        // se timbroByNrgAndIdUienza non è valorizzato significa che non sono stati inseriti i timbri
        if (timbroByNrgAndIdUienza == null) {
            // logica per prelevare l'idcat corretto che è o l'originale del desk o quello controfirmato dal cancelliere)

            if (signedResult == null || signedResult.getIdAttoControfirmato() == null
                    || signedResult.getIdAttoControfirmato().getId() == null) {
                LOGGER.info("Nessun atto firmato dal cancelliere trovato:" + signedResult);
                CriteriaBuilderHelper<Atto> criteriaBuilder = new CriteriaBuilderHelper<>(Atto.class);
                AttiCriteriaParameters param = new AttiCriteriaParameters();
                param.setIdContenitore(String.valueOf(elabProv.getDeposito().getIdCat()));
                List<Atto> atti = criteriaBuilder.resultList(entityManagerCaricamento, param);
                // check per il ritrasmetti pubblicazione,preleviamo gli atti dal db perche sn stati gia salvati nel primo giro
                for (Atto atto : atti) {
                    switch (atto.getTipoGl()) {
                        case "SM":
                            idCatOscurato = String.valueOf(atto.getIdCat());
                            break;
                        case "ATTO":
                            idcatatto = String.valueOf(atto.getIdCatBusta());
                            break;
                        default:
                            break;
                    }

                }
                if (idcatatto == null) {
                    // se non si hanno gli atti salvati sul db, allora solleviamo una eccezione
                    LOGGER.error("Nessun atto firmato trovato. idCat:" + elabProv.getDeposito().getIdCat());
                    throw new PubblicazioneException("Nessun atto firmato trovato", RestErrorCodeEnum.ATTO_NOT_FOUND);
                    // check per il ritrasmetti pubblicazione,preleviamo gli atti dal db perche sn stati gia salvati nel primo giro
                }
            } else {
                idcatatto = signedResult.getIdAttoControfirmato().getId();
                idCatOscurato = signedResult.getIdAttoControfirmatoOscurato() != null
                        && signedResult.getIdAttoControfirmatoOscurato().getId() != null ? signedResult.getIdAttoControfirmatoOscurato()
                                .getId() : null;
            }
            // inserimento dati nella tabella con le informazioni da mostrare nei timbri
            Timbro timbro = new Timbro();
            timbro.setNumric(String.format("%s/%s", numeroRicorso, annoRicorso));
            timbro.setTipooscuramento(
                    elabProv.getOscuramentoDati() != null ? elabProv.getOscuramentoDati().name() : TipoOscuramentoPubblica.Nessuno.name());
            timbro.setNumsezionale(elabProv.getNumeroSezionale());
            timbro.setNumraccgen(elabProv.getnRaccg());
            timbro.setDatapubblicazione(new Date());
            timbro.setNrg(deposito.getNrg());
            timbro.setIdcatatto(idcatatto);
            timbro.setIdcatattooscurato(idCatOscurato);
            timbro.setIdUdienza(udienza.getId());
            entityManagerCaricamento.persist(timbro);
            // committo per permettere al gl-cass di poter aggiungere subito i timbri

            LOGGER.debug("Eseguito la commit nella tabella timbri. idCatAtto:" + idcatatto + ", idCatOscurato:" + idCatOscurato);
        } else {
            LOGGER.info("I timbri sono stati già inseriti");
            // mi prendo l'idcat dai timbri
            idcatatto = timbroByNrgAndIdUienza.getIdcatatto();
        }
        transactionCaricamento.commit();
        LOGGER.info("Salvataggio dei timbri completato");
        return idcatatto;
    }

    public Timbro findTimbroByNrgAndIdUienza(EntityManager entityManager, Long idUdienza, Long nrg) throws ElaborazioneException {
        // LOGGING NRG/ID_UDIEN - Inizio findTimbroByNrgAndIdUienza
        LOGGER.info(String.format("[LOGGING NRG/ID_UDIEN] findTimbroByNrgAndIdUienza - START - Received nrg: %s, idUdienza: %s", nrg,
                idUdienza));
        if (nrg == null || idUdienza == null) {
            LOGGER.error(
                    String.format("[LOGGING NRG/ID_UDIEN] findTimbroByNrgAndIdUienza - ERRORE DIRETTO: nrg (%s) o idUdienza (%s) è NULL.",
                            nrg, idUdienza));
            throw new ElaborazioneException("nrg o idUdienza null");
        }
        CriteriaBuilderHelper<Timbro> criteriaBuilder = new CriteriaBuilderHelper<>(Timbro.class);
        List<PageInfo.Order> ordersList = new ArrayList<>();
        PageInfo.Order o = new PageInfo.Order();
        o.setPropertyName("idUdienza");
        o.setAsc(false);
        ordersList.add(o);
        return criteriaBuilder.firstResult(entityManager, ordersList, new TimbroSearchParams(idUdienza, nrg));
    }

    private boolean isLastDeposito(EntityManager entityManagerCaricamento, Long idCat, UdienzaRicorso udienzaRicorso) {
        ProvvedimentoPenale provv = this.getIdProvvByIdCat(entityManagerCaricamento, idCat);
        return provv != null && provv.getIdUdienza() != null && provv.getIdUdienza().equals(udienzaRicorso.getUdienza().getId())
                && provv.getNrg().equals(udienzaRicorso.getRicorso().getNrg());
    }

    //
    // private List<it.netservice.sic.model.common.dettaglioricorso.Sentenza> checkFascicoliRiuniti(SicPenaleRepository repo, Long sentenza,
    // Long idSezione, Long tipoSent) {
    // return repo.findFascicoliRiuniti(sentenza, idSezione, tipoSent);
    // }
    //
    private Long createNRaccGen(SicPenaleRepository repo, Utente utenteOperatore, RicorsoSic ricorso, BigDecimal idSezione, String anno,
            Sentenza sentenzaRicorso) {
        Long nRaccg = repo.getnRaccg(anno).longValue();
        String nRaccgStr = nRaccg.toString();
        String nRaccGenAnno = nRaccgStr.substring(0, 4);
        String nRaccGenNum = Long.valueOf(nRaccgStr.substring(4, 10)).toString();
        String strSent = sentenzaRicorso.getSentenza().toString();
        String NSentFormat = "" + Integer.parseInt(strSent.substring(4, 9)) + "/" + strSent.substring(0, 4) + "/"
                + strSent.substring(9, 12);
        String note = nRaccGenNum + "-" + nRaccGenAnno + " (Provv. " + NSentFormat + " " + sentenzaRicorso.getTipoSentenza() + ")";
        repo.createStoriaNraccg(utenteOperatore, ricorso.getNrg(), sentenzaRicorso.getId(), note, idSezione.longValue());
        return nRaccg;
    }

    private ProvvedimentoPenale getIdProvvByIdCat(EntityManager entityManager, Long idCat) {
        Query query = entityManager.createNativeQuery(
                "SELECT * FROM PENALE_PROVVEDIMENTI pp WHERE FK_IDCAT = :IDCAT ORDER BY DATA_ULTIMA_MODIFICA DESC ",
                ProvvedimentoPenale.class);
        query.setParameter("IDCAT", idCat);
        List<ProvvedimentoPenale> result = query.getResultList();
        if (result != null && !result.isEmpty()) {
            return result.get(0);
        }
        return null;
    }

    public void registraErrorePubblicazione(Deposito deposito, String operatore, boolean inviaGL) {
        LOGGER.info("Registro l'errore pubblicazione. idBusta:" + deposito.getIdCat());
        EntityManager entityManager = emfCaricamento.createEntityManager();
        EntityTransaction transaction = entityManager.getTransaction();
        transaction.begin();
        try {
            deposito.setStato(Deposito.Stato.ERROREPUBBLICAZIONE);
            entityManager.merge(deposito);
            transaction.commit();
            LOGGER.info("Registro l'errore pubblicazione penale sul gl-cass. idBusta:" + deposito.getIdCat() + ", invioGL-CASS:" + inviaGL);
            // invia messaggio al gl-cass per ritrasmissione da CUA
            if (inviaGL) {
                ServiziAccettazione accettazioneGL = serviceBuilder.getAccettazioneService();
                accettazioneGL.errorePubblicazionePenale(deposito.getIdCat().toString(), operatore);
            }
            LOGGER.info("Fine registro l'errore pubblicazione. idBusta:" + deposito.getIdCat());
        } catch (Throwable ex) {
            LOGGER.error("Errore durante registrazione errore di pubblicazione. idDeposito:" + deposito.getIdCat(), ex);
            if (transaction.isActive()) {
                transaction.rollback();
            }
        } finally {
            entityManager.close();
        }
    }

    public String ritrasmettiPubblicazione(Long idDeposito, String operatore) throws Exception {
        if (idDeposito == null) {
            throw new CspBackendException("Riferimento deposito non corretto. idDeposito null", RestErrorCodeEnum.ID_DEPOSITO_NULL, 433);
        }
        PubblicazioneSemaforo.getInstance().registraPubblicazione(idDeposito, false);
        EntityManager entityManager = emfCaricamento.createEntityManager();
        EntityTransaction transaction = entityManager.getTransaction();
        transaction.begin();

        try {
            Deposito deposito = entityManager.find(Deposito.class, idDeposito);
            if (!Deposito.Stato.ERROREPUBBLICAZIONE.equals(deposito.getStato())) {
                LOGGER.error("Impossibile ritrasmettere la pubblicazione, lo stato del deposito non è in errore pubblicazione. idDeposito:"
                        + idDeposito);
                throw new CspCommonRestWarningException(
                        "Impossibile ritrasmettere la pubblicazione, lo stato del deposito non è in errore pubblicazione.",
                        RestErrorCodeEnum.RITRASMISSIONE_PROVV_ERROR);
            }

            ElaborazioneProvvedimento elabDep = elaborazioneProvvedimentoService.findElaborazioneProvvedimentoByIdDeposito(entityManager,
                    idDeposito);
            SicPenaleRepository repo = new SicPenaleRepository(entityManager);
            RicorsoSic ricorso = repo.getRicorsoByElaborazioneDeposito(elabDep);

            String nrgReale = ricorso.getNrgReale().toString();

            String annoRicorso = nrgReale.substring(0, 4);
            String numeroRicorso = Long.valueOf(nrgReale.substring(4, 10)).toString();

            if (deposito.isDepositoMagistratoPubblicaInAccettazione()) {
                try {
                    pubblicaProvvedimento(elabDep, deposito, operatore, numeroRicorso, annoRicorso, null);
                } catch (Throwable t) {
                    // TODO perchè non ritrasmetto a gl-cass?
                    registraErrorePubblicazione(deposito, operatore, false);
                    LOGGER.warn("Deposito accettato correttamente. Errori durante la pubblicazione. idDeposito:" + idDeposito);
                    throw new CspCommonRestWarningException("Deposito accettato correttamente. Errori durante la pubblicazione.",
                            RestErrorCodeEnum.PUBBLICAZIONE_ERROR, t, ErrorTypeEnum.SUCCESS);
                }
            }
            String result = numeroRicorso + "/" + annoRicorso;
            LOGGER.info("Ritrasmissione pubblicazione provvedimento completata. Risultato:" + result);

            return result;
        } catch (Throwable t) {
            if (transaction.isActive()) {
                transaction.rollback();
            }
            throw t;
        } finally {
            entityManager.close();
            PubblicazioneSemaforo.getInstance().sbloccaPubblicazione(idDeposito, false);
        }
    }

    public void checkPubblicazione(ElaborazioneProvvedimento elabDep, Deposito deposito) throws CspBackendException {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            SicPenaleRepository repo = new SicPenaleRepository(entityManager);
            Sentenza sentenzaDettaglio = repo.getSetenzaByIdCatAndNrg(elabDep.getDeposito().getIdCat(), deposito.getNrg());

            if (elabDep.getTipoVerificato().equals(TipoProvvedimentoMagistrato.Ordinanza)
                    || elabDep.getTipoVerificato().equals(TipoProvvedimentoMagistrato.Sentenza)) {
                if (elabDep.getOscuramentoDati() == null && elabDep.getTipoVerificato() == null && elabDep.getIdDispositivo() == null) {
                    LOGGER.error(
                            "Impossibile accettare il deposito. Oscuramento dati  / tipo verificato / dispositivo non valorizzati correttamente. idCat:"
                                    + elabDep.getDeposito().getIdCat() + ", nrg:" + deposito.getNrg());
                    throw new CspCommonRestWarningException(
                            "Impossibile accettare il deposito. Oscuramento dati  / tipo verificato / dispositivo non valorizzati correttamente.",
                            RestErrorCodeEnum.DATA_PROVV_NULL);
                }
                checkTipoProvvedimento(elabDep, sentenzaDettaglio);
            } else {
                LOGGER.error("Provvedimento non pubblicabile. Il tipo provvedimento non è Ordinanza o Sentenza. idCat:"
                        + elabDep.getDeposito().getIdCat() + ", nrg:" + deposito.getNrg() + ", tipo provveidmento:"
                        + elabDep.getTipoVerificato());
                throw new CspCommonRestWarningException("Impossibile accettare il deposito. Provvedimento non pubblicabile.",
                        RestErrorCodeEnum.PROVV_NON_PUBB);
            }
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore nella ricerca del provvedimento. idCat:" + deposito.getIdCat());
            throw new CspCommonRestWarningException("Errore nella ricerca del provvedimento.", RestErrorCodeEnum.PROVV_SEARCH_ERROR);
        } finally {
            entityManager.close();
        }
    }

    private void checkTipoProvvedimento(ElaborazioneProvvedimento elabProv, Sentenza sentenzaRicorso) throws CspCommonRestWarningException {
        if (elabProv == null || sentenzaRicorso == null) {
            return;
        }
        String tipoSentenza = sentenzaRicorso.getTipoSentenza();
        if ("OR".equals(tipoSentenza) && !elabProv.getTipoVerificato().equals(TipoProvvedimentoMagistrato.Ordinanza)
                || "SE".equals(tipoSentenza) && !elabProv.getTipoVerificato().equals(TipoProvvedimentoMagistrato.Sentenza)) {
            LOGGER.error("Il provvedimento che si sta cercando di pubblicare è differente rispetto a quanto scaricato sul SIC. idSentenza:"
                    + sentenzaRicorso.getId() + ", tipo provvediemento SIC:" + tipoSentenza + ", tipo provvedimento:"
                    + elabProv.getTipoVerificato());
            throw new CspCommonRestWarningException(
                    "Il provvedimento che si sta cercando di pubblicare è differente rispetto a quanto scaricato sul SIC.",
                    RestErrorCodeEnum.TIPO_PROVV_NOT_MATCHING);
        }
    }

    //
    /**
     * Setta e restituisce le properties necessarie per accedere al documentale
     *
     * @return
     */
    public Properties getPubblicazioneProperties() {
        Properties props = new Properties();
        props.setProperty("GIUSTIZIA.ENDPOINT", System.getProperty("documentale.endpoint"));
        props.setProperty("FTP.HOST", System.getProperty("documentale.ftp.host"));
        props.setProperty("FTP.PORT", System.getProperty("documentale.ftp.port"));
        props.setProperty("FTP.ROOTUSER", System.getProperty("documentale.ftp.rootuser"));
        props.setProperty("FTP.ROOTPWD", System.getProperty("documentale.ftp.rootpwd"));
        props.setProperty("FTP.BIGTHRESHOLD", System.getProperty("documentale.ftp.bigthreshold"));
        return props;
    }

    // /**
    // * Costruisco l'autenticazione per poter accedere ai servizi del documentale
    // *
    // * @return
    // */
    public Autenticazione getAutenticazione() {
        Autenticazione auth = new Autenticazione();
        auth.setIdutente(Integer.parseInt(System.getProperty("documentale.auth.id.utente")));
        auth.setCodiceapplicazione(System.getProperty("documentale.auth.codice"));
        auth.setPassword(System.getProperty("documentale.auth.password"));
        auth.setIdufficio(Long.valueOf(System.getProperty("documentale.auth.ufficio")));
        return auth;
    }

    // public void finePubblicazioneSic(Long idPubblicazioneSic) throws Exception {
    // PubblicazioneSemaforo.getInstance().sbloccaPubblicazione(idPubblicazioneSic, true);
    // }
    //
    // public void inizioPubblicazioneSic(Long idPubblicazioneSic) throws Exception {
    // PubblicazioneSemaforo.getInstance().registraPubblicazione(idPubblicazioneSic, true);
    // }
}
