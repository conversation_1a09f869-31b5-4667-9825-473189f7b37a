/**
 *
 */
package it.netservice.csp.backend.rest.ricorsi;

import java.util.List;

import javax.annotation.security.RolesAllowed;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.log4j.Logger;

import it.netservice.common.rest.backend.InputPayloadBackend;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.dto.RicercaRicorsiBaseDto;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.RicorsiExcpetion;
import it.netservice.csp.backend.rest.AbstractCspBackendController;
import it.netservice.csp.backend.service.ricorsi.RicorsiService;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.penale.model.sic.criteria.csp.RicercaRicorsiBaseInUdienzaCriteriaParameters;
import it.netservice.penale.model.sic.csp.PenaleParti;
import it.netservice.penale.model.sic.csp.PenaleReatiRicorso;
import it.netservice.penale.model.sic.csp.PenaleUdienzaRicorso;

/**
 * Servizi di ricerca ricorsi per il csp.
 *
 * <AUTHOR>
 */
@RolesAllowed({ "CSPCLIENT" })
@Path("/ricorsi")
public class RicorsiController extends AbstractCspBackendController {

    private static final Logger LOGGER = Logger.getLogger(RicorsiController.class);

    @Inject
    private RicorsiService ricorsiService;

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    public Response ricerca(@Context HttpServletRequest context,
            InputPayloadBackend<RicercaRicorsiBaseInUdienzaCriteriaParameters> inputPayload) throws CspBackendException {
        try {
            RicercaRicorsiBaseDto ricercaRicorsiBaseDto = ricorsiService.ricercaRicorsi(inputPayload);
            return Response.ok(ricercaRicorsiBaseDto).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            throw new RicorsiExcpetion("Errore durante la ricerca dei ricorsi", RestErrorCodeEnum.SEARCH_RICORSI_ERROR, e);
        }
    }

    @GET
    @Path("/reati")
    public Response getReatiRicorsoByNrg(@Context HttpServletRequest context, @QueryParam("nrg") String nrg) throws CspBackendException {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        SicPenaleRepository repo = new SicPenaleRepository(entityManager);
        try {
            List<PenaleReatiRicorso> result = repo.getPenaleReatiRicorsoByNrg(new Long(nrg));
            return Response.ok(result).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            throw new RicorsiExcpetion("Errore durante la ricerca dei reati. nrg:" + nrg, RestErrorCodeEnum.REATI_RICORSO_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET
    @Path("/listaParti")
    public Response getParti(@Context HttpServletRequest context, @QueryParam("parte") String cognomeParte) throws CspBackendException {
        LOGGER.info("Avvio ricerca parti. cognomeParte:" + cognomeParte);
        EntityManager entityManager = emfCaricamento.createEntityManager();
        SicPenaleRepository repo = new SicPenaleRepository(entityManager);
        try {
            List<String> result = repo.getCognomiParte(cognomeParte);
            LOGGER.info("Ricerca parti completata. Risultati trovati:" + result.size());
            return Response.ok(result).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            throw new RicorsiExcpetion("Errore durante la ricerca delle parti. cognomeParte:" + cognomeParte,
                    RestErrorCodeEnum.SEARCH_RICORSI_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET
    @Path("/parti")
    public Response getPartiRicorsoByNrg(@Context HttpServletRequest context, @QueryParam("nrg") String nrg) throws CspBackendException {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        SicPenaleRepository repo = new SicPenaleRepository(entityManager);

        try {
            List<PenaleParti> result = repo.getPenalePartiRicorsoByNrg(new Long(nrg));
            return Response.ok(result).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            throw new RicorsiExcpetion("Errore durante la ricerca delle parti. nrg:" + nrg, RestErrorCodeEnum.PARTI_RICORSO_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET
    @Path("/riuniti/{idRicUdien}")
    public Response getPartiRicorsoByNrg(@Context HttpServletRequest context, @PathParam("idRicUdien") Long idRicUdienPadre)
            throws CspBackendException {
        List<PenaleUdienzaRicorso> result = ricorsiService.getRicorsiRiuniti(idRicUdienPadre);
        return Response.ok(result).build();
    }

}
