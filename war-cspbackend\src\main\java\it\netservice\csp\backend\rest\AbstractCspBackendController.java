package it.netservice.csp.backend.rest;

import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.TimeZone;

import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public abstract class AbstractCspBackendController extends AbstractCspBackend {

    public static Calendar fixTime(Calendar toFix) {
        // IMPORTANTE!!!! SPIEGA COME FUNZIONA IL TEMPO
        // Dato che l'orario che mi arriva dal GL come calendar mi arriva come se i depositi fossero arrivati a Londra (GMT)
        // con l'orario italiano (e' complesso, lo so), sono costretto a mettere a posto a mano la TimeZone.
        // Pertanto, sperando che Cassazione non si sposti mai dall'Italia per dislocarsi in uno stato con una TimeZone diversa
        // (o che le TimeZone cambino in maniera radicale) mi recupero la TimeZone di Roma e vado a togliere l'offset dall'orario di
        // ricezione.
        // (non posso fare il getDefault,per qualche motivo sconosciuto mi viene recuperata la TZ GMT)
        TimeZone tz = TimeZone.getTimeZone("Europe/Rome");
        Calendar now = GregorianCalendar.getInstance();
        int offset = -tz.getOffset(now.getTimeInMillis());
        boolean dayLightTimeNow = tz.inDaylightTime(now.getTime());
        toFix.add(Calendar.MILLISECOND, offset);
        boolean dayLightTimeReceived = tz.inDaylightTime(toFix.getTime());
        if (dayLightTimeNow && !dayLightTimeReceived) {
            // Vuol dire che in Italia c'è l'ora solare ma quando ho ricevuto il deposito c'era l'ora legale
            // quindi aggiungo un'ora
            toFix.add(Calendar.HOUR, 1);
        } else if (!dayLightTimeNow && dayLightTimeReceived) {
            // Vuol dire che in Italia c'è l'ora legale ma quando ho ricevuto il deposito c'era l'ora solare
            // quindi tolgo un'ora
            toFix.add(Calendar.HOUR, -1);
        }
        return toFix;
    }

}
