/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package it.netservice.csp.backend.pubblicazioneDepositi;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.*;

import org.apache.log4j.Logger;

import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.excpetion.PubblicazioneException;
import it.netservice.csp.backend.model.ucu.UcuDocumento;
import it.netservice.csp.backend.model.ucu.UcuJobItalgiure;
import it.netservice.csp.backend.model.ucu.UcuStoriaDocumento;
import it.netservice.csp.backend.sic.service.depositi.PubblicazioneUtil;
import it.netservice.penale.model.csp.ElaborazioneProvvedimento;
import it.netservice.penale.model.enums.TipoOscuramentoPubblica;
import it.netservice.penale.model.enums.TipoProvvedimentoMagistrato;

/**
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class UfficioCopieService {

    private static final Logger LOGGER = Logger.getLogger(UfficioCopieService.class);

    @PersistenceUnit(unitName = "DBUCU")
    protected EntityManagerFactory emfUcu;

    public UfficioCopieService() {
    }

    private String mapFlagOscuramento(TipoOscuramentoPubblica oscuramentoDati) {
        if (PubblicazioneUtil.hasOscurato(oscuramentoDati)) {
            return "S";
        }
        return "N";
    }

    private BigDecimal mapImpostaRegistro(Boolean contributoUnificatoEsente, Boolean contributoUnificatoStabilita) {
        if ((contributoUnificatoEsente != null && contributoUnificatoEsente.booleanValue())) {
            return new BigDecimal(6);
        }
        return null;
    }

    private BigDecimal mapTipoDocumento(TipoProvvedimentoMagistrato tipoVerificato) {
        switch (tipoVerificato) {
            // case DecretoEstinzione:
            // return new BigDecimal(6);
            case Sentenza:
                return new BigDecimal(4);
            case Ordinanza:
                return new BigDecimal(3);
            default:
                return null;
        }
    }

    private BigDecimal mapStatoDoc(Boolean rilasciabile) {
        if (rilasciabile) {
            return new BigDecimal(2);
        } else {
            return new BigDecimal(1);
        }
    }

    private <T> TypedQuery<T> fillQuery(EntityManager em, String query, Class<T> clazz, Map<String, Object> params) {
        TypedQuery<T> q = em.createQuery(query, clazz);
        for (String key : params.keySet()) {
            q.setParameter(key, params.get(key));
        }
        return q;
    }

    private <T> T findEntityByQuery(EntityManager em, String query, Class<T> clazz, Map<String, Object> params) {
        T result = null;
        try {
            result = fillQuery(em, query, clazz, params).getSingleResult();
        } catch (NoResultException ex) {
            LOGGER.warn("Oggetto " + clazz.getName() + " non trovato");
        }
        return result;
    }

    public static <K> Map<String, K> getCompiledMap(Object... params) {
        Map<String, K> res = new HashMap<>();
        for (int i = 0; i < params.length - 1; i += 2) {
            res.put(params[i].toString(), (K) params[i + 1]);
        }
        return res;
    }

    public void pubblicaUCU(ElaborazioneProvvedimento elabProv, String operatore, String nomeFile, long idDocumentoPdoc,
            String nomeFileOscurato, long idDocumentoPdocOscurato) throws Throwable {
        EntityManager entityManagerUcu = emfUcu.createEntityManager();
        EntityTransaction transactionUcu = entityManagerUcu.getTransaction();
        transactionUcu.begin();

        LOGGER.info("Inserisco il file nell'ufficio copie. nomeDocumento:" + nomeFile);
        PubblicazioneProvvedimentoUCU pubblicazioneProvvedimentoUCU = new PubblicazioneProvvedimentoUCU(elabProv);
        pubblicazioneProvvedimentoUCU.setIdDocumentoPdoc(idDocumentoPdoc);
        pubblicazioneProvvedimentoUCU.setNomeFilePdoc(nomeFile);
        pubblicazioneProvvedimentoUCU.setIdProvvedimento(elabProv.getIdProvvedimentoSic());
        pubblicazioneProvvedimentoUCU.setUtente(operatore);
        pubblicazioneProvvedimentoUCU.setIdDocumentoPdocOscurato(idDocumentoPdocOscurato);
        pubblicazioneProvvedimentoUCU.setNomeFilePdocOscurato(nomeFileOscurato);
        try {
            // aggiornamento schema UCU
            UcuDocumento ucuDocumento = saveUcuDocumento(pubblicazioneProvvedimentoUCU, entityManagerUcu);

            // riempimento oggetto storia
            BigDecimal idStoriaDocumento = saveUcuStoriaDocumento(entityManagerUcu, pubblicazioneProvvedimentoUCU, ucuDocumento);
            saveUcuJobItalgiure(entityManagerUcu, idStoriaDocumento);
            transactionUcu.commit();
        } catch (Throwable t) {
            if (transactionUcu.isActive()) {
                transactionUcu.rollback();
            }
            LOGGER.error("Errore durante pubblicazione ad ufficio copie. idDocumentoPdoc:" + idDocumentoPdoc + ", idProvvSic:"
                    + elabProv.getIdProvvedimentoSic());
            throw new PubblicazioneException("Errore durante pubblicazione ad ufficio copie", RestErrorCodeEnum.UCU_PUBB_ERROR, t);
        } finally {
            if (entityManagerUcu != null) {
                try {
                    entityManagerUcu.close();
                } catch (Exception ex) {
                    LOGGER.error("Errore chiusura entity manager", ex);
                }
            }
        }
        LOGGER.info("Pubblicazione verso ufficio copie completata");
    }

    private UcuDocumento saveUcuDocumento(PubblicazioneProvvedimentoUCU pubblicazioneProvvedimentoDTO, EntityManager entityManager) {
        UcuDocumento ucuDocumento = new UcuDocumento();
        ucuDocumento.setSicIdProvvedimento(new BigDecimal(pubblicazioneProvvedimentoDTO.getIdProvvedimento()));
        ucuDocumento.setDtModifica(new Date(System.currentTimeMillis()));
        ucuDocumento.setFlagSettore("P");
        ucuDocumento.setFlagOsc(mapFlagOscuramento(pubblicazioneProvvedimentoDTO.getOscuramentoDati()));
        ucuDocumento.setIdFile(new BigDecimal(pubblicazioneProvvedimentoDTO.getIdDocumentoPdoc()));
        ucuDocumento.setNomeFile(pubblicazioneProvvedimentoDTO.getNomeFilePdoc());
        ucuDocumento.setUcuTpImpostaReg(mapImpostaRegistro(pubblicazioneProvvedimentoDTO.getContributoUnificatoEsente(),
                pubblicazioneProvvedimentoDTO.getContributoUnificatoStabilita()));
        ucuDocumento.setUcuTpStatoDoc(mapStatoDoc(pubblicazioneProvvedimentoDTO.getRilasciabile()));
        ucuDocumento.setNumPagine(new BigDecimal(pubblicazioneProvvedimentoDTO.getNumeroPagine()));
        ucuDocumento.setVersione(new BigDecimal(1));
        ucuDocumento.setUcuTpTipoProvv(mapTipoDocumento(pubblicazioneProvvedimentoDTO.getTipoVerificato()));
        ucuDocumento.setUtModifica(pubblicazioneProvvedimentoDTO.getUtente());
        ucuDocumento.setFlagMigrato("N");
        // se presente anche il file oscurato, salva anche le relative informazioni
        if (pubblicazioneProvvedimentoDTO.getNomeFilePdocOscurato() != null) {
            ucuDocumento.setIdFileOscurato(new BigDecimal(pubblicazioneProvvedimentoDTO.getIdDocumentoPdocOscurato()));
            ucuDocumento.setNomeFileOscurato(pubblicazioneProvvedimentoDTO.getNomeFilePdocOscurato());
            ucuDocumento.setVersioneOscurato(new BigDecimal(1));
        }
        entityManager.persist(ucuDocumento);
        // creazione della riga storia documento...ripresa del salvato x avere id
        ucuDocumento = findEntityByQuery(entityManager,
                "FROM UcuDocumento WHERE sicIdProvvedimento = :sicIdProvvedimento and flagSettore = :flagSettore", UcuDocumento.class,
                getCompiledMap("sicIdProvvedimento", new BigDecimal(pubblicazioneProvvedimentoDTO.getIdProvvedimento()), "flagSettore",
                        "P"));
        return ucuDocumento;
    }

    private BigDecimal saveUcuStoriaDocumento(EntityManager entityManager, PubblicazioneProvvedimentoUCU pubblicazioneProvvedimentoDTO,
            UcuDocumento ucuDocumento) {
        UcuStoriaDocumento ucuStoriaDocumento = new UcuStoriaDocumento();
        ucuStoriaDocumento.setIdDocumento(ucuDocumento.getIdDocumento());
        ucuStoriaDocumento.setDtModifica(new Date(System.currentTimeMillis()));
        ucuStoriaDocumento.setFlagOsc(mapFlagOscuramento(pubblicazioneProvvedimentoDTO.getOscuramentoDati()));
        ucuStoriaDocumento.setIdFile(ucuDocumento.getIdFile());
        ucuStoriaDocumento.setNomeFile(ucuDocumento.getNomeFile());
        ucuStoriaDocumento.setUcuTpImpostaReg(ucuDocumento.getUcuTpImpostaReg());
        ucuStoriaDocumento.setUcuTpStatoDoc(ucuDocumento.getUcuTpStatoDoc());
        ucuStoriaDocumento.setNumPagine(ucuDocumento.getNumPagine());
        ucuStoriaDocumento.setVersione(ucuDocumento.getVersione());
        ucuStoriaDocumento.setUcuTpTipoProvv(mapTipoDocumento(pubblicazioneProvvedimentoDTO.getTipoVerificato()));
        ucuStoriaDocumento.setUtModifica(ucuDocumento.getUtModifica());
        ucuStoriaDocumento.setFlagMigrato(ucuDocumento.getFlagMigrato());
        ucuStoriaDocumento.setIdAzione(new BigDecimal(1));
        entityManager.persist(ucuStoriaDocumento);
        entityManager.flush();

        Query query = entityManager.createNativeQuery(
                "SELECT id_storia_documento FROM ucu_storia_documento where " + "id_documento = " + ucuDocumento.getIdDocumento());
        return (BigDecimal) query.getSingleResult();
    }

    private void saveUcuJobItalgiure(EntityManager entityManager, BigDecimal idStoriaDocumento) {
        UcuJobItalgiure ucuJobItalgiure = new UcuJobItalgiure();
        ucuJobItalgiure.setFlagInviato("N");
        ucuJobItalgiure.setDtInserimento(new Date());
        ucuJobItalgiure.setStoriaDocumento(idStoriaDocumento);
        entityManager.persist(ucuJobItalgiure);
    }

}
