package it.netservice.csp.backend.rest.monitoraggio;

import java.util.*;

import javax.annotation.security.RolesAllowed;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.log4j.Logger;

import it.netservice.common.rest.backend.InputPayloadBackend;
import it.netservice.csp.backend.dto.PenaleUdienzeViewDto;
import it.netservice.csp.backend.dto.RestErrorCodeEnum;
import it.netservice.csp.backend.excpetion.CspBackendException;
import it.netservice.csp.backend.excpetion.MonitoraggioException;
import it.netservice.csp.backend.rest.AbstractCspBackendController;
import it.netservice.csp.backend.service.monitoraggio.MonitoraggioService;
import it.netservice.csp.backend.sic.service.repository.SicPenaleRepository;
import it.netservice.penale.model.common.dettaglioricorso.StoricoProvvedimento;
import it.netservice.penale.model.sic.criteria.csp.PenaleUdienzeViewCriteriaParameters;
import it.netservice.penale.model.sic.csp.EstensoriSentenzaView;
import it.netservice.penale.model.sic.csp.PenaleParti;

@RolesAllowed({ "CSPCLIENT" })
@Path("/monitoraggio")
public class MonitoraggioController extends AbstractCspBackendController {
    private static final Logger LOGGER = Logger.getLogger(MonitoraggioController.class);

    @Inject
    private MonitoraggioService monitoraggioService;

    @GET()
    @Path("/trackingProvvedimento/{id}")
    public Response trackingProvvedimento(@Context HttpServletRequest context, @PathParam("id") String idProvv) throws CspBackendException {
        LOGGER.info("Richiesta tracking per provvedimento. idProvv:" + idProvv);
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            List<StoricoProvvedimento> resultList = monitoraggioService.getStoricoProvvedimento(idProvv, entityManager);
            return Response.ok(resultList).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante il recupero dello informazioni dello storico del provvedimento. idProvv:" + idProvv);
            throw new MonitoraggioException("Errore durante il recupero dello informazioni dello storico del provvedimento",
                    RestErrorCodeEnum.STORICO_PROVV_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET()
    @Path("/trackingProvvedimentoByIdCat/{idCat}")
    public Response trackingProvvedimento(@Context HttpServletRequest context, @PathParam("idCat") Integer idCat)
            throws CspBackendException {
        LOGGER.info("Richiesta tracking per provvedimento. idCat:" + idCat);
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            String idProvvByNrg = monitoraggioService.getIdProvvByIdCat(entityManager, idCat);
            List<StoricoProvvedimento> resultList = monitoraggioService.getStoricoProvvedimento(idProvvByNrg, entityManager);
            return Response.ok(resultList).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante il recupero dello informazioni dello storico del provvedimento. idCat:" + idCat);
            throw new MonitoraggioException("Errore durante il recupero dello informazioni dello storico del provvedimento",
                    RestErrorCodeEnum.STORICO_PROVV_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET()
    @Path("/infoDate/{id}/{idSentenza}")
    public Response infoDate(@Context HttpServletRequest context, @PathParam("id") String idProvv,
            @PathParam("idSentenza") String idSentenza) throws CspBackendException {
        LOGGER.info("Richiesta info date per provvedimento " + idProvv);
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            Map<String, Date> resultList = monitoraggioService.getDateFascicolo(idProvv, idSentenza, entityManager);
            return Response.ok(resultList).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante il recupero delle date del provvedimento. idProvv:" + idProvv);
            throw new MonitoraggioException("Errore durante il recupero delle date del provvedimento",
                    RestErrorCodeEnum.STORICO_PROVV_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    public Response ricerca(@Context HttpServletRequest context, InputPayloadBackend<PenaleUdienzeViewCriteriaParameters> inputPayload)
            throws CspBackendException {
        LOGGER.info("Richiesta ricerca udienze");
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            PenaleUdienzeViewDto penaleUdienzeViewDto = monitoraggioService.ricercaUdienze(inputPayload, entityManager);
            return Response.ok(penaleUdienzeViewDto).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la ricerca delle udienze.");
            throw new MonitoraggioException("Errore durante la ricerca udienze", RestErrorCodeEnum.STORICO_PROVV_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @POST
    @Path("/esporta")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.MEDIA_TYPE_WILDCARD)
    public Response esportaSelezionati(@Context HttpServletRequest context,
            InputPayloadBackend<PenaleUdienzeViewCriteriaParameters> inputPayload) throws CspBackendException {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        LOGGER.info("Esportazione risultati ricerca Monitoraggio Udienze per ID selezionati:"
                + inputPayload.getCriteriaParameters().getIdRicUdienza().toString());
        try {
            return monitoraggioService.esportaRisultatiRicercaSelezionati(inputPayload, entityManager);
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante l'esportazione dei dati.");
            throw new MonitoraggioException("Errore durante l'esportazione dei dati", RestErrorCodeEnum.EXPORT_UDIENZE_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @POST
    @Path("/esporta-monitoraggio")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.MEDIA_TYPE_WILDCARD)
    public Response esportaMonitoraggio(@Context HttpServletRequest context,
            InputPayloadBackend<PenaleUdienzeViewCriteriaParameters> inputPayload) throws CspBackendException {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        LOGGER.info("Esportazione risultati ricerca Monitoraggio Navetta per ID selezionati:"
                + inputPayload.getCriteriaParameters().getIdRicUdienza().toString());
        try {
            return monitoraggioService.esportaMonitoraggio(inputPayload, entityManager);
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante l'esportazione dei dati.");
            throw new MonitoraggioException("Errore durante l'esportazione dei dati", RestErrorCodeEnum.EXPORT_UDIENZE_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET()
    @Path("/listaPresidenti")
    public Response getPresidenti(@Context HttpServletRequest context, @QueryParam("sezione") String siglaSezione,
            @QueryParam("dataUdienzaDa") String dataUdienzaDa, @QueryParam("dataUdienzaA") String dataUdienzaA,
            @QueryParam("tipoUdienza") String tipoUdienza, @QueryParam("collegio") String collegio,
            @QueryParam("dataMinutaDa") String dataMinutaDa, @QueryParam("dataMinutaA") String dataMinutaA,
            @QueryParam("dataPubblicazioneDa") String dataPubblicazioneDa, @QueryParam("dataPubblicazioneA") String dataPubblicazioneA,
            @QueryParam("numeroRicorsoDa") String numeroRicorsoDa, @QueryParam("numeroRicorsoA") String numeroRicorsoA,
            @QueryParam("annoRicorsoDa") String annoRicorsoDa, @QueryParam("annoRicorsoA") String annoRicorsoA) throws CspBackendException {
        LOGGER.info("Richiesta ricerca presidenti");
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            Map<String, String> resultList = monitoraggioService.getPresidenti(siglaSezione, dataUdienzaDa, dataUdienzaA, tipoUdienza,
                    collegio, dataMinutaDa, dataMinutaA, dataPubblicazioneDa, dataPubblicazioneA, numeroRicorsoDa, numeroRicorsoA,
                    annoRicorsoDa, annoRicorsoA, entityManager);
            LOGGER.info("Ricerca presidenti completata");
            return Response.ok(resultList).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la ricerca dei presidenti. Sezione:" + siglaSezione);
            throw new MonitoraggioException("Errore durante la ricerca dei presidenti", RestErrorCodeEnum.MONITORAGGIO_SEARCH_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET()
    @Path("/listaEstensori")
    public Response getEstensori(@Context HttpServletRequest context, @QueryParam("sezione") String siglaSezione,
            @QueryParam("dataUdienzaDa") String dataUdienzaDa, @QueryParam("dataUdienzaA") String dataUdienzaA,
            @QueryParam("tipoUdienza") String tipoUdienza, @QueryParam("collegio") String collegio,
            @QueryParam("dataMinutaDa") String dataMinutaDa, @QueryParam("dataMinutaA") String dataMinutaA,
            @QueryParam("dataPubblicazioneDa") String dataPubblicazioneDa, @QueryParam("dataPubblicazioneA") String dataPubblicazioneA,
            @QueryParam("numeroRicorsoDa") String numeroRicorsoDa, @QueryParam("numeroRicorsoA") String numeroRicorsoA,
            @QueryParam("annoRicorsoDa") String annoRicorsoDa, @QueryParam("annoRicorsoA") String annoRicorsoA) throws CspBackendException {
        LOGGER.info("Richiesta ricerca estensori");
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            Map<String, String> resultList = monitoraggioService.getEstensori(siglaSezione, dataUdienzaDa, dataUdienzaA, tipoUdienza,
                    collegio, dataMinutaDa, dataMinutaA, dataPubblicazioneDa, dataPubblicazioneA, numeroRicorsoDa, numeroRicorsoA,
                    annoRicorsoDa, annoRicorsoA, entityManager);
            LOGGER.info("Ricerca estensori completata");
            return Response.ok(resultList).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la ricerca degli estensori. Sezione:" + siglaSezione);
            throw new MonitoraggioException("Errore durante la ricerca degli estensori", RestErrorCodeEnum.MONITORAGGIO_SEARCH_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET()
    @Path("/listaEstensoriByUdienza")
    public Response getEstensoriByUdienza(@Context HttpServletRequest context, @QueryParam("idUdienza") String idUdienza)
            throws CspBackendException {
        LOGGER.info("Richiesta ricerca estensori per udienza " + idUdienza);
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            Map<String, String> resultList = monitoraggioService.getEstensoriByUdienza(idUdienza, entityManager);
            LOGGER.info("Richiesta ricerca estensori per udienza " + idUdienza + " completata");
            return Response.ok(resultList).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la ricerca degli estensori. idUdienza:" + idUdienza);
            throw new MonitoraggioException("Errore durante la ricerca degli estensori", RestErrorCodeEnum.MONITORAGGIO_SEARCH_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET()
    @Path("/listaEstensoriBySentenza")
    public Response getEstensoriBySentenza(@Context HttpServletRequest context, @QueryParam("idSentenza") String idSentenza)
            throws CspBackendException {
        LOGGER.info("Richiesta ricerca estensori per sentenza " + idSentenza);
        EntityManager entityManager = emfCaricamento.createEntityManager();
        try {
            List<EstensoriSentenzaView> resultList = monitoraggioService.getEstensoriBySentenza(idSentenza, entityManager);
            LOGGER.info("Richiesta ricerca estensori per udienza " + idSentenza + " completata");
            return Response.ok(resultList).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            LOGGER.error("Errore durante la ricerca degli estensori. idSentenza:" + idSentenza);
            throw new MonitoraggioException("Errore durante la ricerca degli estensori", RestErrorCodeEnum.MONITORAGGIO_SEARCH_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET()
    @Path("/listaParti")
    public Response getParti(@Context HttpServletRequest context, @QueryParam("parte") String cognomeParte) throws CspBackendException {
        LOGGER.info("Avvio ricerca parti. cognomeParte:" + cognomeParte);
        EntityManager entityManager = emfCaricamento.createEntityManager();
        SicPenaleRepository repo = new SicPenaleRepository(entityManager);
        try {
            List<String> result = repo.getCognomiParte(cognomeParte);
            LOGGER.info("Ricerca parti completata. Risultati trovati:" + result.size());
            return Response.ok(result).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            throw new MonitoraggioException("Errore durante la ricerca delle parti. cognomeParte:" + cognomeParte,
                    RestErrorCodeEnum.MONITORAGGIO_SEARCH_ERROR, e);
        } finally {
            entityManager.close();
        }
    }

    @GET
    @Path("/parti")
    public Response getPartiRicorsoByNrg(@Context HttpServletRequest context, @QueryParam("nrg") String nrg) throws CspBackendException {
        EntityManager entityManager = emfCaricamento.createEntityManager();
        SicPenaleRepository repo = new SicPenaleRepository(entityManager);

        try {
            List<PenaleParti> result = repo.getPenalePartiRicorsoByNrg(new Long(nrg));
            return Response.ok(result).build();
        } catch (Throwable e) {
            if (e instanceof CspBackendException) {
                throw (CspBackendException) e;
            }
            throw new MonitoraggioException("Errore durante la ricerca delle parti. nrg:" + nrg, RestErrorCodeEnum.PARTI_RICORSO_ERROR, e);
        } finally {
            entityManager.close();
        }
    }
}
