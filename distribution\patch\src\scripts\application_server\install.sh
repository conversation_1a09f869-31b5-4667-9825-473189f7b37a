#!/bin/sh
#
# Installazione patch su JBoss7
#
# author:  luca.santoiemma
# version: $Id: install.sh 13 2016-12-20 15:10:00Z luca.santoiemma $
#

today=`date +%Y%m%d%H%M%S`
prettyToday=`date +"%d/%m/%Y %H:%M:%S"`
patchVersion=`cat include/.version`

[ -z "$JBOSS_USER" ] && JBOSS_USER=jboss
[ -z "$JBOSS_GROUP" ] && JBOSS_GROUP=jboss

if [ -z "$JBOSS_HOME" ] ; then
  JBOSS_HOME=`su - $JBOSS_USER -c pwd`
fi

if [ -z "$BACKUP_DIR" ] ; then
  BACKUP_DIR=backup_$today
fi

if [ ! -e $BACKUP_DIR ]; then
  mkdir -p $BACKUP_DIR 
fi

if [ ! -d $BACKUP_DIR ]; then
  echo "Directory di backup non valida"
  exit 1
fi

if [ -z "$LOG_FILE" ]; then
	LOG_FILE=patch_$today.log
fi

. include/functions.sh

touch ${LOG_FILE}

# Converte in path in assoluto per facilitare l'applicazione della patch
LOG_FILE=$(readlink -f ${LOG_FILE})

echo "Applicazione patch" | tee $LOG_FILE
echo "JBOSS USER: $JBOSS_USER" | tee -a $LOG_FILE
echo "JBOSS HOME: $JBOSS_HOME" | tee -a $LOG_FILE
echo "LOG FILE  : $LOG_FILE" | tee -a $LOG_FILE

reply=
while [ "$reply" != "s" ] && [ "$reply" != "n" ] ; do
  echo -n "Procedere con l'applicazione (s/n)? "
  read reply
done

[ "$reply" != "s" ] && exit 1


#
# installazione della patch
#

JBOSS_SERVER_HOME=$JBOSS_HOME/standalone

# operazioni pre installazione
if [ -r ./include/preinstall.sh ] ; then
 echo "Executing pre-install tasks" | tee -a $LOG_FILE
 . ./include/preinstall.sh || return 1
fi

# Moduli applicativi
installDir deploy $JBOSS_SERVER_HOME/deployments

# Modules JBoss 7
installDir modules $JBOSS_HOME/modules

# operazioni post installazione
if [ -r ./include/postinstall.sh ] ; then 
  echo "Executing post install tasks" | tee -a $LOG_FILE
  . ./include/postinstall.sh || return 1
fi

# registrazione dell'applicazione della patch
touch $JBOSS_HOME/patch.log
echo "$prettyToday - $patchVersion" >> $JBOSS_HOME/patch.log
