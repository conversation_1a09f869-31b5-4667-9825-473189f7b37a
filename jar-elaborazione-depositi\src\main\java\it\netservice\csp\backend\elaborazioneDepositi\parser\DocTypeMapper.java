package it.netservice.csp.backend.elaborazioneDepositi.parser;

import java.io.InputStream;
import java.util.NoSuchElementException;

import javax.xml.namespace.QName;
import javax.xml.stream.XMLEventReader;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.events.StartElement;
import javax.xml.stream.events.XMLEvent;

/**
 *
 * <AUTHOR>
 */
public class DocTypeMapper {

    public static String getRootElement(InputStream document) throws XMLStreamException {
        return extractNamespace(document).getLocalPart();
    }

    public static QName extractNamespace(InputStream document) throws XMLStreamException {
        XMLInputFactory xmlInputFactory = XMLInputFactory.newInstance();
        XMLEventReader reader = xmlInputFactory.createXMLEventReader(document);
        while (reader.hasNext()) {
            XMLEvent nextEvent = reader.nextEvent();
            if (nextEvent.isStartElement()) {
                StartElement startElement = nextEvent.asStartElement();
                return startElement.getName();
            }
        }
        throw new NoSuchElementException("Impossibile determinare l'elemento root del deposito");
    }

}
